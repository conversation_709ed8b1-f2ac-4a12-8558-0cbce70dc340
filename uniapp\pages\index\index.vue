<template>
	<view class="container">
		<!-- 顶部搜索图标 -->
		<view class="search-icon">
			<!-- <text class="iconfont icon-search">🔍</text> -->
		</view>
		
		<!-- 顶部大标题 -->
		<!-- <view class="main-title">
			<text class="title-text">AI一键成片</text>
			<text class="subtitle-text">把小说/故事轻松变成视频</text>
		</view> -->
		
		<!-- 轮播图组件，顶部铺满 -->
		<swiper class="banner-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000" circular indicator-color="rgba(255,255,255,0.4)" indicator-active-color="#ffffff" @change="onSwiperChange">
			<swiper-item v-for="(item, index) in banners" :key="index" @click="handleBannerClick(item)">
				<image :src="$getImageUrl(item.image)" class="banner-image" mode="aspectFill"></image>
			</swiper-item>
		</swiper>
		
		<!-- 透明分组栏 - 位于轮播图上方 -->
		<view class="group-nav" :style="navBarStyle">
			<view class="group-nav-border"></view>
			<scroll-view class="group-scroll" scroll-x="true" show-scrollbar="false" :scroll-left="scrollLeft" ref="groupScroll">
				<view class="group-item hot-item" :class="{active: activeTab === 'favorite'}" @click="setActiveTab('favorite', 0)">
					<view class="bubble-bg" v-if="activeTab === 'favorite'"></view>
					<text class="group-name">收藏</text>
				</view>
				<view class="group-item hot-item" :class="{active: activeTab === 'all'}" @click="setActiveTab('all', 1)">
					<view class="bubble-bg" v-if="activeTab === 'all'"></view>
					<text class="group-name">全部</text>
				</view>
				<view 
					class="group-item"
					:class="{active: activeTab === group.id}"
					v-for="(group, index) in agentGroups" 
					:key="group.id"
					@click="handleGroupClick(group, index)"
				>
					<view class="bubble-bg" v-if="activeTab === group.id"></view>
					<text class="group-name">{{group.name}}</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 应用卡片区域 - 使用API获取的智能体数据 -->
		<swiper class="app-swiper" :current="currentSwiperIndex" @change="onAppSwiperChange" :circular="false" :duration="300">
			<!-- 收藏页面 -->
			<swiper-item>
				<scroll-view scroll-y="true" class="app-scroll-view" show-scrollbar="false">
					<view class="fixed-apps" v-if="favoriteApps.length > 0">
						<view class="apps-row" v-for="(row, rowIndex) in chunkedFavoriteApps" :key="rowIndex">
							<view 
								class="app-card" 
								v-for="(app, index) in row" 
								:key="index" 
								:style="{'background-image': `url('${$getImageUrl(app.icon || '')}')`}"
								@click="handleAppClick(app)"
							>
								<view class="app-card-tag">
									<text class="app-tag-name">{{app.title || '智能应用'}}</text>
									<text class="app-desc-text">{{app.description || '智能AI应用'}}</text>
									<text class="app-tag-label" v-if="app.premium">专业版</text>
								</view>
								<view class="favorite-icon" @click.stop="toggleFavorite(app, $event)">
									<text class="iconfont" :class="{'icon-heart-filled': app.isFavorite, 'icon-heart-outline': !app.isFavorite}">{{app.isFavorite ? '♥' : '♡'}}</text>
								</view>
								<view class="app-card-btn">
									<text class="btn-text">{{app.buttonText || '立即使用'}}</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 无收藏数据时提示 -->
					<view class="empty-tip" v-else>
						<text>暂无收藏</text>
					</view>
				</scroll-view>
			</swiper-item>
			
			<!-- 全部应用页面 -->
			<swiper-item>
				<scroll-view scroll-y="true" class="app-scroll-view" show-scrollbar="false">
					<view class="fixed-apps" v-if="allApps.length > 0">
						<view class="apps-row" v-for="(row, rowIndex) in chunkedAllApps" :key="rowIndex">
							<view 
								class="app-card" 
								v-for="(app, index) in row" 
								:key="index" 
								:style="{'background-image': `url('${$getImageUrl(app.icon || '')}')`}"
								@click="handleAppClick(app)"
							>
								<view class="app-card-tag">
									<text class="app-tag-name">{{app.title || '智能应用'}}</text>
									<text class="app-desc-text">{{app.description || '智能AI应用'}}</text>
									<text class="app-tag-label" v-if="app.premium">专业版</text>
								</view>
								<view class="favorite-icon" @click.stop="toggleFavorite(app, $event)">
									<text class="iconfont" :class="{'icon-heart-filled': app.isFavorite, 'icon-heart-outline': !app.isFavorite}">{{app.isFavorite ? '♥' : '♡'}}</text>
								</view>
								<view class="app-card-btn">
									<text class="btn-text">{{app.buttonText || '立即使用'}}</text>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 无数据时提示 -->
					<view class="empty-tip" v-else>
						<text>暂无更多智能体</text>
					</view>
				</scroll-view>
			</swiper-item>
			
			<!-- 分组应用 -->
			<swiper-item v-for="(group, index) in agentGroups" :key="group.id">
				<scroll-view scroll-y="true" class="app-scroll-view" show-scrollbar="false">
					<view class="fixed-apps">
						<view class="apps-row" v-for="(row, rowIndex) in groupApps[group.id] ? chunkedGroupApps(group.id) : []" :key="rowIndex">
							<view 
								class="app-card" 
								v-for="(app, appIndex) in row" 
								:key="appIndex" 
								:style="{'background-image': `url('${$getImageUrl(app.icon || '')}')`}"
								@click="handleAppClick(app)"
							>
								<view class="app-card-tag">
									<text class="app-tag-name">{{app.title || '智能应用'}}</text>
									<text class="app-desc-text">{{app.description || '智能AI应用'}}</text>
									<text class="app-tag-label" v-if="app.premium">专业版</text>
								</view>
								<view class="favorite-icon" @click.stop="toggleFavorite(app, $event)">
									<text class="iconfont" :class="{'icon-heart-filled': app.isFavorite, 'icon-heart-outline': !app.isFavorite}">{{app.isFavorite ? '♥' : '♡'}}</text>
								</view>
								<view class="app-card-btn">
									<text class="btn-text">{{app.buttonText || '立即使用'}}</text>
								</view>
							</view>
						</view>
						<!-- 无数据时的提示 -->
						<view class="empty-tip" v-if="!groupApps[group.id] || groupApps[group.id].length === 0">
							<text>暂无更多智能体</text>
						</view>
					</view>
				</scroll-view>
			</swiper-item>
		</swiper>
		
		<!-- 加载状态 -->
		<view class="loading" v-if="loading">
			<text>{{loadingText}}</text>
		</view>



		<!-- 魔法导航栏 -->
		<MagicNavigation
			:current="currentNavIndex"
			:items="navItems"
			@change="onNavChange"
		/>

		<!-- 智能登录弹窗 -->
		<smart-login-modal
			:visible="showLoginModalFlag"
			@close="hideLoginModal"
			@login-success="handleLoginSuccess"
		></smart-login-modal>
	</view>
</template>

<script>
	import MagicNavigation from '@/components/MagicNavigation.vue'
	import SmartLoginModal from '@/components/smart-login-modal.vue'
	import { userStore } from '@/api/members.js'

	export default {
		components: {
			MagicNavigation,
			SmartLoginModal
		},
		data() {
			return {
				banners: [], // 轮播图数据
				agentGroups: [], // 智能体分组数据
				themeApps: [], // 智能体主题应用数据
				activeTab: 'all', // 当前选中的标签，默认为"全部"
				loading: false, // 加载状态
				// 按钮文字映射
				buttonTextMap: {
					'story': '生成视频',
					'draw': '开始创作',
					'voice': '立即使用',
					'image-video': '生成视频'
				},
				// 魔法导航栏相关
				currentNavIndex: 0, // 当前导航索引
				navItems: [
					{ text: '初页', icon: 'icon-home', path: '/pages/index/index' },
					{ text: '会享', icon: 'icon-message', path: '/pages/chat/index' },
					{ text: '往档', icon: 'icon-history', path: '/pages/history/index' },
					{ text: '吾界', icon: 'icon-user', path: '/pages/profile/index' }
				],
				// 新增：导航栏样式
				navBarStyle: {
					background: 'rgba(255, 255, 255, 0.15)',
				},
				scrollLeft: 0,
				currentSwiperIndex: 1, // 默认显示全部页面
				groupApps: {},
				favoriteApps: [],
				allApps: [], // 新增：所有应用列表
				loadingText: '', // 显示加载状态的文本
				// 登录相关状态
				showLoginModalFlag: false, // 控制登录弹窗显示
				isLoggedIn: false // 用户登录状态
			}
		},
		computed: {
			// 将收藏应用数组拆分为每行两个的二维数组
			chunkedFavoriteApps() {
				const result = [];
				for (let i = 0; i < this.favoriteApps.length; i += 2) {
					result.push(this.favoriteApps.slice(i, i + 2));
				}
				return result;
			},
			// 将所有应用数组拆分为每行两个的二维数组
			chunkedAllApps() {
				const result = [];
				for (let i = 0; i < this.allApps.length; i += 2) {
					result.push(this.allApps.slice(i, i + 2));
				}
				return result;
			},
			chunkedGroupApps() {
				return (groupId) => {
					if (!this.groupApps[groupId] || !Array.isArray(this.groupApps[groupId])) {
						return [];
					}
					
					const result = [];
					const apps = this.groupApps[groupId];
					for (let i = 0; i < apps.length; i += 2) {
						result.push(apps.slice(i, i + 2));
					}
					return result;
				}
			}
		},
		onLoad() {
			// 检查登录状态
			this.checkLoginStatus();
			// 页面加载时获取数据
			this.loadFavorites(); // 先加载收藏数据
			this.getBanners();
			this.getAgentGroups();
			this.getAllAgentThemes(); // 获取所有智能体主题
		},
		onShow() {
			// 页面显示时重新检查登录状态
			this.checkLoginStatus();
		},
		methods: {
			// 导航栏变化处理
			onNavChange(event) {
				console.log('导航栏切换:', event)
				this.currentNavIndex = event.index

				// 可以在这里添加页面切换的额外逻辑
				if (event.item.path) {
					// 路径跳转已在组件内部处理
					console.log('跳转到:', event.item.path)
				}
			},

			// 获取图标文字（取首字）
			getIconText(name) {
				return name ? name.charAt(0) : 'A';
			},
			
			// 确保隐藏加载状态
			hideLoading() {
				this.loading = false;
				this.loadingText = '';
				uni.hideLoading();
			},
			
			// 设置当前选中的标签
			setActiveTab(tabId, index) {
				// 添加动画类
				this.activeTab = tabId;
				console.log('切换到标签:', tabId);
				
				// 根据选中的标签更新当前显示的内容
				if (tabId === 'favorite') {
					this.currentSwiperIndex = 0;
				} else if (tabId === 'all') {
					this.currentSwiperIndex = 1;
				}
				
				// 滚动到选中的分组
				this.$nextTick(() => {
					// 获取元素
					const groupItems = document.querySelectorAll('.group-item');
					if (groupItems && groupItems.length > 0) {
						let el = null;
						if (index === undefined) {
							// 找到当前激活的元素
							el = document.querySelector('.group-item.active');
						} else {
							el = groupItems[index];
						}
						
						if (el) {
							// 计算滚动位置
							const parentScrollView = el.parentNode;
							const scrollViewWidth = parentScrollView.offsetWidth;
							const elLeft = el.offsetLeft;
							const elWidth = el.offsetWidth;
							
							// 居中显示，除非是第一个或最后一个
							if (index === 0 || el === groupItems[0]) {
								this.scrollLeft = 0;
							} else if (index === groupItems.length - 1 || el === groupItems[groupItems.length - 1]) {
								this.scrollLeft = parentScrollView.scrollWidth - scrollViewWidth;
							} else {
								this.scrollLeft = elLeft - (scrollViewWidth / 2) + (elWidth / 2);
							}
						}
					}
				});
			},
			
			// 获取轮播图列表
			getBanners() {
				this.showLoading('加载轮播图');
				
				this.$api({
					url: '/api/banners',
					data: {
						enabled: true // 只获取已启用的轮播图
					}
				}).then(res => {
					if (res && res.code === 200) {
						// 处理数据，确保list字段存在
						const list = res.data.list || [];
						// 按order字段排序，如果支持的话
						this.banners = Array.isArray(list) ? list.sort((a, b) => (a.order || 0) - (b.order || 0)) : [];
						console.log('轮播图数据加载成功', this.banners);
					} else {
						uni.showToast({
							title: '获取轮播图失败',
							icon: 'none'
						});
						console.error('获取轮播图失败', res);
					}
				}).catch(err => {
					console.error('轮播图请求异常', err);
				}).finally(() => {
					this.hideLoading();
				});
			},
			
			// 获取分组数据
			getAgentGroups() {
				this.showLoading('加载分组');
				
				this.$api({
					url: '/api/agent-groups',
					data: {
						enabled: true // 只获取已启用的分组
					}
				}).then(res => {
					if (res && res.code === 200) {
						// 处理数据，确保使用正确的数据结构
						let groups = [];
						if (res.data && res.data.list && Array.isArray(res.data.list)) {
							groups = res.data.list;
						} else if (Array.isArray(res.data)) {
							groups = res.data;
						}
						
						// 按order字段排序
						this.agentGroups = groups.sort((a, b) => (a.order || 0) - (b.order || 0));
						console.log('分组数据加载成功', this.agentGroups);
						
						// 预加载所有分组的应用数据
						this.agentGroups.forEach(group => {
							this.getGroupThemes(group.id);
						});
					} else {
						console.error('获取分组失败', res);
					}
				}).catch(err => {
					console.error('分组请求异常', err);
				}).finally(() => {
					this.hideLoading();
				});
			},
			
			// 切换收藏状态
			toggleFavorite(app, e) {
				// 防止事件冒泡
				if (e) {
					e.stopPropagation();
				}
				
				// 立即切换收藏状态，提高响应性
				const newFavoriteState = !app.isFavorite;
				this.$set(app, 'isFavorite', newFavoriteState);
				
				// 显示简短提示
				uni.showToast({
					title: newFavoriteState ? '已收藏' : '已取消',
					icon: 'none',
					duration: 1000
				});
				
				// 同步更新收藏列表
				if (newFavoriteState) {
					// 添加到收藏列表（检查是否已存在）
					const exists = this.favoriteApps.some(item => item.id === app.id);
					if (!exists) {
						const appCopy = JSON.parse(JSON.stringify(app));
						this.favoriteApps.push(appCopy);
					}
				} else {
					// 从收藏列表中移除
					const index = this.favoriteApps.findIndex(item => item.id === app.id);
					if (index !== -1) {
						this.favoriteApps.splice(index, 1);
					}
				}
				
				// 同步更新全部应用列表
				const allAppIndex = this.allApps.findIndex(item => item.id === app.id);
				if (allAppIndex !== -1) {
					this.$set(this.allApps[allAppIndex], 'isFavorite', newFavoriteState);
				}
				
				// 同步更新分组应用列表
				Object.keys(this.groupApps).forEach(groupId => {
					const groupAppIndex = this.groupApps[groupId].findIndex(item => item.id === app.id);
					if (groupAppIndex !== -1) {
						this.$set(this.groupApps[groupId][groupAppIndex], 'isFavorite', newFavoriteState);
					}
				});
				
				// 保存收藏数据到本地
				this.saveFavorites();
			},
			
			// 保存收藏到本地存储
			saveFavorites() {
				try {
					// 确保只存储必要的数据，避免循环引用问题
					const favoritesToSave = this.favoriteApps.map(app => ({
						id: app.id,
						title: app.title,
						description: app.description,
						icon: app.icon,
						type: app.type,
						premium: app.premium,
						buttonText: app.buttonText,
						isFavorite: true
					}));
					uni.setStorageSync('favoriteApps', JSON.stringify(favoritesToSave));
				} catch (e) {
					console.error('保存收藏失败', e);
				}
			},
			
			// 加载本地收藏
			loadFavorites() {
				try {
					const favStr = uni.getStorageSync('favoriteApps');
					if (favStr) {
						this.favoriteApps = JSON.parse(favStr);
						
						// 确保所有收藏项的isFavorite属性为true
						this.favoriteApps.forEach(app => {
							this.$set(app, 'isFavorite', true);
						});
						
						console.log('成功加载收藏数据:', this.favoriteApps.length, '个项目');
					}
				} catch (e) {
					console.error('加载收藏失败', e);
					this.favoriteApps = [];
				}
			},
			
			// 获取所有智能体主题应用数据
			getAllAgentThemes() {
				this.showLoading('加载应用');
				
				// 确保最多加载10秒
				const loadingTimeout = setTimeout(() => {
					this.hideLoading();
				}, 10000);
				
				this.$api({
					url: '/api/agent-themes',
					data: { enabled: true }
				}).then(res => {
					if (res) {
						try {
							// 处理返回数据
							let themes = [];
							if (Array.isArray(res)) {
								themes = res;
							} else if (res.data && Array.isArray(res.data)) {
								themes = res.data;
							} else if (res.data && res.data.list && Array.isArray(res.data.list)) {
								themes = res.data.list;
							}
							
							// 确保title和description字段正确显示
							themes = themes.map((theme, index) => {
								// 解码可能的中文字段
								const title = theme.title || '未命名应用';
								const description = theme.description || '暂无描述';
								const icon = theme.icon || '';

								// 判断应用是否为工作流类型
								let isWorkflow = false;
								if (
									theme.workflowId ||
									theme.type === 'workflow' ||
									theme.type === '工作流' ||
									(title && (title.includes('工作流') || title.includes('流程'))) ||
									(description && (description.includes('工作流') || description.includes('流程'))) ||
									(theme.tags && Array.isArray(theme.tags) && theme.tags.includes('workflow'))
								) {
									isWorkflow = true;
								}

								// 判断应用是否为智能体类型
								let isAgent = false;
								// 如果主题名称或描述中包含智能体、AI助手、机器人等关键词，则判定为智能体类型
								if (
									!isWorkflow && ( // 不是工作流的情况下才判断为智能体
										(title && (title.includes('智能体') || title.includes('AI助手') || title.includes('机器人'))) ||
										(description && (description.includes('智能体') || description.includes('AI助手') || description.includes('机器人'))) ||
										theme.type === 'agent' ||
										theme.type === '智能体' ||
										(theme.tags && Array.isArray(theme.tags) && theme.tags.includes('agent')) ||
										theme.agentId // 有agentId的也认为是智能体
									)
								) {
									isAgent = true;
								}

								// 根据类型设置不同的按钮文本
								let buttonText = '立即使用';
								if (isWorkflow) {
									buttonText = '执行工作流';
								} else if (isAgent) {
									buttonText = '开始对话';
								} else if (index % 4 === 0) {
									buttonText = '生成视频';
								} else if (index % 4 === 1) {
									buttonText = '开始创作';
								} else if (index % 4 === 2) {
									buttonText = '立即使用';
								} else if (index % 4 === 3) {
									buttonText = '生成视频';
								}

								// 检查是否为收藏项
								const isFavorite = this.favoriteApps.some(item => item.id === theme.id);

								return {
									...theme,
									title: title,
									description: description,
									icon: icon,
									buttonText: buttonText,
									isFavorite: isFavorite,
									isAgent: isAgent,
									isWorkflow: isWorkflow
								};
							});
							
							// 使用Vue的响应式方法更新数组
							this.allApps = themes;
							
							// 同步更新收藏列表中的内容，确保数据一致性
							this.syncFavoritesWithAllApps();
						} catch (error) {
							console.error('处理数据时出错:', error);
							this.allApps = [];
						}
					} else {
						console.error('获取智能体主题失败', res);
						this.allApps = [];
					}
				}).catch(err => {
					console.error('智能体主题请求异常', err);
					this.allApps = [];
				}).finally(() => {
					clearTimeout(loadingTimeout);
					this.hideLoading();
				});
			},
			
			// 同步收藏列表和全部应用列表
			syncFavoritesWithAllApps() {
				// 从收藏列表中删除已不存在的应用
				const updatedFavorites = this.favoriteApps.filter(favApp => {
					return this.allApps.some(app => app.id === favApp.id);
				});
				
				// 更新收藏列表中应用的最新信息
				this.favoriteApps = updatedFavorites.map(favApp => {
					// 在全部应用中查找对应应用的最新信息
					const latestAppInfo = this.allApps.find(app => app.id === favApp.id);
					if (latestAppInfo) {
						// 创建新对象以确保Vue可以检测到变化
						return {
							...latestAppInfo,
							isFavorite: true
						};
					}
					return {
						...favApp,
						isFavorite: true
					};
				});
				
				// 同时确保全部应用列表中的收藏状态正确
				this.allApps.forEach((app, index) => {
					const isFavorite = this.favoriteApps.some(favApp => favApp.id === app.id);
					// 使用Vue的响应式方法设置isFavorite
					this.$set(this.allApps[index], 'isFavorite', isFavorite);
				});
				
				// 保存更新后的收藏列表
				this.saveFavorites();
			},
			
			// 根据应用类型获取按钮文本
			getButtonTextByType(type) {
				return this.buttonTextMap[type] || '立即使用';
			},
			
			// 处理分组点击
			handleGroupClick(group, index) {
				this.activeTab = group.id;
				console.log('选择分组', group);
				
				// 更新轮播图索引 (加2是因为前面有收藏和全部两个tab)
				this.currentSwiperIndex = index + 2;
				
				// 触发动画并滚动到居中位置
				this.setActiveTab(group.id, index + 2);
				
				// 根据分组ID获取对应的智能体主题
				if (!this.groupApps[group.id]) {
					this.getGroupThemes(group.id);
				}
			},
			
			// 获取特定分组的智能体主题
			getGroupThemes(groupId) {
				this.showLoading('加载分组应用');
				
				// 确保最多加载10秒
				const loadingTimeout = setTimeout(() => {
					this.hideLoading();
				}, 10000);
				
				this.$api({
					url: '/api/agent-themes',
					data: { 
						enabled: true
					}
				}).then(res => {
					if (res) {
						try {
							// 处理返回数据
							let themes = [];
							if (Array.isArray(res)) {
								themes = res;
							} else if (res.data && Array.isArray(res.data)) {
								themes = res.data;
							} else if (res.data && res.data.list && Array.isArray(res.data.list)) {
								themes = res.data.list;
							}
							
							// 确保title和description字段正确显示
							themes = themes.map((theme, index) => {
								// 解码可能的中文字段
								const title = theme.title || '未命名应用';
								const description = theme.description || '暂无描述';
								const icon = theme.icon || '';

								// 判断应用是否为工作流类型
								let isWorkflow = false;
								if (
									theme.workflowId ||
									theme.type === 'workflow' ||
									theme.type === '工作流' ||
									(title && (title.includes('工作流') || title.includes('流程'))) ||
									(description && (description.includes('工作流') || description.includes('流程'))) ||
									(theme.tags && Array.isArray(theme.tags) && theme.tags.includes('workflow'))
								) {
									isWorkflow = true;
								}

								// 判断应用是否为智能体类型
								let isAgent = false;
								// 如果主题名称或描述中包含智能体、AI助手、机器人等关键词，则判定为智能体类型
								if (
									!isWorkflow && ( // 不是工作流的情况下才判断为智能体
										(title && (title.includes('智能体') || title.includes('AI助手') || title.includes('机器人'))) ||
										(description && (description.includes('智能体') || description.includes('AI助手') || description.includes('机器人'))) ||
										theme.type === 'agent' ||
										theme.type === '智能体' ||
										(theme.tags && Array.isArray(theme.tags) && theme.tags.includes('agent')) ||
										theme.agentId // 有agentId的也认为是智能体
									)
								) {
									isAgent = true;
								}

								// 根据类型设置不同的按钮文本
								let buttonText = '立即使用';
								if (isWorkflow) {
									buttonText = '执行工作流';
								} else if (isAgent) {
									buttonText = '开始对话';
								} else if (index % 4 === 0) {
									buttonText = '生成视频';
								} else if (index % 4 === 1) {
									buttonText = '开始创作';
								} else if (index % 4 === 2) {
									buttonText = '立即使用';
								} else if (index % 4 === 3) {
									buttonText = '生成视频';
								}

								// 检查是否为收藏项
								const isFavorite = this.favoriteApps.some(item => item.id === theme.id);

								return {
									...theme,
									title: title,
									description: description,
									icon: icon,
									buttonText: buttonText,
									isFavorite: isFavorite,
									isAgent: isAgent,
									isWorkflow: isWorkflow
								};
							});
							
							// 获取当前分组
							const currentGroup = this.agentGroups.find(g => g.id === groupId);
							const currentGroupName = currentGroup ? currentGroup.name : '';
							
							// 根据标签tags字段过滤智能体主题
							const filteredThemes = themes.filter(theme => {
								// 主要通过tags字段匹配
								if (Array.isArray(theme.tags) && theme.tags.includes(currentGroupName)) {
									return true;
								}
								
								// 确保我们不遗漏可能使用的其他关联方式
								if (theme.groupName === currentGroupName) {
									return true;
								}
								
								return false;
							});
							
							// 使用Vue的响应式方法更新对象属性
							this.$set(this.groupApps, groupId, filteredThemes);
						} catch (error) {
							console.error('处理数据时出错:', error);
							// 如果处理失败，设置为空数组
							this.$set(this.groupApps, groupId, []);
						}
					} else {
						console.error('获取智能体主题失败', res);
						this.$set(this.groupApps, groupId, []);
					}
				}).catch(err => {
					console.error('智能体主题请求异常', err);
					this.$set(this.groupApps, groupId, []);
				}).finally(() => {
					clearTimeout(loadingTimeout);
					this.hideLoading();
				});
			},
			
			// 处理应用点击
			handleAppClick(app) {
				console.log('点击应用', app);

				// 检查登录状态，未登录则弹出登录弹窗
				if (!this.isLoggedIn) {
					console.log('用户未登录，显示登录弹窗');
					this.showLoginModal();
					return;
				}

				const appTitle = app.title || (app.type ? this.buttonTextMap[app.type] : '智能应用');

				uni.showToast({
					title: `为您打开: ${appTitle}`,
					icon: 'none'
				});

				// 优先检查应用对象中的标识
				const isWorkflow = app.isWorkflow || app.workflowId || app.type === 'workflow' || app.type === '工作流' ||
								  (app.tags && Array.isArray(app.tags) && app.tags.includes('workflow'));

				const isAgent = app.isAgent || app.agentId || app.type === 'agent' || app.type === '智能体' ||
							   (app.tags && Array.isArray(app.tags) && app.tags.includes('agent'));

				// 如果是工作流类型，跳转到gzl.vue页面
				if (isWorkflow) {
					uni.navigateTo({
						url: `/pages/index/gzl?id=${app.id}`,
						success: () => {
							console.log('跳转到工作流详情页，ID:', app.id);
						},
						fail: (err) => {
							console.error('跳转失败', err);
						}
					});
					return;
				}

				// 如果是智能体类型，跳转到znt.vue页面
				if (isAgent) {
					uni.navigateTo({
						url: `/pages/index/znt?id=${app.id}`,
						success: () => {
							console.log('跳转到智能体详情页，ID:', app.id);
						},
						fail: (err) => {
							console.error('跳转失败', err);
						}
					});
					return;
				}

				// 根据应用类型处理不同的跳转逻辑
				const appType = app.type;
				if (appType) {
					switch(appType) {
						case 'story':
							// 跳转到AI故事视频页面
							// uni.navigateTo({ url: '/pages/story/index' });
							break;
						case 'draw':
							// 跳转到绘图与视频页面
							// uni.navigateTo({ url: '/pages/draw/index' });
							break;
						case 'voice':
							// 跳转到口播视频页面
							// uni.navigateTo({ url: '/pages/voice/index' });
							break;
						case 'image-video':
							// 跳转到图生视频页面
							// uni.navigateTo({ url: '/pages/image-video/index' });
							break;
						case 'agent':
							// 跳转到智能体详情页面
							uni.navigateTo({ url: `/pages/index/znt?id=${app.id}` });
							break;
						case 'workflow':
							// 跳转到工作流详情页面
							uni.navigateTo({ url: `/pages/index/gzl?id=${app.id}` });
							break;
					}
				}
			},
			
			// 处理轮播图点击
			handleBannerClick(banner) {
				if (banner.format === 'link' && banner.link) {
					// 处理链接类型
					uni.navigateTo({
						url: banner.link,
						fail: () => {
							// 如果无法在应用内打开，尝试使用系统浏览器打开
							if (banner.link.startsWith('http')) {
								// #ifdef H5
								window.open(banner.link);
								// #endif
								
								// #ifdef APP-PLUS
								plus.runtime.openURL(banner.link);
								// #endif
								
								// #ifdef MP
								uni.showToast({
									title: '无法打开外部链接',
									icon: 'none'
								});
								// #endif
							}
						}
					});
				} else if (banner.format === 'qrcode' && banner.qrcode) {
					// 处理二维码类型，显示二维码大图
					uni.previewImage({
						urls: [this.$getImageUrl(banner.image)],
						current: this.$getImageUrl(banner.image)
					});
				}
			},
			
			// 新增：轮播图切换事件
			onSwiperChange(e) {
				const current = e.detail.current;
				if (this.banners[current]) {
					// 根据轮播图的主色调调整导航栏背景，使用半透明效果
					this.navBarStyle.background = `rgba(120, 120, 120, 0.2)`;
					// 这里可以添加更复杂的逻辑，从轮播图提取主色调
				}
			},
			
			// 新增：应用卡片切换事件
			onAppSwiperChange(e) {
				const current = e.detail.current;
				this.currentSwiperIndex = current;
				
				// 同步切换顶部分组
				if (current === 0) {
					// 收藏分组
					this.setActiveTab('favorite', 0);
				} else if (current === 1) {
					// 全部分组
					this.setActiveTab('all', 1);
				} else {
					// 其他分组
					const groupIndex = current - 2; // 减2是因为前面有收藏和全部两个tab
					if (this.agentGroups[groupIndex]) {
						this.setActiveTab(this.agentGroups[groupIndex].id, groupIndex + 2);
						
						// 如果该分组还没有加载数据，则加载
						const groupId = this.agentGroups[groupIndex].id;
						if (!this.groupApps[groupId]) {
							this.getGroupThemes(groupId);
						}
					}
				}
			},
			
			// 显示加载状态
			showLoading(text = '加载中') {
				this.loadingText = text;
				this.loading = true;
			},

			// 隐藏加载状态
			hideLoading() {
				this.loading = false;
				this.loadingText = '';
			},

			// 检查登录状态
			checkLoginStatus() {
				this.isLoggedIn = userStore.isLoggedIn();
				console.log('当前登录状态:', this.isLoggedIn);
			},

			// 显示登录弹窗
			showLoginModal() {
				this.showLoginModalFlag = true;
			},

			// 隐藏登录弹窗
			hideLoginModal() {
				this.showLoginModalFlag = false;
			},

			// 处理登录成功
			handleLoginSuccess(memberData) {
				console.log('登录成功:', memberData);
				// 刷新登录状态
				this.checkLoginStatus();
				// 隐藏登录弹窗
				this.hideLoginModal();
				// 显示成功提示
				uni.showToast({
					title: '登录成功',
					icon: 'success'
				});
			}
		}
	}
</script>

<style>
	/* 全局隐藏滚动条 */
	* {
		scrollbar-width: none !important; /* Firefox */
		-ms-overflow-style: none !important; /* IE 10+ */
	}

	*::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
	}

	/* 全局容器，大气深色主题 */
	.container {
		width: 100%;
		min-height: 100vh;
		padding: 0 0 200rpx 0; /* 底部留出导航栏空间 */
		margin: 0;
		display: flex;
		flex-direction: column;
		background: linear-gradient(135deg,
			#0c0c0c 0%,     /* 深黑色 */
			#1a1a2e 20%,    /* 深蓝黑 */
			#16213e 40%,    /* 深蓝 */
			#0f3460 60%,    /* 中蓝 */
			#533483 80%,    /* 深紫 */
			#e43c5c 100%    /* 深红 */
		);
		background-size: 400% 400%;
		animation: gradientShift 20s ease infinite;
		overflow-x: hidden;
		overflow-y: auto;
		position: relative;
	}

	/* 添加星空效果 */
	.container::before {
		content: '';
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-image:
			radial-gradient(2px 2px at 20px 30px, #eee, transparent),
			radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
			radial-gradient(1px 1px at 90px 40px, #fff, transparent),
			radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
			radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
		background-repeat: repeat;
		background-size: 200px 100px;
		animation: sparkle 50s linear infinite;
		pointer-events: none;
		z-index: 1;
	}

	/* 星空闪烁动画 */
	@keyframes sparkle {
		from {
			transform: translateX(0);
		}
		to {
			transform: translateX(200px);
		}
	}

	/* 背景渐变动画 */
	@keyframes gradientShift {
		0% {
			background-position: 0% 50%;
		}
		50% {
			background-position: 100% 50%;
		}
		100% {
			background-position: 0% 50%;
		}
	}
	
	/* 顶部搜索图标 */
	.search-icon {
		position: absolute;
		top: 40rpx;
		left: 40rpx;
		z-index: 10;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.icon-search {
		font-size: 40rpx;
		color: #ffffff;
	}
	
	/* 顶部大标题 */
	.main-title {
		position: absolute;
		top: 120rpx;
		left: 40rpx;
		z-index: 10;
		display: flex;
		flex-direction: column;
	}
	
	.title-text {
		font-size: 60rpx;
		font-weight: bold;
		color: #ffcc00;
		text-shadow: 0 2px 4px rgba(0,0,0,0.3);
		margin-bottom: 10rpx;
	}
	
	.subtitle-text {
		font-size: 28rpx;
		color: #ffffff;
		text-shadow: 0 1px 2px rgba(0,0,0,0.3);
	}
	
	/* 轮播图样式，顶部和两边铺满，底部圆角 */
	.banner-swiper {
		width: 100%;
		height: 400rpx;
		margin: 0;
		padding: 0;
		background: linear-gradient(135deg,
			#1a1a2e 0%,
			#16213e 25%,
			#0f3460 50%,
			#533483 75%,
			#e43c5c 100%
		);
		position: relative;
		border-radius: 0 0 30rpx 30rpx;
		overflow: hidden;
		box-shadow:
			0 20rpx 40rpx rgba(0, 0, 0, 0.3),
			0 10rpx 20rpx rgba(0, 0, 0, 0.2),
			inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
	}
	
	/* 轮播图内容样式 */
	.banner-swiper .uni-swiper {
		overflow: hidden;
		border-radius: 0 0 30rpx 30rpx;
	}
	
	/* 轮播图四角轮廓增强 */
	.banner-swiper::before {
		content: "";
		position: absolute;
		bottom: 0;
		left: 20rpx;
		right: 20rpx;
		height: 2px;
		background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
		z-index: 2;
		border-radius: 0 0 30rpx 30rpx;
	}
	
	.banner-swiper::after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 0 0 30rpx 30rpx;
		box-shadow: 
			inset 0 -3rpx 8rpx rgba(255, 255, 255, 0.4),
			inset 3rpx 0 8rpx rgba(255, 255, 255, 0.3),
			inset -3rpx 0 8rpx rgba(255, 255, 255, 0.3);
		pointer-events: none;
		z-index: 2;
	}
	
	.banner-image {
		width: 100%;
		height: 100%;
		opacity: 0.9;
		border-radius: 0 0 30rpx 30rpx;
	}
	
	/* 分组栏样式 - 深色主题玻璃效果 */
	.group-nav {
		width: calc(100% - 30rpx); /* 与应用列表对齐 */
		height: 88rpx;
		background: linear-gradient(135deg,
			#ff6b6b 0%, #ff8e53 1%, #ff6348 2%, #ff9ff3 3%, #f368e0 4%, #ff3838 5%,
			#ff4757 6%, #ff6348 7%, #ff7675 8%, #fd79a8 9%, #fdcb6e 10%, #e17055 11%,
			#00b894 12%, #00cec9 13%, #55a3ff 14%, #74b9ff 15%, #0984e3 16%, #6c5ce7 17%,
			#a29bfe 18%, #fd79a8 19%, #fdcb6e 20%, #e17055 21%, #00b894 22%, #00cec9 23%,
			#55a3ff 24%, #74b9ff 25%, #0984e3 26%, #6c5ce7 27%, #a29bfe 28%, #ffeaa7 29%,
			#fab1a0 30%, #ff7675 31%, #fd79a8 32%, #e84393 33%, #00b894 34%, #00cec9 35%,
			#74b9ff 36%, #0984e3 37%, #6c5ce7 38%, #a29bfe 39%, #ffeaa7 40%, #fab1a0 41%,
			#ff7675 42%, #fd79a8 43%, #e84393 44%, #00b894 45%, #00cec9 46%, #74b9ff 47%,
			#0984e3 48%, #6c5ce7 49%, #a29bfe 50%, #ffeaa7 51%, #fab1a0 52%, #ff7675 53%,
			#fd79a8 54%, #e84393 55%, #00b894 56%, #00cec9 57%, #74b9ff 58%, #0984e3 59%,
			#6c5ce7 60%, #a29bfe 61%, #ffeaa7 62%, #fab1a0 63%, #ff7675 64%, #fd79a8 65%,
			#e84393 66%, #00b894 67%, #00cec9 68%, #74b9ff 69%, #0984e3 70%, #6c5ce7 71%,
			#a29bfe 72%, #ffeaa7 73%, #fab1a0 74%, #ff7675 75%, #fd79a8 76%, #e84393 77%,
			#00b894 78%, #00cec9 79%, #74b9ff 80%, #0984e3 81%, #6c5ce7 82%, #a29bfe 83%,
			#ffeaa7 84%, #fab1a0 85%, #ff7675 86%, #fd79a8 87%, #e84393 88%, #00b894 89%,
			#00cec9 90%, #74b9ff 91%, #0984e3 92%, #6c5ce7 93%, #a29bfe 94%, #ffeaa7 95%,
			#fab1a0 96%, #ff7675 97%, #fd79a8 98%, #e84393 99%, #ff6b6b 100%
		);
		background-size: 400% 400%;
		backdrop-filter: blur(30rpx);
		position: absolute;
		top: calc(400rpx + 15rpx); /* 调整到轮播图和应用列表的中间位置 */
		left: 15rpx; /* 与应用列表左边距对齐 */
		z-index: 20;
		border-radius: 44rpx;
		overflow: visible;
		transition: all 0.3s ease;
		border: 1px solid rgba(255, 255, 255, 0.3);
		box-shadow:
			0 20rpx 40rpx rgba(0, 0, 0, 0.3),
			0 10rpx 20rpx rgba(0, 0, 0, 0.2),
			0 5rpx 10rpx rgba(0, 0, 0, 0.1),
			inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
		animation: floatUpDown 6s ease-in-out infinite, gradientShift 15s ease-in-out infinite;
	}

	/* 分组栏悬浮动画 */
	@keyframes floatUpDown {
		0%, 100% {
			transform: translateY(0rpx);
			box-shadow:
				0 15rpx 35rpx rgba(0, 0, 0, 0.2),
				0 5rpx 15rpx rgba(0, 0, 0, 0.1),
				inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
		}
		50% {
			transform: translateY(-5rpx);
			box-shadow:
				0 20rpx 40rpx rgba(0, 0, 0, 0.25),
				0 8rpx 20rpx rgba(0, 0, 0, 0.15),
				inset 0 1rpx 0 rgba(255, 255, 255, 0.5);
		}
	}

	/* 多色渐变动画 */
	@keyframes gradientShift {
		0% {
			background-position: 0% 50%;
		}
		25% {
			background-position: 100% 50%;
		}
		50% {
			background-position: 100% 100%;
		}
		75% {
			background-position: 0% 100%;
		}
		100% {
			background-position: 0% 50%;
		}
	}

	/* 边框效果 */
	.group-nav-border {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 44rpx;
		border: 1px solid rgba(255, 255, 255, 0.15);
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
		pointer-events: none;
		overflow: hidden; /* 确保内部阴影不溢出 */
	}
	
	/* 添加底部轮廓线 */
	.group-nav-border::after {
		content: "";
		position: absolute;
		bottom: -1px;
		left: 15rpx;
		right: 15rpx;
		height: 2px;
		background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
		border-radius: 0 0 44rpx 44rpx;
	}
	
	/* 添加左右轮廓线 - 向内卷曲效果 */
	.group-nav::before {
		content: "";
		position: absolute;
		top: 6rpx;
		bottom: 6rpx;
		left: 0;
		width: 18rpx;
		background: radial-gradient(ellipse at left, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.3) 50%, transparent 85%);
		border-radius: 44rpx 0 0 44rpx;
		z-index: 1;
	}
	
	.group-nav::after {
		content: "";
		position: absolute;
		top: 6rpx;
		bottom: 6rpx;
		right: 0;
		width: 18rpx;
		background: radial-gradient(ellipse at right, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.3) 50%, transparent 85%);
		border-radius: 0 44rpx 44rpx 0;
		z-index: 1;
	}
	
	/* 添加额外的光泽层，增强卷曲效果 */
	.group-nav-border::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: 
			radial-gradient(ellipse at left, rgba(255, 255, 255, 0.6) 0%, transparent 30%) left center/20rpx 100% no-repeat,
			radial-gradient(ellipse at right, rgba(255, 255, 255, 0.6) 0%, transparent 30%) right center/20rpx 100% no-repeat;
		box-shadow: inset 0 0 15rpx rgba(255, 255, 255, 0.2);
		border-radius: 44rpx;
		pointer-events: none;
	}
	
	.group-scroll {
		width: 100%;
		height: 88rpx;  /* 与group-nav高度匹配 */
		white-space: nowrap;
		box-sizing: border-box;
		/* 完全隐藏滚动条 - 增强版 */
		scrollbar-width: none !important; /* Firefox */
		-ms-overflow-style: none !important; /* IE and Edge */
		overflow: -moz-scrollbars-none !important; /* Firefox */
		overflow-x: scroll !important;
		overflow-y: hidden !important;
		scroll-behavior: smooth; /* 添加平滑滚动 */
		/* 增强隐藏滚动条 */
		scrollbar-color: #f5f5f5 #f5f5f5 !important; /* Firefox - thumb and track color */
	}
	
	/* 确保在所有浏览器中完全隐藏滚动条 */
	.group-scroll::-webkit-scrollbar,
	.group-scroll::-webkit-scrollbar-button,
	.group-scroll::-webkit-scrollbar-track,
	.group-scroll::-webkit-scrollbar-track-piece,
	.group-scroll::-webkit-scrollbar-thumb,
	.group-scroll::-webkit-scrollbar-corner,
	.group-scroll::-webkit-resizer {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
		background-color: #f5f5f5 !important;
		opacity: 0 !important;
		visibility: hidden !important;
		color: transparent !important;
		border: none !important;
	}
	
	.group-item {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		height: 88rpx;  /* 与group-nav高度匹配 */
		padding: 0 32rpx;  /* 稍微增加内边距 */
		position: relative;
		transition: all 0.3s ease;
	}
	
	/* 点击动画效果 */
	.group-item:active {
		transform: scale(0.95);
	}
	
	.hot-item {
		padding-left: 45rpx;  /* 稍微增加内边距 */
		padding-right: 45rpx;  /* 稍微增加内边距 */
	}
	
	.group-name {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.9);
		text-align: center;
		z-index: 1;
		position: relative;
	}
	
	.group-item.active .group-name {
		color: #ffffff;
		font-weight: bold;
		text-shadow: 0 1px 2px rgba(0,0,0,0.15);
	}
	
	/* 气泡背景 */
	.bubble-bg {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 85%; /* 从95%缩小到85% */
		height: 70%; /* 从80%缩小到70% */
		background: linear-gradient(135deg, #ff6b6b, #ff8e53, #ff6348);
		border-radius: 35rpx;
		z-index: 0;
		opacity: 0.4;
		transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
		animation: bubbleColorShift 500s ease-in-out infinite;
	}
	
	.group-item.active .bubble-bg {
		opacity: 0.8; /* 激活时增加不透明度 */
		transform: translate(-50%, -50%) scale(1.05); /* 轻微放大 */
		box-shadow:
			inset 0 0 15rpx rgba(0, 0, 0, 0.3),
			0 0 20rpx rgba(255, 255, 255, 0.2);
	}
	
	/* 应用卡片区域 - 调整顶部边距以适应分组栏新位置 */
	.fixed-apps {
		padding: 15rpx; /* 减少左右内边距 */
		padding-top: 118rpx; /* 调整顶部内边距以适应分组栏新位置 */
		background: transparent;
		position: relative;
		z-index: 3;
	}
	
	/* 新增：应用轮播容器 */
	.app-swiper {
		height: calc(100vh - 400rpx + 40rpx); /* 减去轮播图高度，加上overlap部分 */
		width: 100%;
		margin-top: 25rpx; /* 调整为更大的正值，增加与分组栏的距离 */
		background: transparent;
	}
	
	.app-scroll-view {
		height: 100%;
		width: 100%;
	}

	/* 强制隐藏所有滚动条 */
	.app-scroll-view::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
	}

	.app-scroll-view::-webkit-scrollbar-track {
		display: none !important;
	}

	.app-scroll-view::-webkit-scrollbar-thumb {
		display: none !important;
	}

	.app-scroll-view {
		-ms-overflow-style: none !important;  /* IE和Edge */
		scrollbar-width: none !important;  /* Firefox */
		overflow-x: hidden !important;
	}

	/* 针对uni-app的scroll-view组件 */
	scroll-view::-webkit-scrollbar {
		display: none !important;
		width: 0 !important;
		height: 0 !important;
	}
	
	.apps-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx; /* 减少行间距 */
	}

	/* 为不同位置的卡片添加不同的动画延迟 */
	.apps-row:nth-child(odd) .app-card:nth-child(1) {
		animation-delay: 0s;
	}

	.apps-row:nth-child(odd) .app-card:nth-child(2) {
		animation-delay: 1s;
	}

	.apps-row:nth-child(even) .app-card:nth-child(1) {
		animation-delay: 2s;
	}

	.apps-row:nth-child(even) .app-card:nth-child(2) {
		animation-delay: 3s;
	}
	
	.app-card {
		width: 48%;
		height: 350rpx; /* 减少卡片高度 */
		border-radius: 24rpx;
		overflow: hidden;
		position: relative;
		background-size: cover;
		background-position: center;
		transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
		border: 1px solid rgba(255, 255, 255, 0.1);
		box-shadow:
			0 20rpx 40rpx rgba(0, 0, 0, 0.4),
			0 10rpx 20rpx rgba(0, 0, 0, 0.2),
			inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(15rpx);
		animation: cardFloat 6s ease-in-out infinite;
		z-index: 2;
	}

	/* 应用卡片悬浮动画 */
	@keyframes cardFloat {
		0%, 100% {
			transform: translateY(0rpx) scale(1);
			box-shadow:
				0 15rpx 35rpx rgba(0, 0, 0, 0.15),
				0 5rpx 15rpx rgba(0, 0, 0, 0.1),
				inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
		}
		50% {
			transform: translateY(-8rpx) scale(1.02);
			box-shadow:
				0 25rpx 50rpx rgba(0, 0, 0, 0.2),
				0 10rpx 25rpx rgba(0, 0, 0, 0.15),
				inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
		}
	}

	/* 鼠标悬停效果 */
	.app-card:hover {
		transform: translateY(-10rpx) scale(1.05);
		box-shadow:
			0 30rpx 60rpx rgba(0, 0, 0, 0.25),
			0 15rpx 30rpx rgba(0, 0, 0, 0.2),
			inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
	}
	
	/* 点击动画 */
	.app-card:active {
		transform: scale(0.98);
	}
	
	/* 各种卡片的背景 - 仅在API数据为空时使用 */
	.story-card {
		background-image: url('https://picsum.photos/400/600');
	}
	
	.draw-card {
		background-image: url('https://picsum.photos/401/600');
	}
	
	.voice-card {
		background-image: url('https://picsum.photos/402/600');
	}
	
	.image-video-card {
		background-image: url('https://picsum.photos/403/600');
	}
	
	.app-card-tag {
		position: absolute;
		top: 10rpx;
		left: 10rpx;
		right: 10rpx;
		display: flex;
		flex-direction: column;
		padding: 5rpx;
		align-items: flex-start;
	}
	
	.app-tag-label {
		display: inline-block;
		background: linear-gradient(135deg, #ff9900, #ff6600);
		color: white;
		font-size: 20rpx;
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		margin-top: 10rpx;
		max-width: 120rpx;
		text-align: center;
		align-self: flex-start;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
		text-transform: uppercase;
		letter-spacing: 1px;
		font-weight: bold;
	}
	
	.app-tag-name {
		font-size: 26rpx;
		font-weight: bold;
		color: #ffffff;
		text-shadow: 0 1px 2px rgba(0,0,0,0.8);
		max-width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-bottom: 5rpx;
		font-family: 'Arial Rounded MT Bold', 'Avenir Next', 'Segoe UI', sans-serif;
		letter-spacing: 0.5px;
		filter: brightness(1.2);
	}
	
	/* .app-card-desc is now merged into .app-card-tag */
	
	.app-desc-text {
		font-size: 20rpx;
		color: rgba(255, 255, 255, 1);
		display: block;
		margin-bottom: 3rpx;
		max-width: 100%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		text-shadow: 0 1px 2px rgba(0,0,0,0.8);
		font-family: 'Hiragino Sans GB', 'Microsoft YaHei', 'PingFang SC', sans-serif;
		font-style: italic;
		letter-spacing: 0px;
		filter: brightness(1.2);
	}
	
	.app-card-btn {
		position: absolute;
		bottom: 20rpx;
		left: 20rpx;
		right: 20rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background: linear-gradient(135deg,
			#ff6b6b 0%, #ff8e53 1%, #ff6348 2%, #ff9ff3 3%, #f368e0 4%, #ff3838 5%,
			#ff4757 6%, #ff6348 7%, #ff7675 8%, #fd79a8 9%, #fdcb6e 10%, #e17055 11%,
			#00b894 12%, #00cec9 13%, #55a3ff 14%, #74b9ff 15%, #0984e3 16%, #6c5ce7 17%,
			#a29bfe 18%, #fd79a8 19%, #fdcb6e 20%, #e17055 21%, #00b894 22%, #00cec9 23%,
			#55a3ff 24%, #74b9ff 25%, #0984e3 26%, #6c5ce7 27%, #a29bfe 28%, #ffeaa7 29%,
			#fab1a0 30%, #ff7675 31%, #fd79a8 32%, #e84393 33%, #00b894 34%, #00cec9 35%,
			#74b9ff 36%, #0984e3 37%, #6c5ce7 38%, #a29bfe 39%, #ffeaa7 40%, #fab1a0 41%,
			#ff7675 42%, #fd79a8 43%, #e84393 44%, #00b894 45%, #00cec9 46%, #74b9ff 47%,
			#0984e3 48%, #6c5ce7 49%, #a29bfe 50%, #ffeaa7 51%, #fab1a0 52%, #ff7675 53%,
			#fd79a8 54%, #e84393 55%, #00b894 56%, #00cec9 57%, #74b9ff 58%, #0984e3 59%,
			#6c5ce7 60%, #a29bfe 61%, #ffeaa7 62%, #fab1a0 63%, #ff7675 64%, #fd79a8 65%,
			#e84393 66%, #00b894 67%, #00cec9 68%, #74b9ff 69%, #0984e3 70%, #6c5ce7 71%,
			#a29bfe 72%, #ffeaa7 73%, #fab1a0 74%, #ff7675 75%, #fd79a8 76%, #e84393 77%,
			#00b894 78%, #00cec9 79%, #74b9ff 80%, #0984e3 81%, #6c5ce7 82%, #a29bfe 83%,
			#ffeaa7 84%, #fab1a0 85%, #ff7675 86%, #fd79a8 87%, #e84393 88%, #00b894 89%,
			#00cec9 90%, #74b9ff 91%, #0984e3 92%, #6c5ce7 93%, #a29bfe 94%, #ffeaa7 95%,
			#fab1a0 96%, #ff7675 97%, #fd79a8 98%, #e84393 99%, #ff6b6b 100%
		);
		background-size: 400% 400%;
		border-radius: 40rpx;
		border: 1px solid rgba(255, 255, 255, 0.2);
		transition: all 0.3s ease;
		backdrop-filter: blur(10rpx);
		box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.3);
		animation: appCardColorShift 400s ease-in-out infinite;
	}
	
	.app-card:active .app-card-btn {
		transform: translateY(2rpx);
		box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.2);
	}
	
	.btn-text {
		font-size: 28rpx;
		font-weight: bold;
		color: #fff;
	}
	
	/* 加载状态 */
	.loading {
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		background-color: rgba(0, 0, 0, 0.7);
		color: white;
		padding: 20rpx 40rpx;
		border-radius: 10rpx;
		z-index: 999;
		font-size: 28rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	
	.loading:before {
		content: "";
		width: 40rpx;
		height: 40rpx;
		display: inline-block;
		border-radius: 50%;
		border: 4rpx solid rgba(255, 255, 255, 0.3);
		border-top-color: #fff;
		animation: spin 1s linear infinite;
		margin-bottom: 10rpx;
	}
	
	@keyframes spin {
		to { transform: rotate(360deg); }
	}
	
	/* 无数据提示 */
	.empty-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200rpx;
		color: #999;
		font-size: 28rpx;
	}
	
	/* 动画过渡效果 */
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(10rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	.app-card {
		animation: fadeIn 0.3s ease-out;
	}
	
	/* 收藏图标 */
	.favorite-icon {
		position: absolute;
		top: 4rpx;
		right: 4rpx;
		width: 70rpx;
		height: 70rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
		transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}
	
	.favorite-icon:active {
		transform: scale(1.3);
		background-color: rgba(0, 0, 0, 0.5);
	}
	
	.iconfont {
		font-size: 42rpx;
		color: #ffffff;
		text-shadow: 0 2px 3px rgba(0,0,0,0.3);
		transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
	}
	
	.icon-heart-filled {
		color: #ff3b5c;
		transform: scale(1.3);
		text-shadow: 0 2px 4px rgba(255,59,92,0.6);
		filter: brightness(1.2);
	}
	
	.icon-heart-outline {
		color: rgba(255, 255, 255, 0.85);
	}
	
	/* 添加心跳动画效果 */
	@keyframes heartBeat {
		0% { transform: scale(1); }
		14% { transform: scale(1.3); }
		28% { transform: scale(1); }
		42% { transform: scale(1.3); }
		70% { transform: scale(1); }
	}
	
	.favorite-icon:active .iconfont.icon-heart-filled {
		animation: heartBeat 0.8s ease-in-out;
	}
	
	/* 添加收藏成功的动画 */
	@keyframes heartPulse {
		0% { transform: scale(1); }
		50% { transform: scale(1.3); }
		100% { transform: scale(1); }
	}

	.favorite-icon:active .iconfont.icon-heart-filled {
		animation: heartPulse 0.4s ease-in-out;
	}

	/* 气泡缓慢颜色渐变动画 - 每5秒一个新的颜色组合，总共500秒循环 */
	@keyframes bubbleColorShift {
		0% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6348 100%); }
		1% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 30%, #ff6348 60%, #ff9ff3 100%); }
		2% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 30%, #ff9ff3 70%, #f368e0 100%); }
		3% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 40%, #f368e0 80%, #ff3838 100%); }
		4% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 50%, #ff3838 100%); }

		5% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 50%, #ff4757 100%); }
		6% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 30%, #ff4757 60%, #ff7675 100%); }
		7% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 30%, #ff7675 70%, #fd79a8 100%); }
		8% { background: linear-gradient(135deg, #ff4757 0%, #ff7675 40%, #fd79a8 80%, #fdcb6e 100%); }
		9% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 50%, #fdcb6e 100%); }

		10% { background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 50%, #e17055 100%); }
		11% { background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 30%, #e17055 60%, #00b894 100%); }
		12% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 30%, #00b894 70%, #00cec9 100%); }
		13% { background: linear-gradient(135deg, #e17055 0%, #00b894 40%, #00cec9 80%, #55a3ff 100%); }
		14% { background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #55a3ff 100%); }

		15% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 50%, #74b9ff 100%); }
		16% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 30%, #74b9ff 60%, #0984e3 100%); }
		17% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 30%, #0984e3 70%, #6c5ce7 100%); }
		18% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 40%, #6c5ce7 80%, #a29bfe 100%); }
		19% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 50%, #a29bfe 100%); }

		20% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 50%, #ffeaa7 100%); }
		21% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 30%, #ffeaa7 60%, #fab1a0 100%); }
		22% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 30%, #fab1a0 70%, #e84393 100%); }
		23% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 40%, #e84393 80%, #ff6b6b 100%); }
		24% { background: linear-gradient(135deg, #fab1a0 0%, #e84393 50%, #ff6b6b 100%); }

		25% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 50%, #ff8e53 100%); }
		26% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 30%, #ff8e53 60%, #ff6348 100%); }
		27% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 30%, #ff6348 70%, #ff9ff3 100%); }
		28% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 40%, #ff9ff3 80%, #f368e0 100%); }
		29% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 50%, #f368e0 100%); }

		30% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 50%, #ff3838 100%); }
		31% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 30%, #ff3838 60%, #ff4757 100%); }
		32% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 30%, #ff4757 70%, #ff7675 100%); }
		33% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 40%, #ff7675 80%, #fd79a8 100%); }
		34% { background: linear-gradient(135deg, #ff4757 0%, #ff7675 50%, #fd79a8 100%); }

		35% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 50%, #fdcb6e 100%); }
		36% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 30%, #fdcb6e 60%, #e17055 100%); }
		37% { background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 30%, #e17055 70%, #00b894 100%); }
		38% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 40%, #00b894 80%, #00cec9 100%); }
		39% { background: linear-gradient(135deg, #e17055 0%, #00b894 50%, #00cec9 100%); }

		40% { background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #55a3ff 100%); }
		41% { background: linear-gradient(135deg, #00b894 0%, #00cec9 30%, #55a3ff 60%, #74b9ff 100%); }
		42% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 30%, #74b9ff 70%, #0984e3 100%); }
		43% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 40%, #0984e3 80%, #6c5ce7 100%); }
		44% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%); }

		45% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 50%, #a29bfe 100%); }
		46% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 30%, #a29bfe 60%, #ffeaa7 100%); }
		47% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 30%, #ffeaa7 70%, #fab1a0 100%); }
		48% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 40%, #fab1a0 80%, #e84393 100%); }
		49% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #e84393 100%); }

		50% { background: linear-gradient(135deg, #fab1a0 0%, #e84393 50%, #ff6b6b 100%); }
		51% { background: linear-gradient(135deg, #fab1a0 0%, #e84393 30%, #ff6b6b 60%, #ff8e53 100%); }
		52% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 30%, #ff8e53 70%, #ff6348 100%); }
		53% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 40%, #ff6348 80%, #ff9ff3 100%); }
		54% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 50%, #ff9ff3 100%); }

		55% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 50%, #f368e0 100%); }
		56% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 30%, #f368e0 60%, #ff3838 100%); }
		57% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 30%, #ff3838 70%, #ff4757 100%); }
		58% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 40%, #ff4757 80%, #ff7675 100%); }
		59% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 50%, #ff7675 100%); }

		60% { background: linear-gradient(135deg, #ff4757 0%, #ff7675 50%, #fd79a8 100%); }
		61% { background: linear-gradient(135deg, #ff4757 0%, #ff7675 30%, #fd79a8 60%, #fdcb6e 100%); }
		62% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 30%, #fdcb6e 70%, #e17055 100%); }
		63% { background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 40%, #e17055 80%, #00b894 100%); }
		64% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 50%, #00b894 100%); }

		65% { background: linear-gradient(135deg, #e17055 0%, #00b894 50%, #00cec9 100%); }
		66% { background: linear-gradient(135deg, #e17055 0%, #00b894 30%, #00cec9 60%, #55a3ff 100%); }
		67% { background: linear-gradient(135deg, #00b894 0%, #00cec9 30%, #55a3ff 70%, #74b9ff 100%); }
		68% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 40%, #74b9ff 80%, #0984e3 100%); }
		69% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 50%, #0984e3 100%); }

		70% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%); }
		71% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 30%, #6c5ce7 60%, #a29bfe 100%); }
		72% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 30%, #a29bfe 70%, #ffeaa7 100%); }
		73% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 40%, #ffeaa7 80%, #fab1a0 100%); }
		74% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 50%, #fab1a0 100%); }

		75% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #e84393 100%); }
		76% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 30%, #e84393 60%, #ff6b6b 100%); }
		77% { background: linear-gradient(135deg, #fab1a0 0%, #e84393 30%, #ff6b6b 70%, #ff8e53 100%); }
		78% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 40%, #ff8e53 80%, #ff6348 100%); }
		79% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6348 100%); }

		80% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 50%, #ff9ff3 100%); }
		81% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 30%, #ff9ff3 60%, #f368e0 100%); }
		82% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 30%, #f368e0 70%, #ff3838 100%); }
		83% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 40%, #ff3838 80%, #ff4757 100%); }
		84% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 50%, #ff4757 100%); }

		85% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 50%, #ff7675 100%); }
		86% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 30%, #ff7675 60%, #fd79a8 100%); }
		87% { background: linear-gradient(135deg, #ff4757 0%, #ff7675 30%, #fd79a8 70%, #fdcb6e 100%); }
		88% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 40%, #fdcb6e 80%, #e17055 100%); }
		89% { background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 50%, #e17055 100%); }

		90% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 50%, #00b894 100%); }
		91% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 30%, #00b894 60%, #00cec9 100%); }
		92% { background: linear-gradient(135deg, #e17055 0%, #00b894 30%, #00cec9 70%, #55a3ff 100%); }
		93% { background: linear-gradient(135deg, #00b894 0%, #00cec9 40%, #55a3ff 80%, #74b9ff 100%); }
		94% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 50%, #74b9ff 100%); }

		95% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 50%, #0984e3 100%); }
		96% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 30%, #0984e3 60%, #6c5ce7 100%); }
		97% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 30%, #6c5ce7 70%, #a29bfe 100%); }
		98% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 40%, #a29bfe 80%, #ffeaa7 100%); }
		99% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 50%, #ffeaa7 100%); }
		100% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 50%, #ff6b6b 100%); }
	}

	/* 应用卡片按钮缓慢颜色渐变动画 - 每5秒一个新的颜色组合，总共400秒循环 */
	@keyframes appCardColorShift {
		0% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%); }
		1.25% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 30%, #6c5ce7 60%, #a29bfe 100%); }
		2.5% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 30%, #a29bfe 70%, #ffeaa7 100%); }
		3.75% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 40%, #ffeaa7 80%, #fab1a0 100%); }
		5% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 50%, #fab1a0 100%); }

		6.25% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #ff7675 100%); }
		7.5% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 30%, #ff7675 60%, #fd79a8 100%); }
		8.75% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 30%, #fd79a8 70%, #e84393 100%); }
		10% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 40%, #e84393 80%, #ff6b6b 100%); }
		11.25% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 50%, #ff6b6b 100%); }

		12.5% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 50%, #ff8e53 100%); }
		13.75% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 30%, #ff8e53 60%, #ff6348 100%); }
		15% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 30%, #ff6348 70%, #ff9ff3 100%); }
		16.25% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 40%, #ff9ff3 80%, #f368e0 100%); }
		17.5% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 50%, #f368e0 100%); }

		18.75% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 50%, #00b894 100%); }
		20% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 30%, #00b894 60%, #00cec9 100%); }
		21.25% { background: linear-gradient(135deg, #f368e0 0%, #00b894 30%, #00cec9 70%, #55a3ff 100%); }
		22.5% { background: linear-gradient(135deg, #00b894 0%, #00cec9 40%, #55a3ff 80%, #74b9ff 100%); }
		23.75% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 50%, #74b9ff 100%); }

		25% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 50%, #0984e3 100%); }
		26.25% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 30%, #0984e3 60%, #6c5ce7 100%); }
		27.5% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 30%, #6c5ce7 70%, #a29bfe 100%); }
		28.75% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 40%, #a29bfe 80%, #ffeaa7 100%); }
		30% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 50%, #ffeaa7 100%); }

		31.25% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 50%, #fab1a0 100%); }
		32.5% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 30%, #fab1a0 60%, #ff7675 100%); }
		33.75% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 30%, #ff7675 70%, #fd79a8 100%); }
		35% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 40%, #fd79a8 80%, #e84393 100%); }
		36.25% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 50%, #e84393 100%); }

		37.5% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 50%, #ff6b6b 100%); }
		38.75% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 30%, #ff6b6b 60%, #ff8e53 100%); }
		40% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 30%, #ff8e53 70%, #ff6348 100%); }
		41.25% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 40%, #ff6348 80%, #ff9ff3 100%); }
		42.5% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 50%, #ff9ff3 100%); }

		43.75% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 50%, #f368e0 100%); }
		45% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 30%, #f368e0 60%, #00b894 100%); }
		46.25% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 30%, #00b894 70%, #00cec9 100%); }
		47.5% { background: linear-gradient(135deg, #f368e0 0%, #00b894 40%, #00cec9 80%, #55a3ff 100%); }
		48.75% { background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #55a3ff 100%); }

		50% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 50%, #74b9ff 100%); }
		51.25% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 30%, #74b9ff 60%, #0984e3 100%); }
		52.5% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 30%, #0984e3 70%, #6c5ce7 100%); }
		53.75% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 40%, #6c5ce7 80%, #a29bfe 100%); }
		55% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 50%, #a29bfe 100%); }

		56.25% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 50%, #ffeaa7 100%); }
		57.5% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 30%, #ffeaa7 60%, #fab1a0 100%); }
		58.75% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 30%, #fab1a0 70%, #ff7675 100%); }
		60% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 40%, #ff7675 80%, #fd79a8 100%); }
		61.25% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 50%, #fd79a8 100%); }

		62.5% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 50%, #e84393 100%); }
		63.75% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 30%, #e84393 60%, #ff6b6b 100%); }
		65% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 30%, #ff6b6b 70%, #ff8e53 100%); }
		66.25% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 40%, #ff8e53 80%, #ff6348 100%); }
		67.5% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6348 100%); }

		68.75% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 50%, #ff9ff3 100%); }
		70% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 30%, #ff9ff3 60%, #f368e0 100%); }
		71.25% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 30%, #f368e0 70%, #00b894 100%); }
		72.5% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 40%, #00b894 80%, #00cec9 100%); }
		73.75% { background: linear-gradient(135deg, #f368e0 0%, #00b894 50%, #00cec9 100%); }

		75% { background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #55a3ff 100%); }
		76.25% { background: linear-gradient(135deg, #00b894 0%, #00cec9 30%, #55a3ff 60%, #74b9ff 100%); }
		77.5% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 30%, #74b9ff 70%, #0984e3 100%); }
		78.75% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 40%, #0984e3 80%, #6c5ce7 100%); }
		80% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%); }

		81.25% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 50%, #a29bfe 100%); }
		82.5% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 30%, #a29bfe 60%, #ffeaa7 100%); }
		83.75% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 30%, #ffeaa7 70%, #fab1a0 100%); }
		85% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 40%, #fab1a0 80%, #ff7675 100%); }
		86.25% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #ff7675 100%); }

		87.5% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 50%, #fd79a8 100%); }
		88.75% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 30%, #fd79a8 60%, #e84393 100%); }
		90% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 30%, #e84393 70%, #ff6b6b 100%); }
		91.25% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 40%, #ff6b6b 80%, #ff8e53 100%); }
		92.5% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 50%, #ff8e53 100%); }

		93.75% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6348 100%); }
		95% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 30%, #ff6348 60%, #ff9ff3 100%); }
		96.25% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 30%, #ff9ff3 70%, #f368e0 100%); }
		97.5% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 40%, #f368e0 80%, #74b9ff 100%); }
		98.75% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 50%, #74b9ff 100%); }
		100% { background: linear-gradient(135deg, #f368e0 0%, #74b9ff 50%, #0984e3 100%); }
	}
</style>
