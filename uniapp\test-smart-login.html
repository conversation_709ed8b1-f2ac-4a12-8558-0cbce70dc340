<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能登录弹窗测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .test-container {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-title {
            font-size: 24px;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.95);
        }

        .test-desc {
            font-size: 14px;
            margin-bottom: 30px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.5;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .user-text {
            display: flex;
            flex-direction: column;
            gap: 4px;
            text-align: left;
        }

        .user-title {
            font-size: 18px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
        }

        .user-desc {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
        }

        .environment-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .env-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #4CAF50;
        }

        .env-detail {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 5px;
        }

        /* 模态弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            padding: 40px;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-container {
            background: #ffffff;
            border-radius: 24px;
            width: 100%;
            max-width: 400px;
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 40px 40px 20px;
        }

        .logo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 20px;
        }

        .app-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #666;
            cursor: pointer;
            border: none;
        }

        .login-methods {
            padding: 20px 40px 40px;
        }

        .login-method {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 20px;
            border-radius: 16px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 600;
        }

        .login-method.primary {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .login-method.secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .login-method:hover {
            transform: translateY(-2px);
        }

        .method-icon {
            font-size: 24px;
        }

        .divider {
            text-align: center;
            margin: 30px 0 20px;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }

        .divider-text {
            background: white;
            padding: 0 15px;
            font-size: 14px;
            color: #999;
            position: relative;
        }

        .other-methods {
            display: flex;
            gap: 15px;
        }

        .other-methods .login-method {
            flex: 1;
            font-size: 14px;
        }

        .agreement {
            text-align: center;
            padding: 20px 40px 40px;
            font-size: 12px;
            color: #999;
        }

        .agreement-link {
            color: #667eea;
            cursor: pointer;
        }

        /* 登录表单样式 */
        .login-form {
            padding: 20px 40px 40px;
        }

        .form-item {
            margin-bottom: 20px;
        }

        .input-wrapper {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .input-wrapper:focus-within {
            border-color: #667eea;
            background: #fff;
        }

        .input-icon {
            padding: 0 15px;
            font-size: 18px;
            color: #999;
        }

        .form-input {
            flex: 1;
            padding: 15px 10px;
            font-size: 16px;
            border: none;
            background: transparent;
            color: #333;
            outline: none;
        }

        .toggle-password {
            padding: 0 15px;
            font-size: 18px;
            color: #999;
            cursor: pointer;
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            font-size: 14px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .remember-me input[type="checkbox"] {
            margin-right: 8px;
        }

        .checkbox-text {
            color: #666;
        }

        .forgot-password {
            color: #667eea;
            cursor: pointer;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .register-link {
            text-align: center;
            font-size: 14px;
        }

        .link-text {
            color: #666;
        }

        .link-action {
            color: #667eea;
            cursor: pointer;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">智能登录弹窗测试</h1>
        <p class="test-desc">
            点击下方用户信息区域测试智能登录功能<br>
            <strong>浏览器环境</strong>：显示登录表单弹窗<br>
            <strong>微信/小程序环境</strong>：自动进行相应登录，无弹窗
        </p>

        <div class="environment-info">
            <div class="env-title">当前环境检测</div>
            <div class="env-detail" id="userAgent">用户代理: 检测中...</div>
            <div class="env-detail" id="environment">环境类型: 检测中...</div>
            <div class="env-detail" id="recommendation">推荐登录方式: 检测中...</div>
        </div>

        <div class="user-info" onclick="showLoginModal()">
            <div class="avatar">🤖</div>
            <div class="user-text">
                <div class="user-title">登录/注册</div>
                <div class="user-desc">成为本站会员</div>
            </div>
        </div>
    </div>

    <!-- 智能登录弹窗 -->
    <div class="modal-overlay" id="loginModal">
        <div class="modal-container">
            <div class="modal-header">
                <div class="logo-section">
                    <div class="logo-icon">🤖</div>
                    <div class="app-title">启元AI</div>
                </div>
                <button class="close-btn" onclick="hideLoginModal()">✕</button>
            </div>

            <div class="login-form">
                <!-- 用户名输入 -->
                <div class="form-item">
                    <div class="input-wrapper">
                        <span class="input-icon">👤</span>
                        <input type="text" class="form-input" placeholder="请输入手机号/邮箱" id="username">
                    </div>
                </div>

                <!-- 密码输入 -->
                <div class="form-item">
                    <div class="input-wrapper">
                        <span class="input-icon">🔒</span>
                        <input type="password" class="form-input" placeholder="请输入密码" id="password">
                        <span class="toggle-password" onclick="togglePassword()">🙈</span>
                    </div>
                </div>

                <!-- 记住密码和忘记密码 -->
                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkbox-text">记住密码</span>
                    </label>
                    <span class="forgot-password" onclick="alert('忘记密码功能开发中')">忘记密码？</span>
                </div>

                <!-- 登录按钮 -->
                <button class="login-btn" onclick="handleFormLogin()">登录</button>

                <!-- 注册链接 -->
                <div class="register-link">
                    <span class="link-text">还没有账号？</span>
                    <span class="link-action" onclick="alert('注册功能开发中')">立即注册</span>
                </div>
            </div>

            <div class="agreement">
                登录即表示同意
                <span class="agreement-link">《用户协议》</span>
                与
                <span class="agreement-link">《隐私政策》</span>
            </div>
        </div>
    </div>

    <script>
        // 环境检测
        function detectEnvironment() {
            const ua = navigator.userAgent.toLowerCase();
            let environment = 'browser';
            let recommendation = '账号密码登录';
            let primaryIcon = '👤';
            let primaryText = '账号密码登录';

            document.getElementById('userAgent').textContent = `用户代理: ${navigator.userAgent}`;

            if (ua.includes('micromessenger')) {
                if (ua.includes('miniprogram')) {
                    environment = 'miniprogram';
                    recommendation = '微信小程序登录';
                    primaryIcon = '📱';
                    primaryText = '微信小程序登录';
                } else {
                    environment = 'wechat';
                    recommendation = '微信公众号登录';
                    primaryIcon = '💬';
                    primaryText = '微信登录';
                }
            } else {
                environment = 'browser';
                recommendation = '账号密码登录';
                primaryIcon = '👤';
                primaryText = '账号密码登录';
            }

            document.getElementById('environment').textContent = `环境类型: ${environment}`;
            document.getElementById('recommendation').textContent = `推荐登录方式: ${recommendation}`;
            
            // 更新主要登录方式
            document.getElementById('primaryIcon').textContent = primaryIcon;
            document.getElementById('primaryText').textContent = primaryText;

            return environment;
        }

        // 显示登录弹窗
        function showLoginModal() {
            const env = detectEnvironment();

            if (env === 'wechat') {
                // 微信环境直接进行微信登录
                alert('检测到微信环境，自动进行微信公众号登录...');
                // 这里会调用微信OAuth登录
                return;
            }

            if (env === 'miniprogram') {
                // 小程序环境直接进行小程序登录
                alert('检测到小程序环境，自动进行微信小程序登录...');
                // 这里会调用小程序登录API
                return;
            }

            // 浏览器环境显示登录表单弹窗
            document.getElementById('loginModal').classList.add('show');
        }

        // 隐藏登录弹窗
        function hideLoginModal() {
            document.getElementById('loginModal').classList.remove('show');
        }

        // 主要登录方式处理
        function handlePrimaryLogin() {
            const env = detectEnvironment();
            switch(env) {
                case 'wechat':
                    alert('微信公众号登录功能开发中');
                    break;
                case 'miniprogram':
                    alert('微信小程序登录功能开发中');
                    break;
                default:
                    handleAccountLogin();
                    break;
            }
        }

        // 账号密码登录
        function handleAccountLogin() {
            alert('跳转到账号密码登录页面');
            hideLoginModal();
        }

        // 切换密码显示
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.toggle-password');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '👁️';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '🙈';
            }
        }

        // 处理表单登录
        function handleFormLogin() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const rememberMe = document.getElementById('rememberMe').checked;

            if (!username) {
                alert('请输入用户名、手机号或邮箱');
                return;
            }

            if (!password) {
                alert('请输入密码');
                return;
            }

            if (password.length < 6) {
                alert('密码长度不能少于6位');
                return;
            }

            // 模拟登录过程
            const loginBtn = document.querySelector('.login-btn');
            loginBtn.textContent = '登录中...';
            loginBtn.disabled = true;

            setTimeout(() => {
                alert(`登录成功！\n用户名: ${username}\n记住密码: ${rememberMe ? '是' : '否'}`);
                hideLoginModal();

                // 重置按钮
                loginBtn.textContent = '登录';
                loginBtn.disabled = false;

                // 清空表单
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
                document.getElementById('rememberMe').checked = false;
            }, 1500);
        }

        // 微信登录
        function handleWechatLogin() {
            alert('微信登录功能开发中');
        }

        // 点击遮罩关闭弹窗
        document.getElementById('loginModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideLoginModal();
            }
        });

        // 页面加载时检测环境
        window.onload = function() {
            detectEnvironment();
        };
    </script>
</body>
</html>
