import { AppDataSource } from '../data-source';
import { PackageExpiryService } from '../services/package-expiry.service';

/**
 * 测试改进后的套餐到期逻辑
 * 验证只清理过期套餐的剩余点数，而不是清零所有点数
 */
async function testImprovedPackageExpiry() {
  console.log('=== 测试改进后的套餐到期逻辑 ===\n');

  try {
    // 初始化数据库连接
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('数据库连接已建立');
    }

    // 1. 创建测试用户
    const testUserId = 999;
    const packageId = 1;
    
    console.log('1. 创建测试用户和初始数据...');
    
    // 删除可能存在的测试数据
    await AppDataSource.query(`DELETE FROM points_records WHERE memberId = ?`, [testUserId]);
    await AppDataSource.query(`DELETE FROM members WHERE id = ?`, [testUserId]);
    
    // 创建测试用户
    await AppDataSource.query(`
      INSERT INTO members (id, name, phone, email, password, points, packageId, packageExpiredAt, createdAt)
      VALUES (?, '测试用户', '13800000999', '<EMAIL>', 'password', 0, NULL, NULL, NOW())
    `, [testUserId]);
    
    console.log('✅ 测试用户创建成功');

    // 2. 模拟用户获得多种来源的点数
    console.log('\n2. 模拟用户获得多种来源的点数...');
    
    // 注册赠送100点
    await AppDataSource.query(`
      INSERT INTO points_records (memberId, type, amount, balanceBefore, balanceAfter, description, relatedType, relatedId, createdAt)
      VALUES (?, 'register_gift', 100, 0, 100, '注册赠送', 'system', NULL, NOW())
    `, [testUserId]);
    
    // 邀请赠送50点
    await AppDataSource.query(`
      INSERT INTO points_records (memberId, type, amount, balanceBefore, balanceAfter, description, relatedType, relatedId, createdAt)
      VALUES (?, 'invite_gift', 50, 100, 150, '邀请好友赠送', 'system', NULL, NOW())
    `, [testUserId]);
    
    // 购买套餐获得1000点
    await AppDataSource.query(`
      INSERT INTO points_records (memberId, type, amount, balanceBefore, balanceAfter, description, relatedType, relatedId, createdAt)
      VALUES (?, 'package_purchase', 1000, 150, 1150, '购买套餐：标准套餐', 'package', ?, NOW())
    `, [testUserId, packageId]);
    
    // 更新用户总点数和套餐信息
    await AppDataSource.query(`
      UPDATE members 
      SET points = 1150, packageId = ?, packageExpiredAt = '2024-01-01 00:00:00'
      WHERE id = ?
    `, [packageId, testUserId]);
    
    console.log('✅ 用户点数来源：注册赠送100 + 邀请赠送50 + 套餐购买1000 = 1150点');

    // 3. 模拟用户使用套餐中的部分点数
    console.log('\n3. 模拟用户使用套餐中的部分点数...');
    
    // 使用300点（来自套餐）
    await AppDataSource.query(`
      INSERT INTO points_records (memberId, type, amount, balanceBefore, balanceAfter, description, relatedType, relatedId, createdAt)
      VALUES (?, 'app_usage', -300, 1150, 850, '智能体消费', 'package', ?, NOW())
    `, [testUserId, packageId]);
    
    // 更新用户点数
    await AppDataSource.query(`
      UPDATE members SET points = 850 WHERE id = ?
    `, [testUserId]);
    
    console.log('✅ 用户使用了300点（来自套餐），剩余850点');

    // 4. 查看处理前的状态
    console.log('\n4. 查看处理前的状态...');
    const beforeState = await AppDataSource.query(`
      SELECT id, name, points, packageId, packageExpiredAt 
      FROM members 
      WHERE id = ?
    `, [testUserId]);
    
    console.log('处理前用户状态:', {
      总点数: beforeState[0].points,
      套餐ID: beforeState[0].packageId,
      过期时间: beforeState[0].packageExpiredAt
    });
    
    // 计算预期结果
    const totalQuota = 1000; // 套餐总点数
    const usedPoints = 300;   // 已使用点数
    const remainingPackagePoints = totalQuota - usedPoints; // 套餐剩余点数 = 700
    const otherSourcePoints = 100 + 50; // 其他来源点数 = 150
    const expectedFinalPoints = otherSourcePoints; // 预期最终点数 = 150
    
    console.log('预期计算：');
    console.log(`- 套餐总点数: ${totalQuota}`);
    console.log(`- 套餐已使用: ${usedPoints}`);
    console.log(`- 套餐剩余: ${remainingPackagePoints}`);
    console.log(`- 其他来源点数: ${otherSourcePoints}`);
    console.log(`- 预期最终保留点数: ${expectedFinalPoints}`);

    // 5. 执行改进后的套餐到期处理
    console.log('\n5. 执行改进后的套餐到期处理...');
    await PackageExpiryService.checkAndProcessExpiredPackages();

    // 6. 查看处理后的状态
    console.log('\n6. 查看处理后的状态...');
    const afterState = await AppDataSource.query(`
      SELECT id, name, points, packageId, packageExpiredAt 
      FROM members 
      WHERE id = ?
    `, [testUserId]);
    
    console.log('处理后用户状态:', {
      总点数: afterState[0].points,
      套餐ID: afterState[0].packageId,
      过期时间: afterState[0].packageExpiredAt
    });

    // 7. 查看点数记录
    console.log('\n7. 查看点数记录...');
    const pointsRecords = await AppDataSource.query(`
      SELECT type, amount, description, createdAt 
      FROM points_records 
      WHERE memberId = ? 
      ORDER BY createdAt DESC
    `, [testUserId]);
    
    console.log('点数记录历史:');
    pointsRecords.forEach((record, index) => {
      console.log(`${index + 1}. ${record.type}: ${record.amount > 0 ? '+' : ''}${record.amount} - ${record.description}`);
    });

    // 8. 验证结果
    console.log('\n=== 测试结果验证 ===');
    
    const actualFinalPoints = afterState[0].points;
    
    if (actualFinalPoints === expectedFinalPoints) {
      console.log('✅ 套餐到期逻辑正确：只清理了过期套餐的剩余点数，保留了其他来源的点数');
      console.log(`✅ 预期保留 ${expectedFinalPoints} 点，实际保留 ${actualFinalPoints} 点`);
    } else {
      console.log('❌ 套餐到期逻辑有误');
      console.log(`❌ 预期保留 ${expectedFinalPoints} 点，实际保留 ${actualFinalPoints} 点`);
    }
    
    if (afterState[0].packageId === null && afterState[0].packageExpiredAt === null) {
      console.log('✅ 过期套餐信息清理成功');
    } else {
      console.log('❌ 过期套餐信息清理失败');
    }
    
    // 检查是否有套餐过期记录
    const expiredRecord = pointsRecords.find(r => r.type === 'package_expired');
    if (expiredRecord) {
      console.log('✅ 套餐过期记录创建成功');
      console.log(`✅ 扣除点数: ${Math.abs(expiredRecord.amount)} (应为 ${remainingPackagePoints})`);
    } else {
      console.log('❌ 套餐过期记录创建失败');
    }

    console.log('\n=== 测试完成 ===');

  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    // 清理测试数据
    try {
      await AppDataSource.query(`DELETE FROM points_records WHERE memberId = 999`);
      await AppDataSource.query(`DELETE FROM members WHERE id = 999`);
      console.log('\n🧹 测试数据清理完成');
    } catch (cleanupError) {
      console.error('清理测试数据失败:', cleanupError);
    }
  }
}

// 运行测试
testImprovedPackageExpiry().catch(console.error);
