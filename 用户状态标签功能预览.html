<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户状态标签功能预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
        }

        .user-info-section {
            padding: 20px;
        }

        .user-basic-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .user-text-info {
            flex: 1;
        }

        .username-row {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 5px;
        }

        .username {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .user-uid {
            font-size: 14px;
            color: #666;
        }

        /* 用户标签样式 */
        .user-tags {
            display: flex;
            flex-direction: column;
            gap: 6px;
            align-items: flex-start;
        }

        .user-tag {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            line-height: 1.2;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .partner-tag {
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
            color: white;
            box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
        }

        .partner-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 107, 107, 0.4);
        }

        .vip-tag {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
        }

        .vip-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.4);
        }

        .normal-tag {
            background: linear-gradient(135deg, #e0e0e0, #f0f0f0);
            color: #666;
            border: 1px solid #ddd;
        }

        .non-partner-tag {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #6c757d;
            border: 1px solid #dee2e6;
            cursor: pointer;
        }

        .non-partner-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2);
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
        }

        .tag-text {
            font-weight: 500;
            font-size: 12px;
        }

        .demo-section {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .demo-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .demo-description {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .switch-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .switch-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: #667eea;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .switch-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .switch-btn.active {
            background: #4c63d2;
        }

        .features-list {
            margin-top: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .features-list h3 {
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 5px 0;
            font-size: 14px;
            color: #555;
            position: relative;
            padding-left: 20px;
        }

        .features-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>用户状态标签功能</h1>
        </div>

        <div class="user-info-section">
            <div class="user-basic-info">
                <div class="user-avatar">张</div>
                <div class="user-text-info">
                    <div class="username-row">
                        <span class="username">张三</span>
                        <div class="user-tags" id="userTags">
                            <!-- 标签将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    <div class="user-uid">UID: 12345</div>
                </div>
            </div>

            <div class="demo-section">
                <div class="demo-title">状态切换演示</div>
                <div class="demo-description">
                    点击下方按钮切换不同的用户状态，查看标签显示效果：
                </div>
                <div class="switch-buttons">
                    <button class="switch-btn" onclick="setUserStatus('normal')">普通会员+非合伙人</button>
                    <button class="switch-btn" onclick="setUserStatus('partner')">普通会员+合伙人</button>
                    <button class="switch-btn" onclick="setUserStatus('vip')">VIP会员+非合伙人</button>
                    <button class="switch-btn" onclick="setUserStatus('both')">VIP会员+合伙人</button>
                </div>
            </div>

            <div class="features-list">
                <h3>功能特点</h3>
                <ul>
                    <li>两个独立标签系统：会员等级 + 合伙人身份</li>
                    <li>会员等级：普通会员 ↔ VIP会员（根据套餐状态）</li>
                    <li>合伙人身份：非合伙人 ↔ 合伙人（根据邀请人数）</li>
                    <li>点击标签弹窗显示权益详情和升级条件</li>
                    <li>现代化设计，适合年轻用户</li>
                    <li>响应式布局，支持移动端</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function setUserStatus(status) {
            const userTags = document.getElementById('userTags');
            const buttons = document.querySelectorAll('.switch-btn');
            
            // 重置按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            
            // 清空标签
            userTags.innerHTML = '';
            
            // 根据状态添加标签（合伙人标签在上，会员标签在下）
            switch(status) {
                case 'normal':
                    userTags.innerHTML = `
                        <div class="user-tag non-partner-tag"><span class="tag-text">非合伙人</span></div>
                        <div class="user-tag normal-tag"><span class="tag-text">普通会员</span></div>
                    `;
                    buttons[0].classList.add('active');
                    break;
                case 'partner':
                    userTags.innerHTML = `
                        <div class="user-tag partner-tag"><span class="tag-text">合伙人</span></div>
                        <div class="user-tag normal-tag"><span class="tag-text">普通会员</span></div>
                    `;
                    buttons[1].classList.add('active');
                    break;
                case 'vip':
                    userTags.innerHTML = `
                        <div class="user-tag non-partner-tag"><span class="tag-text">非合伙人</span></div>
                        <div class="user-tag vip-tag"><span class="tag-text">VIP会员</span></div>
                    `;
                    buttons[2].classList.add('active');
                    break;
                case 'both':
                    userTags.innerHTML = `
                        <div class="user-tag partner-tag"><span class="tag-text">合伙人</span></div>
                        <div class="user-tag vip-tag"><span class="tag-text">VIP会员</span></div>
                    `;
                    buttons[3].classList.add('active');
                    break;
            }
        }

        // 默认显示普通用户状态
        setUserStatus('normal');

        // 添加标签点击事件
        document.addEventListener('click', function(e) {
            if (e.target.closest('.user-tag')) {
                const tag = e.target.closest('.user-tag');
                if (tag.classList.contains('partner-tag')) {
                    alert('点击合伙人标签 - 将显示合伙人权益弹窗');
                } else if (tag.classList.contains('non-partner-tag')) {
                    alert('点击非合伙人标签 - 将显示合伙人升级条件弹窗');
                } else if (tag.classList.contains('vip-tag')) {
                    alert('点击VIP标签 - 将显示VIP权益弹窗');
                }
            }
        });
    </script>
</body>
</html>
