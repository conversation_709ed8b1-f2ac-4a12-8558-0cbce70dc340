import http from 'http';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import multer from 'multer';
import { initDB, connectDB } from './src/db/index.js';
import redemptionCodeRoutes from './src/routes/redemptionCodeRoutes.js';
import { saveConfigToDB, getConfigFromDB, getAllConfigsFromDB } from './src/db/config.js';
import dotenv from 'dotenv';
import { initConfigDatabase } from './init-config-db.js';
import { initUserDatabase } from './server/db/init-user-db.js';
// import authRoutes from './server/routes/auth-routes-esm.js';
import userRoutes from './routes/userRoutes.js';
import agentRoutes from './routes/agentRoutes.js';
import taskRoutes from './routes/taskRoutes.js';
import configRoutes from './routes/configRoutes.js';
// import cozeProxyRoutes from './src/routes/cozeProxyRoutes.js'; // 导入扣子代理路由
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import bcrypt from 'bcrypt';
import axios from 'axios'; // 用于代理请求
import FormData from 'form-data'; // 用于文件上传

// 导入自定义日志工具
import logger from './utils/logger.js'; // 需要将CommonJS模块转换为ESM

// Get current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
const loadEnv = () => {
  const rootEnvPath = path.resolve(__dirname, './.env');
  const configEnvPath = path.resolve(__dirname, './config.env');
  
  if (fs.existsSync(rootEnvPath)) {
    console.log(`加载环境配置文件: ${rootEnvPath}`);
    const result = dotenv.config({ path: rootEnvPath });
    if (result.error) {
      console.error('无法加载环境变量文件:', result.error);
    }
  } else if (fs.existsSync(configEnvPath)) {
    console.log(`加载环境配置文件: ${configEnvPath}`);
    const result = dotenv.config({ path: configEnvPath });
    if (result.error) {
      console.error('无法加载环境变量文件:', result.error);
    }
  } else {
    console.warn('未找到.env或config.env文件，使用默认环境变量');
    dotenv.config();
  }
};

// 加载环境变量
loadEnv();

// 设置日志级别
const LOG_LEVEL = process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'INFO' : 'DEBUG');
logger.info(`启动服务器，环境: ${process.env.NODE_ENV || 'development'}, 日志级别: ${LOG_LEVEL}`, 'SERVER');

// 获取配置
const PORT = process.env.PORT || 3030;
const API_PORT = process.env.API_PORT || 3030;
const USE_DOMAIN_MODE = process.env.USE_DOMAIN_MODE === 'true';
const API_DOMAIN = process.env.API_DOMAIN || 'api.8a8.top';
const FRONTEND_DOMAIN = process.env.FRONTEND_DOMAIN || 'zntcs.8a8.top';
const API_BASE_URL = process.env.API_BASE_URL || `https://${API_DOMAIN}/api`;
const FRONTEND_URL = process.env.FRONTEND_URL || `https://${FRONTEND_DOMAIN}`;

// Coze API配置
const COZE_API_BASE_URL = 'https://api.coze.cn/v1';
const COZE_AUTH_BASE_URL = 'https://api.coze.cn/oauth/token';

// 创建Express应用
const app = express();

// 配置中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 设置响应头，确保中文正确显示
app.use((req, res, next) => {
  res.header('Content-Type', 'application/json; charset=utf-8');
  next();
});

// 添加日志中间件，记录所有请求
app.use((req, res, next) => {
  // 请求开始时间
  const startTime = Date.now();
  
  // 记录请求信息
  logger.request(req);
  
  // 拦截响应完成事件
  const originalEnd = res.end;
  res.end = function(...args) {
    // 计算请求处理时间
    const responseTime = Date.now() - startTime;
    
    // 记录响应信息
    logger.response(req, res, responseTime);
    
    // 调用原始的end方法
    return originalEnd.apply(this, args);
  };
  
  next();
});

// 添加一些测试路由，确保路由系统正常工作
app.get('/api/test', (req, res) => {
  console.log('测试路由被访问');
  res.json({ message: '测试路由正常工作' });
});

app.get('/api/proxy-test', (req, res) => {
  console.log('代理测试路由被访问');
  res.json({ message: '代理测试路由正常工作' });
});

// 简化的Coze代理API路由，直接位于全局作用域
app.get('/api/proxy/bots-test', (req, res) => {
  console.log('机器人列表测试路由被访问');
  res.json({ 
    success: true, 
    message: '测试成功',
    items: [
      { bot_id: 'test123', name: '测试机器人' }
    ] 
  });
});

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 上传文件的静态服务
app.use('/uploads', express.static(path.join(__dirname, 'public', 'uploads')));

// 初始化上传目录
const initUploadDirectories = () => {
  const uploadDirs = [
    path.join(__dirname, 'public', 'uploads'),
    path.join(__dirname, 'public', 'uploads', 'images'),
    path.join(__dirname, 'public', 'uploads', 'files')
  ];

  uploadDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`创建上传目录: ${dir}`);
    }
  });
};

// 动态生成环境配置文件
app.get('/env-config.js', (req, res) => {
  // 根据当前模式决定URL格式
  const apiBaseUrl = USE_DOMAIN_MODE ? API_BASE_URL : `http://localhost:${API_PORT}`;
  const frontendUrl = USE_DOMAIN_MODE ? FRONTEND_URL : `http://localhost:${process.env.FRONTEND_PORT || 5173}`;
  
  const envConfig = `window.ENV_CONFIG = {
    API_BASE_URL: "${apiBaseUrl}",
    APP_DOMAIN: "${frontendUrl}"
  };`;
  
  res.setHeader('Content-Type', 'application/javascript');
  res.send(envConfig);
});

// 使用扣子代理路由，不需要认证
// app.use('/api/proxy', cozeProxyRoutes);
// logger.info('已加载扣子API代理路由', 'ROUTES');

// 用户相关路由
try {
  const userRoutes = await import('./server/routes/user-routes-esm.js');
  app.use('/api/users', userRoutes.default);
  console.log('已加载用户路由(ESM版本)');
} catch (error) {
  console.warn('无法加载ESM版本的用户路由:', error.message);
  console.log('尝试加载CommonJS版本的用户路由...');
  try {
    const userRoutes = require('./server/routes/user-routes.js');
    app.use('/api/users', userRoutes);
    console.log('已加载用户路由(CommonJS版本)');
  } catch (fallbackError) {
    console.error('无法加载用户路由:', fallbackError);
  }
}

// 认证路由
// app.use('/api/auth', authRoutes);
// console.log('已加载认证路由');

// 全局认证中间件 - 拦截所有API请求，除了公开路径
const PUBLIC_PATHS = [
  '/api/auth/login', 
  '/api/auth/register', 
  '/health', 
  '/api/health',
  '/ping',
  '/api/ping',
  '/api/system/status',
  '/api/config/system-basic',
  '/api/config/system-logo',
  '/api/config/type/system-basic',
  '/api/config/type/system-logo',
  '/api/configs/system-basic',
  '/api/configs/system-logo',
  '/api/configs/type/system-basic',
  '/api/configs/type/system-logo',
  // 扣子代理API
  '/api/proxy/oauth/token',
  '/api/proxy/bots',
  '/api/proxy/chat'
];



// JWT配置
const jwtSecret = process.env.JWT_SECRET || 'secure_jwt_secret_key_for_ai_agent_admin';
const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '365d';

// 认证中间件
const authMiddleware = (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // 格式: "Bearer TOKEN"

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '未提供访问令牌'
    });
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, jwtSecret);

    // 将用户信息添加到请求对象
    req.user = decoded;

    next();
  } catch (err) {
    console.error('验证令牌失败:', err);
    return res.status(401).json({
      success: false,
      message: '无效的访问令牌'
    });
  }
};

app.use('/api', (req, res, next) => {
  // 跳过公开路径的验证
  if (PUBLIC_PATHS.some(path => req.path.startsWith(path))) {
    return next();
  }
  
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // 格式: "Bearer TOKEN"
  
  if (!token) {
    return res.status(401).json({
      code: 401,
      success: false,
      message: '未授权访问，请先登录'
    });
  }

  try {
    // 验证token，使用环境变量中的密钥
    const decoded = jwt.verify(token, jwtSecret);
    
    // 将用户信息添加到请求对象中，以供后续路由使用
    req.user = decoded;
    
    next();
  } catch (err) {
    console.error('验证令牌失败:', err);
    return res.status(401).json({
      code: 401,
      success: false,
      message: '无效的访问令牌，请重新登录'
    });
  }
});

// 添加 /api/auth/me 路由 - 验证当前用户身份
app.get('/api/auth/me', (req, res) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1]; // 格式: "Bearer TOKEN"
  
  if (!token) {
    return res.status(401).json({
      code: 401,
      success: false,
      message: '未提供访问令牌'
    });
  }

  try {
    // 验证token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const decoded = jwt.verify(token, jwtSecret);
    
    // 如果有数据库连接，可以进一步查询用户详情
    if (db) {
      db.query('SELECT * FROM user WHERE id = ?', [decoded.id])
        .then(([users]) => {
          if (users.length === 0) {
            return res.status(404).json({
              code: 404,
              success: false,
              message: '用户不存在'
            });
          }
          
          const user = users[0];
          // 不返回敏感信息
          delete user.password;
          
          return res.json({
            code: 200,
            success: true,
            data: user,
            message: '获取用户信息成功'
          });
        })
        .catch(err => {
          console.error('查询用户失败:', err);
          // 如果数据库查询失败，只返回令牌中的基本信息
          return res.json({
            code: 200,
            success: true,
            data: {
              id: decoded.id,
              username: decoded.username,
              role: decoded.role || 'admin'
            },
            message: '获取基本用户信息成功'
          });
        });
    } else {
      // 没有数据库连接，只返回令牌中的基本信息
      return res.json({
        code: 200,
        success: true,
        data: {
          id: decoded.id,
          username: decoded.username,
          role: decoded.role || 'admin'
        },
        message: '获取基本用户信息成功'
      });
    }
  } catch (err) {
    console.error('验证令牌失败:', err);
    return res.status(401).json({
      code: 401,
      success: false,
      message: '无效的访问令牌'
    });
  }
});

// 确保登录端点可以访问 - 添加一个专用的处理程序来记录和调试 auth/login 请求
app.use((req, res, next) => {
  if (req.path.startsWith('/api/auth')) {
    console.log(`Auth request received: ${req.method} ${req.path}`);
  }
  next();
});

// 处理登录请求
app.post('/api/auth/login', async (req, res) => {
  try {
    // 获取请求体中的用户名和密码
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        code: 400,
        success: false,
        message: '用户名和密码不能为空'
      });
    }
    
    // 检查数据库连接
    if (!db) {
      console.error('数据库未连接，无法验证用户');
      return res.status(500).json({
        code: 500,
        success: false,
        message: '数据库连接失败，无法验证用户'
      });
    }
    
    // 查询用户信息
    const [users] = await db.query('SELECT * FROM user WHERE username = ?', [username]);
    
    if (users.length === 0) {
      return res.status(401).json({
        code: 401,
        success: false,
        message: '用户名或密码不正确'
      });
    }
    
    const user = users[0];
    
    // 限制只有ID为1的用户可以登录
    // 使用Number()转换确保数值类型比较
    if (Number(user.id) !== 1) {
      console.error(`非授权用户尝试登录系统: ID=${user.id}, 用户名=${user.username}`);
      return res.status(403).json({
        code: 403,
        success: false,
        message: '权限不足，仅系统管理员可访问'
      });
    }
    
    // 验证密码
    const passwordMatch = await comparePassword(password, user.password);
    
    if (!passwordMatch) {
      return res.status(401).json({
        code: 401,
        success: false,
        message: '用户名或密码不正确'
      });
    }
    
    // 生成JWT令牌
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const token = jwt.sign(
      { 
        id: user.id,
        username: user.username,
        role: user.role || 'admin'
      },
      jwtSecret,
      { expiresIn: '24h' }
    );
    
    // 返回成功响应
    return res.json({
      code: 200,
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          nickname: user.nickname || '管理员',
          avatar: user.avatar,
          role: user.role || 'admin'
        }
      }
    });
  } catch (error) {
    console.error('处理登录请求失败:', error);
    return res.status(500).json({
      code: 500,
      success: false,
      message: '登录处理失败',
      error: error.message
    });
  }
});

// 辅助函数：比较密码
async function comparePassword(plainPassword, hashedPassword) {
  try {
    // 检查是否使用了salt:hash格式
    if (hashedPassword && hashedPassword.includes(':')) {
      const [salt, hash] = hashedPassword.split(':');
      const testHash = crypto.pbkdf2Sync(plainPassword, salt, 1000, 64, 'sha256').toString('hex');
      return testHash === hash;
    } 
    // 如果使用了bcrypt等其他格式，可以在这里添加处理
    else if (hashedPassword && hashedPassword.startsWith('$2')) {
      // 假设是bcrypt格式，使用bcrypt库比较
      if (bcrypt) {
        return await bcrypt.compare(plainPassword, hashedPassword);
      }
    }
    
    // 如果不能识别密码格式，返回false
    return false;
  } catch (err) {
    console.error('密码比较失败:', err);
    return false;
  }
}

// 添加一个测试登录路由 - 不需要身份验证直接返回成功
app.post('/login-test', (req, res) => {
  console.log('收到测试登录请求');
  
  // 生成一个简单的令牌
  const token = 'test-token-' + Date.now();
  
  // 返回成功响应
  return res.json({
    code: 200,
    success: true,
    message: '测试登录成功',
    data: {
      token,
      user: {
        id: 999,
        username: 'test-user',
        nickname: '测试用户',
        avatar: null,
        role: 'admin'
      }
    }
  });
});

// 还添加一个GET方法的测试登录路由
app.get('/login-test', (req, res) => {
  console.log('收到GET测试登录请求');
  
  // 生成一个简单的令牌
  const token = 'test-token-' + Date.now();
  
  // 返回成功响应
  return res.json({
    code: 200,
    success: true,
    message: 'GET测试登录成功',
    data: {
      token,
      user: {
        id: 999,
        username: 'test-user',
        nickname: '测试用户',
        avatar: null,
        role: 'admin'
      }
    }
  });
});

// 微信验证文件存储配置
const wechatVerifyStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 将验证文件直接保存在网站根目录，不是在public子目录
    const dirPath = path.join(__dirname);
    
    // 确保目录存在
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    cb(null, dirPath);
  },
  filename: function (req, file, cb) {
    // 保持原始文件名
    cb(null, file.originalname);
  }
});

// 微信验证文件上传中间件
const uploadWechatVerify = multer({ 
  storage: wechatVerifyStorage,
  fileFilter: (req, file, cb) => {
    // 只接受txt文件
    if (!file.originalname.toLowerCase().endsWith('.txt')) {
      return cb(new Error('只允许上传txt文件'));
    }
    // 验证文件名格式，微信验证文件通常是MP_verify_开头
    if (!file.originalname.startsWith('MP_verify_')) {
      return cb(new Error('微信验证文件应该以MP_verify_开头'));
    }
    cb(null, true);
  },
  limits: {
    fileSize: 10240 // 限制文件大小10KB
  }
});

// 配置扣子私钥文件存储
const kouziKeyStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const dirPath = path.join(__dirname, 'certs');
    
    // 确保目录存在
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    cb(null, dirPath);
  },
  filename: function (req, file, cb) {
    // 生成私钥文件名
    const timestamp = Date.now();
    const filename = `private_${timestamp}.key`;
    cb(null, filename);
  }
});

// 扣子私钥文件上传中间件
const uploadKouziKey = multer({
  storage: kouziKeyStorage,
  fileFilter: (req, file, cb) => {
    // 接受常见的私钥文件格式
    const allowedExtensions = ['.key', '.pem', '.txt'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    if (!allowedExtensions.includes(fileExt)) {
      return cb(new Error('只允许上传.key、.pem或.txt格式的私钥文件'));
    }
    cb(null, true);
  },
  limits: {
    fileSize: 20480 // 限制文件大小20KB
  }
});

// 微信验证文件上传路由
app.post('/api/upload/wechat-verify', uploadWechatVerify.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        code: 400, 
        message: '未找到上传文件' 
      });
    }
    
    // 读取文件内容
    fs.readFile(req.file.path, 'utf8', (err, data) => {
      if (err) {
        return res.status(500).json({ 
          code: 500, 
          message: '读取文件失败', 
          error: err.message 
        });
      }
      
      // 更新配置数据中的验证文件信息
      if (configData['wechat-official']) {
        configData['wechat-official'].configValue.verifyFileName = req.file.filename;
        configData['wechat-official'].configValue.verifyFileContent = data.trim();
        // 保存到本地文件
        saveConfigToFile();
      }
      
      // 成功结果
      return res.json({
        code: 200,
        message: '文件上传成功',
        data: {
          filename: req.file.filename,
          content: data.trim()
        }
      });
    });
  } catch (error) {
    console.error('处理微信验证文件上传失败:', error);
    res.status(500).json({ 
      code: 500, 
      message: '上传处理失败', 
      error: error.message 
    });
  }
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// 添加ping端点
app.get('/ping', (req, res) => {
  res.json({
    status: 'ok',
    port: currentPort,
    timestamp: new Date().toISOString()
  });
});

app.get('/api/ping', (req, res) => {
  res.json({
    status: 'ok',
    port: currentPort,
    timestamp: new Date().toISOString()
  });
});

// 智能体分组数据
const agentGroups = [
  {
    id: 1,
    name: '通用助手',
    description: '日常问答和任务辅助',
    enabled: true,
    order: 1,
    config: {icon: 'chat', color: '#2080f0', features: ['聊天', '写作', '翻译']},
    createdAt: new Date('2025-06-05T08:00:00.000Z'),
    updatedAt: new Date('2025-06-05T08:30:00.000Z')
  },
  {
    id: 2,
    name: '创意设计',
    description: '创意生成和设计辅助',
    enabled: true,
    order: 2,
    config: {icon: 'palette', color: '#18a058', features: ['绘画', '设计', '创意']},
    createdAt: new Date('2025-06-05T08:05:00.000Z'),
    updatedAt: new Date('2025-06-05T08:35:00.000Z')
  },
  {
    id: 3,
    name: '开发助手',
    description: '代码编写和问题解决',
    enabled: true,
    order: 3,
    config: {icon: 'code', color: '#f0a020', features: ['编程', '调试', '文档']},
    createdAt: new Date('2025-06-05T08:10:00.000Z'),
    updatedAt: new Date('2025-06-05T08:40:00.000Z')
  }
];

// 模拟媒体分组数据
const mediaGroups = [
  {
    id: "group1",
    name: "默认分组",
    count: 5
  },
  {
    id: "group2",
    name: "产品图片",
    count: 3
  },
  {
    id: "group3",
    name: "宣传素材",
    count: 2
  }
];

// 模拟媒体列表数据
const mediaItems = [
  {
    id: "img1",
    name: "产品展示图1.jpg",
    url: "https://picsum.photos/800/600?random=1",
    thumbnailUrl: "https://picsum.photos/200/150?random=1",
    group: "group1",
    createTime: "2023-06-05T08:00:00.000Z",
    type: "image",
    size: 102400
  },
  {
    id: "img2",
    name: "产品展示图2.jpg",
    url: "https://picsum.photos/800/600?random=2",
    thumbnailUrl: "https://picsum.photos/200/150?random=2",
    group: "group1",
    createTime: "2023-06-06T08:00:00.000Z",
    type: "image",
    size: 120000
  },
  {
    id: "vid1",
    name: "宣传视频1.mp4",
    url: "https://example.com/videos/video1.mp4",
    thumbnailUrl: "https://picsum.photos/200/150?random=3",
    group: "group3",
    createTime: "2023-06-07T08:00:00.000Z",
    type: "video",
    size: 5242880,
    duration: 120
  }
];

// 模拟配置数据
let configData = {
  'wechat-official': {
    configType: 'wechat-official',
    description: 'WeChat Official Account',
    isConfigured: true,
    isActive: true,
    lastTestResult: true,
    lastTestTime: '2025-06-10T10:30:00.000Z',
    configValue: {
      appId: 'wxaa95c5ddaa112a17',
      appSecret: 'db4f69a0419a03197e1890f7b95a22c',
      token: 'PBHCuOxeYAqaVjUPgTVVheUcTfHAWnTu',
      encodingAESKey: 'S2aGUckOitdy35bVe37UDAOzYUcpto7MERhK40KEyXh',
      verifyFileName: 'MP_verify_MOkK4IowZD2dmNE1.txt',
      verifyFileContent: 'MQkT4Tow5D2dmB1',
      enabled: true
    }
  },
  'wechat-miniprogram': {
    configType: 'wechat-miniprogram',
    description: 'WeChat Mini Program',
    isConfigured: true,
    isActive: true,
    lastTestResult: false,
    lastTestTime: '2025-06-09T14:20:00.000Z',
    configValue: {
      appId: 'wx1a2b3c4d5e6f7g8h',
      appSecret: '1a2b3c4d5e6f7g8h1a2b3c4d5e6f7g8h',
      originalId: 'gh_abcdef123456',
      serverDomain: 'api.example.com'
    }
  },
  'wechat-pay': {
    configType: 'wechat-pay',
    description: 'WeChat Pay',
    isConfigured: false,
    isActive: false,
    lastTestResult: null,
    lastTestTime: null,
    configValue: {}
  },
  'alipay': {
    configType: 'alipay',
    description: 'Alipay',
    isConfigured: true,
    isActive: false,
    lastTestResult: null,
    lastTestTime: null,
    configValue: {
      appId: '2021000000000000',
      privateKey: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAA...',
      publicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A...'
    }
  },
  'kouzi': {
    configType: 'kouzi',
    description: 'Kouzi API',
    isConfigured: false,
    isActive: false,
    lastTestResult: null,
    lastTestTime: null,
    configValue: {}
  },
  'volcano-ark': {
    configType: 'volcano-ark',
    description: 'Volcano Ark',
    isConfigured: false,
    isActive: false,
    lastTestResult: null,
    lastTestTime: null,
    configValue: {}
  },
  'free-quota': {
    configType: 'free-quota',
    description: 'Free Quota',
    isConfigured: true,
    isActive: true,
    lastTestResult: true,
    lastTestTime: '2025-06-08T09:15:00.000Z',
    configValue: {
      newUserQuota: 100,
      dailyQuota: 10,
      enabled: true
    }
  },
  'referral': {
    configType: 'referral',
    description: 'Referral',
    isConfigured: false,
    isActive: false,
    lastTestResult: null,
    lastTestTime: null,
    configValue: {}
  },
  'system': {
    configType: 'system',
    description: 'System',
    isConfigured: true,
    isActive: true,
    lastTestResult: true,
    lastTestTime: '2025-06-07T16:45:00.000Z',
    configValue: {
      siteName: 'AI Agent Management System',
      siteUrl: 'https://example.com',
      adminEmail: '<EMAIL>',
      logoUrl: '/logo.png'
    }
  }
};

// 从本地文件加载配置
const loadConfigFromFile = () => {
  const configFile = path.join(__dirname, 'config-data.json');
  try {
    if (fs.existsSync(configFile)) {
      const data = fs.readFileSync(configFile, 'utf8');
      configData = JSON.parse(data);
      console.log('配置数据已从文件加载');
    } else {
      console.log('配置文件不存在，使用默认配置');
      saveConfigToFile();
    }
  } catch (error) {
    console.error('加载配置文件失败:', error);
  }
};

// 保存配置到本地文件
const saveConfigToFile = () => {
  const configFile = path.join(__dirname, 'config-data.json');
  try {
    fs.writeFileSync(configFile, JSON.stringify(configData, null, 2), 'utf8');
    console.log('配置数据已保存到文件');
  } catch (error) {
    console.error('保存配置文件失败:', error);
  }
};

// 获取所有配置状态
app.get('/api/configs/status/all', async (req, res) => {
  try {
    // 尝试从数据库获取所有配置
    const dbConfigs = await getAllConfigsFromDB();
    
    // 如果数据库中有配置，优先使用数据库配置
    if (dbConfigs && dbConfigs.length > 0) {
      console.log('从数据库获取所有配置状态');
      const configList = dbConfigs.map(dbConfig => ({
        configType: dbConfig.configType,
        description: dbConfig.description || `${dbConfig.configType}配置`,
        isConfigured: true,
        isActive: Boolean(dbConfig.isActive),
        lastTestResult: dbConfig.lastTestResult,
        lastTestTime: dbConfig.lastTestTime,
        configValue: dbConfig.configValue
      }));
      
      return res.json({
        code: 200,
        message: '获取配置状态成功',
        data: configList
      });
    }
    
    // 否则使用本地文件配置
    console.log('从本地文件获取所有配置状态');
    const configList = Object.values(configData);
    res.json({
      code: 200,
      message: '获取配置状态成功',
      data: configList
    });
  } catch (error) {
    console.error('获取配置状态失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取配置状态失败',
      data: null
    });
  }
});

// 测试配置连接
app.post('/api/configs/:configType/test', async (req, res) => {
  try {
    const { configType } = req.params;
    const testData = req.body;
    
    // 模拟测试连接过程
    const success = Math.random() > 0.3; // 70%的概率测试成功
    const now = new Date().toISOString();
    const testMessage = success ? '连接测试成功' : '连接测试失败，请检查配置';
    
    if (configData[configType]) {
      configData[configType].lastTestResult = success;
      configData[configType].lastTestTime = now;
      configData[configType].lastTestMessage = testMessage;
      
      // 保存到文件
      saveConfigToFile();
    }
    
    // 更新数据库中的测试结果
    try {
      const conn = await connectDB();
      await conn.query(`
        UPDATE config 
        SET lastTestResult = ?, lastTestTime = ?, lastTestMessage = ? 
        WHERE configType = ?
      `, [success ? 1 : 0, new Date(), testMessage, configType]);
      conn.release();
      console.log(`更新了数据库中的测试结果: ${configType}, 结果: ${success}`);
    } catch (dbError) {
      console.error('保存测试结果到数据库失败:', dbError);
    }
    
    setTimeout(() => {
      res.json({
        code: 200,
        message: '测试连接请求已处理',
        data: {
          success,
          message: testMessage,
          timestamp: now
        }
      });
    }, 1000); // 模拟1秒的网络延迟
  } catch (error) {
    console.error('测试连接失败:', error);
    res.status(500).json({
      code: 500,
      message: '测试连接失败',
      data: {
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString()
      }
    });
  }
});

// 扣子私钥上传接口
app.post('/api/configs/kouzi/private-key', uploadKouziKey.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        code: 400, 
        message: '未找到上传文件' 
      });
    }
    
    // 读取文件内容
    fs.readFile(req.file.path, 'utf8', async (err, data) => {
      if (err) {
        return res.status(500).json({ 
          code: 500, 
          message: '读取文件失败', 
          error: err.message 
        });
      }
      
      const privateKey = data.trim();
      
      // 更新扣子配置中的私钥信息
      if (!configData['kouzi']) {
        configData['kouzi'] = {
          configType: 'kouzi',
          description: '扣子服务配置',
          isConfigured: false,
          isActive: false,
          lastTestResult: null,
          lastTestTime: null,
          configValue: {}
        };
      }
      
      // 更新私钥字段
      configData['kouzi'].configValue.privateKey = privateKey;
      configData['kouzi'].configValue.privateKeyFile = req.file.filename;
      configData['kouzi'].configValue.privateKeyPath = req.file.path;
      
      // 保存到本地文件
      saveConfigToFile();
      
      // 同时保存到数据库
      try {
        console.log('正在保存扣子私钥到数据库');
        
        // 获取现有配置
        const existingConfig = await getConfigFromDB('kouzi');
        const configValue = existingConfig ? {...existingConfig.configValue} : {};
        
        // 更新私钥信息
        configValue.privateKey = privateKey;
        configValue.privateKeyFile = req.file.filename;
        configValue.privateKeyPath = req.file.path;
        
        // 保存更新后的配置
        await saveConfigToDB('kouzi', configValue);
        
      } catch (dbError) {
        console.error('保存私钥到数据库失败:', dbError);
      }
      
      // 成功结果
      return res.json({
        code: 200,
        message: '私钥文件上传成功',
        data: {
          privateKey: privateKey,
          filename: req.file.filename,
          filePath: req.file.path
        }
      });
    });
  } catch (error) {
    console.error('处理私钥文件上传失败:', error);
    res.status(500).json({ 
      code: 500, 
      message: '上传处理失败', 
      error: error.message 
    });
  }
});

// 媒体分组接口
app.get('/api/media/groups', (req, res) => {
  res.json(mediaGroups);
});

// 媒体列表接口
app.get('/api/media', (req, res) => {
  try {
    const page = parseInt(req.query.page || '1');
    const pageSize = parseInt(req.query.pageSize || '10');
    const group = req.query.group;
    const keyword = req.query.keyword;
    const type = req.query.type;
    
    let filteredItems = [...mediaItems];
    
    // 按类型筛选
    if (type) {
      filteredItems = filteredItems.filter(item => item.type === type);
    }
    
    // 按分组筛选
    if (group && group !== 'all' && group !== 'ungroup') {
      filteredItems = filteredItems.filter(item => item.group === group);
    } else if (group === 'ungroup') {
      filteredItems = filteredItems.filter(item => !item.group);
    }
    
    // 按关键词搜索
    if (keyword) {
      filteredItems = filteredItems.filter(item => 
        item.name.toLowerCase().includes(keyword.toLowerCase())
      );
    }
    
    // 计算分页
    const total = filteredItems.length;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const items = filteredItems.slice(startIndex, endIndex);
    
    res.json({
      items,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    });
  } catch (error) {
    console.error('处理媒体请求失败:', error);
    res.status(500).json({
      error: '处理请求失败',
      message: error.message
    });
  }
});

// 图片上传存储配置
const imageStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const dirPath = path.join(__dirname, 'public', 'uploads', 'images');

    // 确保目录存在
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    cb(null, dirPath);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const ext = path.extname(file.originalname);
    const filename = `img_${timestamp}_${randomStr}${ext}`;
    cb(null, filename);
  }
});

// 通用文件上传存储配置
const fileStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const dirPath = path.join(__dirname, 'public', 'uploads', 'files');

    // 确保目录存在
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    cb(null, dirPath);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const ext = path.extname(file.originalname);
    const baseName = path.basename(file.originalname, ext);
    const filename = `${baseName}_${timestamp}_${randomStr}${ext}`;
    cb(null, filename);
  }
});

// 图片上传中间件
const uploadImage = multer({
  storage: imageStorage,
  fileFilter: (req, file, cb) => {
    // 只接受图片文件
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('只允许上传图片文件 (JPEG, PNG, GIF, WebP)'));
    }
    cb(null, true);
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 限制文件大小10MB
  }
});

// 通用文件上传中间件
const uploadFile = multer({
  storage: fileStorage,
  fileFilter: (req, file, cb) => {
    // 允许的文件类型
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.pdf', '.doc', '.docx', '.txt', '.zip', '.rar'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    if (!allowedExtensions.includes(fileExt)) {
      return cb(new Error('不支持的文件类型'));
    }
    cb(null, true);
  },
  limits: {
    fileSize: 50 * 1024 * 1024 // 限制文件大小50MB
  }
});

// 图片上传API
app.post('/api/upload/image', uploadImage.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传文件'
      });
    }

    // 生成访问URL
    const baseUrl = USE_DOMAIN_MODE ? `https://${API_DOMAIN}` : `http://localhost:${PORT}`;
    const fileUrl = `${baseUrl}/uploads/images/${req.file.filename}`;

    console.log('图片上传成功:', {
      originalName: req.file.originalname,
      filename: req.file.filename,
      size: req.file.size,
      url: fileUrl
    });

    return res.json({
      code: 0,
      message: '图片上传成功',
      data: {
        url: fileUrl,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        type: 'image'
      }
    });
  } catch (error) {
    console.error('图片上传失败:', error);
    return res.status(500).json({
      code: 500,
      message: '图片上传失败',
      error: error.message
    });
  }
});

// 头像上传API
app.post('/api/members/upload-avatar', authMiddleware, uploadImage.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '未找到上传文件'
      });
    }

    // 生成访问URL
    const baseUrl = USE_DOMAIN_MODE ? `https://${API_DOMAIN}` : `http://localhost:${PORT}`;
    const avatarUrl = `${baseUrl}/uploads/images/${req.file.filename}`;

    console.log('头像上传成功:', {
      userId: req.user.id,
      originalName: req.file.originalname,
      filename: req.file.filename,
      size: req.file.size,
      avatarUrl: avatarUrl
    });

    // 更新数据库中的头像字段
    try {
      if (db) {
        // 如果有数据库连接，更新members表
        await db.execute(
          'UPDATE members SET avatar = ? WHERE id = ?',
          [avatarUrl, req.user.id]
        );
        console.log(`用户 ${req.user.id} 头像已更新到数据库`);
      }
    } catch (dbError) {
      console.error('更新数据库头像失败:', dbError);
      // 即使数据库更新失败，也返回成功，因为文件已经上传成功
    }

    return res.json({
      success: true,
      message: '头像上传成功',
      data: {
        avatarUrl: avatarUrl,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size
      }
    });

  } catch (error) {
    console.error('头像上传失败:', error);
    return res.status(500).json({
      success: false,
      message: '头像上传失败',
      error: error.message
    });
  }
});

// 扣子图片上传API - 将图片上传到扣子服务器
app.post('/api/upload/coze-image', uploadImage.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传文件'
      });
    }

    // 获取扣子配置
    const cozeConfig = configData['kouzi'] || configData['coze'];
    if (!cozeConfig || !cozeConfig.configValue || !cozeConfig.configValue.accessToken) {
      return res.status(500).json({
        code: 500,
        message: '扣子配置未完成，请先配置扣子API'
      });
    }

    const accessToken = cozeConfig.configValue.accessToken;

    // 准备上传到扣子的文件
    const formData = new FormData();
    const fileBuffer = fs.readFileSync(req.file.path);
    formData.append('file', fileBuffer, {
      filename: req.file.originalname,
      contentType: req.file.mimetype
    });

    console.log('开始上传图片到扣子:', {
      originalName: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    });

    // 上传到扣子API
    const cozeResponse = await axios.post('https://api.coze.cn/v1/files/upload', formData, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        ...formData.getHeaders()
      },
      timeout: 30000
    });

    console.log('扣子图片上传响应:', cozeResponse.data);

    if (cozeResponse.data.code === 0 && cozeResponse.data.data) {
      // 同时保存本地图片URL作为备用
      const baseUrl = USE_DOMAIN_MODE ? `https://${API_DOMAIN}` : `http://localhost:${PORT}`;
      const localImageUrl = `${baseUrl}/uploads/images/${req.file.filename}`;

      return res.json({
        code: 0,
        message: '图片上传成功',
        data: {
          file_id: cozeResponse.data.data.id, // 扣子文件ID
          url: localImageUrl, // 本地图片URL
          filename: req.file.filename,
          originalName: req.file.originalname,
          size: req.file.size,
          type: 'image',
          coze_file_info: cozeResponse.data.data // 扣子返回的完整文件信息
        }
      });
    } else {
      throw new Error(cozeResponse.data.msg || '扣子图片上传失败');
    }

  } catch (error) {
    console.error('扣子图片上传失败:', error);

    // 如果扣子上传失败，返回本地图片信息作为备用
    const baseUrl = USE_DOMAIN_MODE ? `https://${API_DOMAIN}` : `http://localhost:${PORT}`;
    const localImageUrl = `${baseUrl}/uploads/images/${req.file.filename}`;

    return res.json({
      code: 1, // 使用code 1表示部分成功（本地上传成功，扣子上传失败）
      message: '图片已保存到本地，但上传到扣子失败',
      data: {
        url: localImageUrl,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        type: 'image',
        error: error.message
      }
    });
  }
});

// 通用文件上传API
app.post('/api/upload/file', uploadFile.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传文件'
      });
    }

    // 生成访问URL
    const baseUrl = USE_DOMAIN_MODE ? `https://${API_DOMAIN}` : `http://localhost:${PORT}`;
    const fileUrl = `${baseUrl}/uploads/files/${req.file.filename}`;

    console.log('文件上传成功:', {
      originalName: req.file.originalname,
      filename: req.file.filename,
      size: req.file.size,
      url: fileUrl
    });

    return res.json({
      code: 0,
      message: '文件上传成功',
      data: {
        url: fileUrl,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        type: 'file'
      }
    });
  } catch (error) {
    console.error('文件上传失败:', error);
    return res.status(500).json({
      code: 500,
      message: '文件上传失败',
      error: error.message
    });
  }
});

// 扣子文件上传API - 将文件上传到扣子服务器
app.post('/api/upload/coze-file', uploadFile.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: '未找到上传文件'
      });
    }

    // 获取扣子配置
    const cozeConfig = configData['kouzi'] || configData['coze'];
    if (!cozeConfig || !cozeConfig.configValue || !cozeConfig.configValue.accessToken) {
      return res.status(500).json({
        code: 500,
        message: '扣子配置未完成，请先配置扣子API'
      });
    }

    const accessToken = cozeConfig.configValue.accessToken;

    // 准备上传到扣子的文件
    const formData = new FormData();
    const fileBuffer = fs.readFileSync(req.file.path);
    formData.append('file', fileBuffer, {
      filename: req.file.originalname,
      contentType: req.file.mimetype
    });

    console.log('开始上传文件到扣子:', {
      originalName: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    });

    // 上传到扣子API
    const cozeResponse = await axios.post('https://api.coze.cn/v1/files/upload', formData, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        ...formData.getHeaders()
      },
      timeout: 30000
    });

    console.log('扣子文件上传响应:', cozeResponse.data);

    if (cozeResponse.data.code === 0 && cozeResponse.data.data) {
      // 同时保存本地文件URL作为备用
      const baseUrl = USE_DOMAIN_MODE ? `https://${API_DOMAIN}` : `http://localhost:${PORT}`;
      const localFileUrl = `${baseUrl}/uploads/files/${req.file.filename}`;

      return res.json({
        code: 0,
        message: '文件上传成功',
        data: {
          file_id: cozeResponse.data.data.id, // 扣子文件ID
          url: localFileUrl, // 本地文件URL
          filename: req.file.filename,
          originalName: req.file.originalname,
          size: req.file.size,
          type: 'file',
          coze_file_info: cozeResponse.data.data // 扣子返回的完整文件信息
        }
      });
    } else {
      throw new Error(cozeResponse.data.msg || '扣子文件上传失败');
    }

  } catch (error) {
    console.error('扣子文件上传失败:', error);

    // 如果扣子上传失败，返回本地文件信息作为备用
    const baseUrl = USE_DOMAIN_MODE ? `https://${API_DOMAIN}` : `http://localhost:${PORT}`;
    const localFileUrl = `${baseUrl}/uploads/files/${req.file.filename}`;

    return res.json({
      code: 1, // 使用code 1表示部分成功（本地上传成功，扣子上传失败）
      message: '文件已保存到本地，但上传到扣子失败',
      data: {
        url: localFileUrl,
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        type: 'file',
        error: error.message
      }
    });
  }
});

// 媒体上传接口
app.post('/api/media/upload', (req, res) => {
  try {
    console.log('接收到上传请求:', req.body);
    // 由于这是模拟API，不处理实际文件，直接返回成功响应
    const timestamp = Date.now();
    const id = `upload_${timestamp}`;

    // 获取请求中的参数
    const type = req.body.type || 'image';
    const groupId = req.body.groupId || 'group1';
    
    // 创建一个新的媒体项并返回
    const newMedia = {
      id: id,
      name: `新上传${type === 'image' ? '图片' : '视频'}_${timestamp}.${type === 'image' ? 'jpg' : 'mp4'}`,
      url: type === 'image' 
        ? `https://picsum.photos/800/600?random=${Math.floor(Math.random() * 100)}` 
        : `https://example.com/videos/video${Math.floor(Math.random() * 10) + 1}.mp4`,
      thumbnailUrl: `https://picsum.photos/200/150?random=${Math.floor(Math.random() * 100)}`,
      group: groupId,
      createTime: new Date().toISOString(),
      type: type,
      size: type === 'image' ? Math.floor(Math.random() * 500000) + 50000 : Math.floor(Math.random() * 10000000) + 1000000
    };
    
    // 添加到媒体列表
    mediaItems.push(newMedia);
    
    // 返回成功响应
    res.status(201).json(newMedia);
  } catch (error) {
    console.error('处理上传请求失败:', error);
    res.status(500).json({ error: '上传失败', message: error.message });
  }
});

// 智能体分组接口
app.get('/api/agent-groups', (req, res) => {
  res.json({
    code: 200,
    data: {
      list: agentGroups,
      pagination: {
        current: 1,
        pageSize: 10,
        total: agentGroups.length
      }
    },
    message: '获取智能体分组列表成功'
  });
});

// 获取单个分组
app.get('/api/agent-groups/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const group = agentGroups.find(g => g.id === id);
  
  if (group) {
    res.json({
      code: 200,
      data: group,
      message: '获取智能体分组详情成功'
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '智能体分组不存在'
    });
  }
});

// 模拟套餐数据
const packageData = [
  {
    id: 1,
    title: '基础套餐',
    price: 99.00,
    duration: 30,
    totalQuota: 100,
    dailyMaxConsumption: 10,
    description: '适合个人使用的基础AI套餐',
    tags: '基础,入门',
    enabled: true,
    frontendDisplay: true,
    order: 1,
    createdAt: '2025-06-01T00:00:00.000Z',
    updatedAt: '2025-06-01T00:00:00.000Z'
  },
  {
    id: 2,
    title: '标准套餐',
    price: 199.00,
    duration: 30,
    totalQuota: 300,
    dailyMaxConsumption: 30,
    description: '适合专业用户的标准AI套餐',
    tags: '标准,专业',
    enabled: true,
    frontendDisplay: true,
    order: 2,
    createdAt: '2025-06-01T00:00:00.000Z',
    updatedAt: '2025-06-01T00:00:00.000Z'
  },
  {
    id: 3,
    title: '高级套餐',
    price: 299.00,
    duration: 30,
    totalQuota: 1000,
    dailyMaxConsumption: 100,
    description: '适合企业用户的高级AI套餐',
    tags: '高级,企业',
    enabled: true,
    frontendDisplay: true,
    order: 3,
    createdAt: '2025-06-01T00:00:00.000Z',
    updatedAt: '2025-06-01T00:00:00.000Z'
  }
];

// 套餐列表接口
app.get('/api/packages', (req, res) => {
  try {
    res.json({
      code: 200,
      data: {
        list: packageData,
        pagination: {
          current: 1,
          pageSize: 10,
          total: packageData.length
        }
      },
      message: '获取套餐列表成功'
    });
  } catch (error) {
    console.error('处理套餐请求失败:', error);
    res.status(500).json({
      code: 500,
      message: '处理请求失败',
      error: error.message
    });
  }
});

// 智能体套餐列表接口（用于会员管理）
app.get('/api/agent-packages', (req, res) => {
  try {
    res.json({
      code: 200,
      data: packageData,
      message: '获取套餐列表成功'
    });
  } catch (error) {
    console.error('处理智能体套餐请求失败:', error);
    res.status(500).json({
      code: 500,
      message: '处理请求失败',
      error: error.message
    });
  }
});

// 整合兑换码路由
app.use('/api', redemptionCodeRoutes);
// 同时在根路径上挂载路由，以兼容前端直接请求/redemption-codes的情况
app.use('/', redemptionCodeRoutes);

// 路由已在文件顶部导入

// 注册API路由
app.use('/api/user', userRoutes);
app.use('/api/agent', agentRoutes);
app.use('/api/tasks', taskRoutes); // 修改为复数形式以匹配前端调用
app.use('/api/configs', configRoutes); // 注册配置路由，使用复数形式

// 会员相关API处理
app.get('/api/members', async (req, res) => {
  try {
    // 查询数据库或返回模拟数据
    const mockMembers = [
      {
        id: 1,
        name: 'admin',
        username: 'admin',
        nickname: '管理员',
        phone: '***********',
        mobile: '***********',
        email: '<EMAIL>',
        points: 99950,
        balance: 0,
        createdAt: '2023-06-01',
        status: true,
        isPartner: false,
        accountType: 'account',
        packageId: 2,
        packageInfo: {
          id: 2,
          title: '标准套餐',
          price: 199.00
        }
      },
      {
        id: 2,
        name: 'test',
        username: 'test',
        nickname: '测试用户',
        phone: '***********',
        mobile: '***********',
        email: '<EMAIL>',
        points: 500,
        balance: 50,
        createdAt: '2023-06-02',
        status: true,
        isPartner: false,
        accountType: 'account',
        packageId: 1,
        packageInfo: {
          id: 1,
          title: '基础套餐',
          price: 99.00
        }
      },
      {
        id: 3,
        name: '5988',
        username: '5988',
        nickname: '',
        phone: '***********',
        mobile: '***********',
        email: '<EMAIL>',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'account',
        packageId: null,
        packageInfo: null
      }
    ];
    
    res.json({
      code: 200,
      message: '获取会员列表成功',
      data: mockMembers
    });
  } catch (error) {
    console.error('获取会员列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取会员列表失败',
      error: error.message
    });
  }
});

// 获取会员详情
app.get('/api/members/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 这里应该是从数据库查询，这里使用模拟数据
    const mockMembers = [
      {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        mobile: '***********',
        points: 99950,
        balance: 0,
        createdAt: '2023-06-01',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 2,
        username: 'test',
        nickname: '测试用户',
        mobile: '***********',
        points: 500,
        balance: 50,  // 修复：将默认100改为50
        createdAt: '2023-06-02',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 3,
        username: '5988',
        nickname: '',
        mobile: '***********',
        email: '<EMAIL>',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'account'
      }
    ];
    
    const member = mockMembers.find(m => m.id === parseInt(id));
    
    if (!member) {
      return res.status(404).json({
        code: 404,
        message: '会员不存在'
      });
    }
    
    res.json({
      code: 200,
      message: '获取会员详情成功',
      data: member
    });
  } catch (error) {
    console.error('获取会员详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取会员详情失败',
      error: error.message
    });
  }
});

// 创建会员的额外端点
app.post('/api/members/create', async (req, res) => {
  try {
    // 转发到原有端点逻辑
    const memberData = req.body;
    console.log('创建会员数据:', memberData);
    
    // 生成ID - 实际中应该是由数据库自动生成
    const mockId = 4; // 假设已有3个会员
    
    // 组装新会员对象
    const newMember = {
      id: mockId,
      ...memberData,
      points: 0,  // 默认点数为0
      balance: 0,  // 默认余额为0
      createdAt: new Date().toISOString().split('T')[0]
    };
    
    res.status(201).json({
      code: 201,
      message: '会员创建成功',
      data: newMember
    });
  } catch (error) {
    console.error('创建会员失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建会员失败',
      error: error.message
    });
  }
});

// 为了解决ts:258行问题 - 资产更新的备用端点
app.put('/api/members/:id/updateAssets', async (req, res) => {
  try {
    // 转发到原有端点逻辑
    const { id } = req.params;
    const { points, balance } = req.body;
    
    console.log(`(备用)更新会员${id}的资产 - 点数:${points}, 余额:${balance}`);
    
    // 这里应该是更新数据库，这里使用模拟数据
    const mockMembers = [
      {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        mobile: '***********',
        points: 99950,
        balance: 0,
        createdAt: '2023-06-01',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 2,
        username: 'test',
        nickname: '测试用户',
        mobile: '***********',
        points: 500,
        balance: 50,
        createdAt: '2023-06-02',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 3,
        username: '5988',
        nickname: '',
        mobile: '***********',
        email: '<EMAIL>',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 4,
        username: 'qew323',
        nickname: '',
        mobile: 'qew323',
        email: '223',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'wechat'
      }
    ];
    
    const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id));
    
    if (memberIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '会员不存在'
      });
    }
    
    // 更新资产
    mockMembers[memberIndex].points = Number(points);
    mockMembers[memberIndex].balance = Number(balance);
    
    res.json({
      code: 200,
      message: '会员资产更新成功',
      data: mockMembers[memberIndex]
    });
  } catch (error) {
    console.error('更新会员资产失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新会员资产失败',
      error: error.message
    });
  }
});

// 为了解决ts:148行问题 - 会员信息更新的备用端点
app.put('/api/members/:id/updateProfile', async (req, res) => {
  try {
    // 转发到原有端点逻辑
    const { id } = req.params;
    const profileData = req.body;
    
    console.log(`(备用)更新会员${id}的资料:`, profileData);
    
    // 这里应该是更新数据库，这里使用模拟数据
    const mockMembers = [
      {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        mobile: '***********',
        points: 99950,
        balance: 0,
        createdAt: '2023-06-01',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 2,
        username: 'test',
        nickname: '测试用户',
        mobile: '***********',
        points: 500,
        balance: 50,
        createdAt: '2023-06-02',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 3,
        username: '5988',
        nickname: '',
        mobile: '***********',
        email: '<EMAIL>',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 4,
        username: 'qew323',
        nickname: '',
        mobile: 'qew323',
        email: '223',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'wechat'
      }
    ];
    
    const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id));
    
    if (memberIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '会员不存在'
      });
    }
    
    // 更新资料，但不更新资产字段
    const { points, balance, ...otherFields } = profileData;
    Object.assign(mockMembers[memberIndex], otherFields);
    
    res.json({
      code: 200,
      message: '会员资料更新成功',
      data: mockMembers[memberIndex]
    });
  } catch (error) {
    console.error('更新会员资料失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新会员资料失败',
      error: error.message
    });
  }
});

// 额外的保存会员端点 - 兼容直接使用id的PUT请求
app.put('/api/members/:id', async (req, res) => {
  try {
    // 转发到原有端点逻辑
    const { id } = req.params;
    const profileData = req.body;
    
    console.log(`(直接端点)更新会员${id}的资料:`, profileData);
    
    // 这里应该是更新数据库，这里使用模拟数据
    const mockMembers = [
      {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        mobile: '***********',
        points: 99950,
        balance: 0,
        createdAt: '2023-06-01',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 2,
        username: 'test',
        nickname: '测试用户',
        mobile: '***********',
        points: 500,
        balance: 50,
        createdAt: '2023-06-02',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 3,
        username: '5988',
        nickname: '',
        mobile: '***********',
        email: '<EMAIL>',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 4,
        username: 'qew323',
        nickname: '',
        mobile: 'qew323',
        email: '223',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'wechat'
      }
    ];
    
    const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id));
    
    if (memberIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '会员不存在'
      });
    }
    
    // 更新资料，但不更新资产字段
    const { points, balance, ...otherFields } = profileData;
    Object.assign(mockMembers[memberIndex], otherFields);
    
    res.json({
      code: 200,
      message: '会员资料更新成功',
      data: mockMembers[memberIndex]
    });
  } catch (error) {
    console.error('更新会员资料失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新会员资料失败',
      error: error.message
    });
  }
});

// 修复会员余额问题
app.post('/api/fix-member-balances', async (req, res) => {
  try {
    console.log('执行修复会员余额操作');

    // 这里应该是修复数据库中的数据，这里使用模拟响应
    res.json({
      code: 200,
      message: '会员余额修复成功',
      data: { fixed: true }
    });
  } catch (error) {
    console.error('修复会员余额失败:', error);
    res.status(500).json({
      code: 500,
      message: '修复会员余额失败',
      error: error.message
    });
  }
});

// 管理员调整会员点数
app.post('/api/admin/members/:id/points/adjust', async (req, res) => {
  try {
    const { id } = req.params;
    const { type, amount, reason } = req.body;

    console.log(`调整会员${id}的点数 - 类型:${type}, 数量:${amount}, 原因:${reason}`);

    // 验证参数
    if (!type || !amount || !reason) {
      return res.status(400).json({
        code: 400,
        message: '参数不完整',
        success: false
      });
    }

    if (!['increase', 'decrease'].includes(type)) {
      return res.status(400).json({
        code: 400,
        message: '操作类型无效',
        success: false
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        code: 400,
        message: '调整数量必须大于0',
        success: false
      });
    }

    // 模拟会员数据
    const mockMembers = [
      {
        id: 1,
        username: 'user1',
        nickname: '用户1',
        mobile: '***********',
        email: '<EMAIL>',
        points: 1000,
        balance: 500.00,
        createdAt: '2024-01-01',
        status: true,
        isPartner: false,
        accountType: 'mobile'
      },
      {
        id: 2,
        username: 'user2',
        nickname: '用户2',
        mobile: '***********',
        email: '<EMAIL>',
        points: 800,
        balance: 300.00,
        createdAt: '2024-01-02',
        status: true,
        isPartner: true,
        accountType: 'email'
      },
      {
        id: 3,
        username: 'user3',
        nickname: '用户3',
        mobile: '***********',
        email: '<EMAIL>',
        points: 1200,
        balance: 800.00,
        createdAt: '2024-01-03',
        status: false,
        isPartner: false,
        accountType: 'wechat'
      },
      {
        id: 4,
        username: 'qew323',
        nickname: '',
        mobile: 'qew323',
        email: '223',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'wechat'
      }
    ];

    const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id));

    if (memberIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '会员不存在',
        success: false
      });
    }

    const member = mockMembers[memberIndex];
    const currentPoints = member.points || 0;

    // 检查余额是否足够（减少时）
    if (type === 'decrease' && currentPoints < amount) {
      return res.status(400).json({
        code: 400,
        message: '点数不足，无法减少',
        success: false
      });
    }

    // 计算新的点数
    const adjustAmount = type === 'increase' ? amount : -amount;
    const newPoints = currentPoints + adjustAmount;

    // 更新会员点数
    mockMembers[memberIndex].points = newPoints;

    console.log(`点数调整成功 - 会员${id}: ${currentPoints} -> ${newPoints}`);

    res.json({
      code: 200,
      message: `点数${type === 'increase' ? '增加' : '减少'}成功`,
      success: true,
      data: {
        memberId: parseInt(id),
        type,
        amount,
        reason,
        pointsBefore: currentPoints,
        pointsAfter: newPoints,
        member: mockMembers[memberIndex]
      }
    });
  } catch (error) {
    console.error('调整会员点数失败:', error);
    res.status(500).json({
      code: 500,
      message: '调整会员点数失败',
      success: false,
      error: error.message
    });
  }
});

// 管理员调整会员余额
app.post('/api/admin/members/:id/balance/adjust', async (req, res) => {
  try {
    const { id } = req.params;
    const { type, amount, reason } = req.body;

    console.log(`调整会员${id}的余额 - 类型:${type}, 金额:${amount}, 原因:${reason}`);

    // 验证参数
    if (!type || !amount || !reason) {
      return res.status(400).json({
        code: 400,
        message: '参数不完整',
        success: false
      });
    }

    if (!['increase', 'decrease'].includes(type)) {
      return res.status(400).json({
        code: 400,
        message: '操作类型无效',
        success: false
      });
    }

    if (amount <= 0) {
      return res.status(400).json({
        code: 400,
        message: '调整金额必须大于0',
        success: false
      });
    }

    // 模拟会员数据
    const mockMembers = [
      {
        id: 1,
        username: 'user1',
        nickname: '用户1',
        mobile: '***********',
        email: '<EMAIL>',
        points: 1000,
        balance: 500.00,
        createdAt: '2024-01-01',
        status: true,
        isPartner: false,
        accountType: 'mobile'
      },
      {
        id: 2,
        username: 'user2',
        nickname: '用户2',
        mobile: '***********',
        email: '<EMAIL>',
        points: 800,
        balance: 300.00,
        createdAt: '2024-01-02',
        status: true,
        isPartner: true,
        accountType: 'email'
      },
      {
        id: 3,
        username: 'user3',
        nickname: '用户3',
        mobile: '***********',
        email: '<EMAIL>',
        points: 1200,
        balance: 800.00,
        createdAt: '2024-01-03',
        status: false,
        isPartner: false,
        accountType: 'wechat'
      },
      {
        id: 4,
        username: 'qew323',
        nickname: '',
        mobile: 'qew323',
        email: '223',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'wechat'
      }
    ];

    const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id));

    if (memberIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '会员不存在',
        success: false
      });
    }

    const member = mockMembers[memberIndex];
    const currentBalance = member.balance || 0;

    // 检查余额是否足够（减少时）
    if (type === 'decrease' && currentBalance < amount) {
      return res.status(400).json({
        code: 400,
        message: '余额不足，无法减少',
        success: false
      });
    }

    // 计算新的余额
    const adjustAmount = type === 'increase' ? amount : -amount;
    const newBalance = currentBalance + adjustAmount;

    // 更新会员余额
    mockMembers[memberIndex].balance = Number(newBalance.toFixed(2));

    console.log(`余额调整成功 - 会员${id}: ${currentBalance} -> ${newBalance}`);

    res.json({
      code: 200,
      message: `余额${type === 'increase' ? '增加' : '减少'}成功`,
      success: true,
      data: {
        memberId: parseInt(id),
        type,
        amount,
        reason,
        balanceBefore: currentBalance,
        balanceAfter: newBalance,
        member: mockMembers[memberIndex]
      }
    });
  } catch (error) {
    console.error('调整会员余额失败:', error);
    res.status(500).json({
      code: 500,
      message: '调整会员余额失败',
      success: false,
      error: error.message
    });
  }
});

// 统计接口
app.get('/api/members/stats/today', (req, res) => {
  res.json({
    code: 200,
    message: '获取今日注册会员数量成功',
    data: { count: 0 }
  });
});

app.get('/api/members/stats/active', (req, res) => {
  res.json({
    code: 200,
    message: '获取活跃会员数量成功',
    data: { count: 0 }
  });
});

app.get('/api/members/stats/partners', (req, res) => {
  res.json({
    code: 200,
    message: '获取合伙人数量成功',
    data: { count: 0 }
  });
});

// 添加额外的API端点以解决前端调用问题
// 这些端点是为了兼容前端可能使用的不同路径名称

// 会员资产更新端点 - 兼容前端path为/assets的请求
app.put('/api/members/:id/assets', async (req, res) => {
  try {
    const { id } = req.params;
    const { points, balance } = req.body;
    
    console.log(`更新会员${id}的资产 - 点数:${points}, 余额:${balance}`);
    
    // 这里应该是更新数据库，这里使用模拟数据
    const mockMembers = [
      {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        mobile: '***********',
        points: 99950,
        balance: 0,
        createdAt: '2023-06-01',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 2,
        username: 'test',
        nickname: '测试用户',
        mobile: '***********',
        points: 500,
        balance: 50,
        createdAt: '2023-06-02',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 3,
        username: '5988',
        nickname: '',
        mobile: '***********',
        email: '<EMAIL>',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 4,
        username: 'qew323',
        nickname: '',
        mobile: 'qew323',
        email: '223',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'wechat'
      }
    ];
    
    const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id));
    
    if (memberIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '会员不存在'
      });
    }
    
    // 更新资产
    mockMembers[memberIndex].points = Number(points);
    mockMembers[memberIndex].balance = Number(balance);
    
    res.json({
      code: 200,
      message: '会员资产更新成功',
      data: mockMembers[memberIndex]
    });
  } catch (error) {
    console.error('更新会员资产失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新会员资产失败',
      error: error.message
    });
  }
});

// 会员资料更新端点 - 兼容前端path为/profile的请求
app.put('/api/members/:id/profile', async (req, res) => {
  try {
    const { id } = req.params;
    const profileData = req.body;
    
    console.log(`更新会员${id}的资料:`, profileData);
    
    // 这里应该是更新数据库，这里使用模拟数据
    const mockMembers = [
      {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        mobile: '***********',
        points: 99950,
        balance: 0,
        createdAt: '2023-06-01',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 2,
        username: 'test',
        nickname: '测试用户',
        mobile: '***********',
        points: 500,
        balance: 50,
        createdAt: '2023-06-02',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 3,
        username: '5988',
        nickname: '',
        mobile: '***********',
        email: '<EMAIL>',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 4,
        username: 'qew323',
        nickname: '',
        mobile: 'qew323',
        email: '223',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'wechat'
      }
    ];
    
    const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id));
    
    if (memberIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '会员不存在'
      });
    }
    
    // 更新资料，但不更新资产字段
    const { points, balance, ...otherFields } = profileData;
    Object.assign(mockMembers[memberIndex], otherFields);
    
    res.json({
      code: 200,
      message: '会员资料更新成功',
      data: mockMembers[memberIndex]
    });
  } catch (error) {
    console.error('更新会员资料失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新会员资料失败',
      error: error.message
    });
  }
});

// 额外的保存会员端点 - 兼容直接使用id的PUT请求
app.put('/api/members/:id', async (req, res) => {
  try {
    // 转发到原有端点逻辑
    const { id } = req.params;
    const profileData = req.body;
    
    console.log(`(直接端点)更新会员${id}的资料:`, profileData);
    
    // 这里应该是更新数据库，这里使用模拟数据
    const mockMembers = [
      {
        id: 1,
        username: 'admin',
        nickname: '管理员',
        mobile: '***********',
        points: 99950,
        balance: 0,
        createdAt: '2023-06-01',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 2,
        username: 'test',
        nickname: '测试用户',
        mobile: '***********',
        points: 500,
        balance: 50,
        createdAt: '2023-06-02',
        status: 'active',
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 3,
        username: '5988',
        nickname: '',
        mobile: '***********',
        email: '<EMAIL>',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'account'
      },
      {
        id: 4,
        username: 'qew323',
        nickname: '',
        mobile: 'qew323',
        email: '223',
        points: 0,
        balance: 0,
        createdAt: '2025-07-01',
        status: true,
        isPartner: false,
        accountType: 'wechat'
      }
    ];
    
    const memberIndex = mockMembers.findIndex(m => m.id === parseInt(id));
    
    if (memberIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: '会员不存在'
      });
    }
    
    // 更新资料，但不更新资产字段
    const { points, balance, ...otherFields } = profileData;
    Object.assign(mockMembers[memberIndex], otherFields);
    
    res.json({
      code: 200,
      message: '会员资料更新成功',
      data: mockMembers[memberIndex]
    });
  } catch (error) {
    console.error('更新会员资料失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新会员资料失败',
      error: error.message
    });
  }
});

// ====== Coze API代理路由 ======

// 获取扣子配置辅助函数
async function getCozeConfig() {
  try {
    // 查询单独的配置记录 - 兼容旧版
    const kouziConfig = await getConfigFromDB('kouzi');
    if (kouziConfig && kouziConfig.configValue) {
      return typeof kouziConfig.configValue === 'string' 
        ? JSON.parse(kouziConfig.configValue) 
        : kouziConfig.configValue;
    }
    
    // 如果没有旧版配置，查询system_config表的coze类型记录
    try {
      const [rows] = await pool.query('SELECT * FROM system_config WHERE type = ?', ['coze']);
      if (rows && rows.length > 0) {
        console.log(`找到 ${rows.length} 条coze配置记录`);
        
        // 将记录组装为对象格式
        const config = {};
        rows.forEach(row => {
          if (row.key === 'enabled') {
            config[row.key] = row.value === 'true';
          } else if (row.key === 'appId') {
            config['clientId'] = row.value; // 映射为clientId
          } else if (row.key === 'appSecret') {
            config['clientSecret'] = row.value; // 映射为clientSecret
          } else {
            config[row.key] = row.value;
          }
        });
        
        return config;
      }
    } catch (dbError) {
      console.error('查询system_config表失败:', dbError);
    }
    
    return null;
  } catch (error) {
    console.error('获取扣子配置失败:', error);
    return null;
  }
}

// 1. OAuth令牌代理
app.post('/api/proxy/oauth/token', async (req, res) => {
  try {
    console.log('代理OAuth请求:', req.body);

    // 调试用，直接返回模拟令牌
    console.log('调试模式：返回模拟OAuth令牌');
    return res.status(200).json({
      access_token: 'mock_token_' + Date.now(),
      token_type: 'bearer',
      expires_in: 3600
    });
    
    /* 
    // 原始实现，暂时注释掉
    const config = await getCozeConfig();
    if (!config) {
      return res.status(500).json({
        success: false,
        message: '扣子配置不存在或无效'
      });
    }
    
    // 从配置中获取认证信息
    const clientId = config.appId || config.app_id;
    const clientSecret = config.appSecret || config.app_secret;
    
    if (!clientId || !clientSecret) {
      return res.status(500).json({
        success: false,
        message: '扣子配置中缺少客户端ID或密钥'
      });
    }
    
    // 准备请求数据
    const tokenRequestData = {
      ...req.body, // 保留原始请求中的所有参数
      client_id: clientId,
      client_secret: clientSecret,
      grant_type: req.body.grant_type || 'client_credentials'
    };
    
    console.log('发送OAuth请求到Coze:', COZE_AUTH_BASE_URL);
    
    // 调用Coze OAuth API
    const response = await axios.post(COZE_AUTH_BASE_URL, tokenRequestData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('OAuth响应成功, 状态码:', response.status);
    
    // 返回代理结果
    return res.status(response.status).json(response.data);
    */
    
  } catch (error) {
    console.error('代理OAuth请求失败:', error.response?.data || error.message);
    return res.status(error.response?.status || 500).json({
      success: false,
      message: '代理OAuth请求失败',
      error: error.response?.data || error.message
    });
  }
});

// 2. 获取已发布机器人列表代理 - 简化版本，无异步代码
app.get('/api/proxy/bots', (req, res) => {
  console.log('简化版：代理获取机器人列表请求');
  
  // 直接返回响应，无需任何异步操作
  return res.status(200).json({
    success: true,
    items: [
      {
        bot_id: '7491699969922318376',
        name: '智能体测试',
        description: '这是一个测试智能体，用于调试代理API',
        avatar_url: 'https://localhost:5173/images/default-bot.png'
      }
    ]
  });
});

// 3. 聊天请求代理
app.post('/api/proxy/chat', async (req, res) => {
  try {
    console.log('代理聊天请求:', req.body);
    
    // 调试用，返回模拟回复，不检查认证
    console.log('调试模式：返回模拟聊天回复');
    
    // 从请求中提取用户消息
    let userMessage = "您好";
    if (req.body && req.body.messages && req.body.messages.length > 0) {
      const lastMessage = req.body.messages[req.body.messages.length - 1];
      if (lastMessage.role === 'user') {
        userMessage = lastMessage.content;
      }
    }
    
    // 返回模拟回复
    return res.status(200).json({
      id: "chatcmpl-" + Date.now(),
      object: "chat.completion",
      created: Math.floor(Date.now() / 1000),
      model: "gpt-3.5-turbo",
      choices: [
        {
          index: 0,
          message: {
            role: "assistant",
            content: `您好！我收到了您的消息："${userMessage}"。这是一个测试回复，实际API尚未连接。`
          },
          finish_reason: "stop"
        }
      ],
      usage: {
        prompt_tokens: 10,
        completion_tokens: 20,
        total_tokens: 30
      }
    });
    
    /* 
    // 原始实现，暂时注释掉
    const config = await getCozeConfig();
    if (!config || !config.accessToken) {
      return res.status(500).json({
        success: false,
        message: '扣子配置不存在或无访问令牌'
      });
    }
    
    const botId = req.body.botId || config.botId;
    if (!botId) {
      return res.status(400).json({
        success: false,
        message: '未指定机器人ID'
      });
    }
    
    // 调用Coze API发送聊天消息
    const response = await axios.post(`${COZE_API_BASE_URL}/bot/${botId}/chat/completions`, req.body, {
      headers: {
        'Authorization': `Bearer ${config.accessToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('聊天请求响应成功');
    
    // 返回代理结果
    return res.status(response.status).json(response.data);
    */
    
  } catch (error) {
    console.error('代理聊天请求失败:', error.response?.data || error.message);
    return res.status(error.response?.status || 500).json({
      success: false,
      message: '代理聊天请求失败',
      error: error.response?.data || error.message
    });
  }
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在'
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: err.message
  });
});

// 更新状态文件
const updateStatusFile = () => {
  // 根据当前模式决定URL格式
  const serverUrl = USE_DOMAIN_MODE ? `https://${API_DOMAIN}` : `http://localhost:${PORT}`;
  
  const status = {
    running: true,
    port: PORT,
    url: serverUrl,
    mode: USE_DOMAIN_MODE ? 'domain' : 'port',
    timestamp: new Date().toISOString()
  };
  
  fs.writeFileSync(
    path.join(__dirname, 'server-status.json'), 
    JSON.stringify(status, null, 2)
  );
  
  fs.writeFileSync(
    path.join(__dirname, 'public', 'server-status.json'), 
    JSON.stringify(status, null, 2)
  );
};

// 启动服务器
async function startServer() {
  try {
    console.log('正在启动服务器...');
    
    // 初始化配置数据库
    console.log('初始化配置数据库...');
    await initConfigDatabase();
    
    // 初始化用户数据库
    console.log('初始化用户数据库...');
    await initUserDatabase();

    // 初始化上传目录
    console.log('初始化上传目录...');
    initUploadDirectories();

    // 启动HTTP服务器
    const server = http.createServer(app);
    server.listen(PORT, () => {
      console.log(`API服务器已启动，运行在端口 ${PORT}`);
      console.log(`当前模式: ${USE_DOMAIN_MODE ? '域名模式' : '端口模式'}`);
      if (USE_DOMAIN_MODE) {
        console.log(`API域名: ${API_DOMAIN}`);
        console.log(`前端域名: ${FRONTEND_DOMAIN}`);
      } else {
        console.log(`API地址: http://localhost:${PORT}`);
        console.log(`前端地址: http://localhost:${process.env.FRONTEND_PORT || 5173}`);
      }
      
      // 更新状态文件
      updateStatusFile();
    });
  } catch (error) {
    console.error('启动服务器时出错:', error);
    process.exit(1);
  }
}

// 启动服务器
startServer().catch(err => {
  console.error('启动服务器失败:', err);
});

// 获取指定类型的配置
app.get('/api/configs/:configType', async (req, res) => {
  try {
    const { configType } = req.params;
    console.log(`获取配置: ${configType}`);
    
    // 特殊处理coze类型配置
    if (configType === 'coze') {
      const [rows] = await pool.query('SELECT * FROM system_config WHERE type = ?', ['coze']);
      
      if (rows.length === 0) {
        console.log('未找到coze配置，返回默认值');
        return res.json({
          code: 200,
          success: true,
          message: '未找到配置',
          data: []
        });
      }
      
      console.log(`找到 ${rows.length} 条coze配置记录`);
      
      // 直接返回所有记录
      return res.json({
        code: 200,
        success: true,
        message: '获取配置成功',
        data: rows.map(row => ({
          id: row.id,
          type: row.type,
          key: row.key,
          value: row.value,
          description: row.description
        }))
      });
    }
    
    // 处理其他类型配置
    // ... existing code ...
  } catch (error) {
    console.error('获取配置失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取配置失败',
      error: error.message
    });
  }
});