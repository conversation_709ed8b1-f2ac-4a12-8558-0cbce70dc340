<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化VIP套餐弹窗演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
        }

        /* 现代化VIP套餐弹窗样式 */
        .modern-package-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modern-package-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modern-package-modal-container {
            background: #ffffff;
            border-radius: 24px;
            width: 100%;
            max-width: 480px;
            max-height: 85vh;
            box-shadow: 0 32px 64px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            margin: 20px;
            transform: scale(0.9) translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modern-package-modal-overlay.show .modern-package-modal-container {
            transform: scale(1) translateY(0);
        }

        .modern-package-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .modern-package-modal-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%); }
            50% { transform: translateX(-50%) translateY(-50%); }
        }

        .header-content {
            flex: 1;
            position: relative;
            z-index: 1;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: white;
            display: block;
            margin-bottom: 4px;
        }

        .modal-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 300;
        }

        .close-button {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            border: none;
            color: white;
            position: relative;
            z-index: 2;
            cursor: pointer;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .close-icon {
            font-size: 20px;
            font-weight: 300;
            line-height: 1;
        }

        .modern-package-modal-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .modern-package-modal-content::-webkit-scrollbar {
            width: 6px;
        }

        .modern-package-modal-content::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .modern-package-modal-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        /* 现代化套餐列表 */
        .modern-package-list {
            padding: 24px;
        }

        /* VIP套餐卡片 */
        .vip-package-card {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 24px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(255, 234, 167, 0.3);
        }

        .vip-package-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .vip-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .package-title-section {
            flex: 1;
            margin-right: 16px;
        }

        .package-name {
            font-size: 20px;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 8px;
            display: block;
        }

        .package-price {
            font-size: 32px;
            font-weight: 800;
            color: #e17055;
            display: flex;
            align-items: baseline;
            gap: 4px;
        }

        .price-symbol {
            font-size: 20px;
        }

        .price-value {
            font-size: 32px;
        }

        /* VIP卡片主体 */
        .vip-card-body {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 16px;
            position: relative;
            z-index: 1;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .info-label {
            font-size: 12px;
            color: #636e72;
            font-weight: 500;
        }

        .info-value {
            font-size: 14px;
            color: #2d3436;
            font-weight: 600;
        }

        .info-value.highlight {
            color: #667eea;
            font-weight: 700;
        }

        .info-value.remaining {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
        }

        .daily-limit-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .limit-label {
            font-size: 14px;
            color: #636e72;
            font-weight: 500;
        }

        .limit-value {
            font-size: 14px;
            color: #2d3436;
            font-weight: 600;
        }

        /* 状态标识 */
        .status-badge {
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
            margin-top: 4px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: inline-block;
            width: auto;
            min-width: auto;
        }

        .status-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            animation: pulse 2s ease-in-out infinite;
        }

        .status-active::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shine 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            }
            50% {
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.5);
            }
        }

        @keyframes shine {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        .status-expired {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
        }

        /* 历史套餐区域 */
        .history-section {
            margin-top: 24px;
        }

        .history-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3436;
            margin-bottom: 16px;
            display: block;
        }

        .package-card.history {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .package-card.history:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .package-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .detail-label {
            font-size: 14px;
            color: #636e72;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #2d3436;
            font-weight: 600;
        }

        /* 弹窗底部 */
        .modern-package-modal-footer {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 16px 24px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
        }

        .footer-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .footer-text {
            font-size: 16px;
            color: #667eea;
            font-weight: 600;
        }

        .footer-desc {
            font-size: 12px;
            color: #718096;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .modern-package-modal-container {
                margin: 20px;
                max-width: calc(100vw - 40px);
            }
            
            .modern-package-list {
                padding: 16px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .vip-card-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .package-price {
                font-size: 24px;
            }
            
            .price-value {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <button class="demo-button" onclick="showModal()">
        ✨ 查看现代化VIP套餐
    </button>

    <!-- 现代化VIP套餐弹窗 -->
    <div id="vipModal" class="modern-package-modal-overlay" onclick="closeModal()">
        <div class="modern-package-modal-container" onclick="event.stopPropagation()">
            <!-- 头部 -->
            <div class="modern-package-modal-header">
                <div class="header-content">
                    <div class="modal-title">✨ 我的VIP套餐</div>
                    <div class="modal-subtitle">My VIP Packages</div>
                </div>
                <button class="close-button" onclick="closeModal()">
                    <span class="close-icon">×</span>
                </button>
            </div>

            <!-- 内容区域 -->
            <div class="modern-package-modal-content">
                <div class="modern-package-list">
                    <!-- 当前使用中的套餐 -->
                    <div class="vip-package-card active">
                        <div class="vip-card-header">
                            <div class="package-title-section">
                                <span class="package-name">专业版套餐</span>
                                <div class="status-badge status-active">
                                    <span class="status-text">使用中</span>
                                </div>
                            </div>
                            <div class="package-price">
                                <span class="price-symbol">¥</span>
                                <span class="price-value">99.00</span>
                            </div>
                        </div>
                        
                        <div class="vip-card-body">
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">开通时间</span>
                                    <span class="info-value">2025-07-30 15:38</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">到期时间</span>
                                    <span class="info-value">2025-08-30 15:38</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">总点数</span>
                                    <span class="info-value highlight">10,000点</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">剩余天数</span>
                                    <span class="info-value remaining">31天</span>
                                </div>
                            </div>
                            
                            <div class="daily-limit-section">
                                <span class="limit-label">每日使用限制</span>
                                <span class="limit-value">500点/天</span>
                            </div>
                        </div>
                    </div>

                    <!-- 历史套餐 -->
                    <div class="history-section">
                        <span class="history-title">历史套餐</span>
                        <div class="package-card history">
                            <div class="card-header">
                                <div class="package-info">
                                    <span class="package-name">标准版套餐</span>
                                    <div class="status-badge status-expired">
                                        <span class="status-text">已过期</span>
                                    </div>
                                </div>
                                <span class="package-price">¥59.00</span>
                            </div>
                            
                            <div class="card-details">
                                <div class="detail-row">
                                    <span class="detail-label">使用期间</span>
                                    <span class="detail-value">2024-12-15 - 2025-01-14</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">套餐配额</span>
                                    <span class="detail-value">5,000点 (无限制)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 弹窗底部 -->
            <div class="modern-package-modal-footer">
                <div class="footer-content">
                    <span class="footer-text">💎 享受VIP专属服务</span>
                    <span class="footer-desc">如有疑问请联系客服</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showModal() {
            const modal = document.getElementById('vipModal');
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            const modal = document.getElementById('vipModal');
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
