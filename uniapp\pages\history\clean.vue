<template>
	<view class="container">
		<view class="header">
			<text class="title">往档 - 创作记录</text>
		</view>

		<view class="content">
			<!-- 状态显示 -->
			<view class="status-bar">
				<text>{{ statusMessage }}</text>
			</view>

			<!-- 任务类型筛选标签 -->
			<view class="type-filter">
				<view
					class="filter-item"
					:class="{ active: activeFilter === 'all' }"
					@click="switchFilter('all')"
				>
					<text class="filter-text">全部</text>
				</view>
				<view
					class="filter-item"
					:class="{ active: activeFilter === 'agent' }"
					@click="switchFilter('agent')"
				>
					<text class="filter-text">智能体</text>
				</view>
				<view
					class="filter-item"
					:class="{ active: activeFilter === 'workflow' }"
					@click="switchFilter('workflow')"
				>
					<text class="filter-text">工作流</text>
				</view>
			</view>

			<!-- 去生成精彩内容按钮 -->
			<view class="generate-button" @click="goToGenerate">
				<text class="generate-text">去生成精彩内容</text>
			</view>
			
			<!-- 任务历史列表 -->
			<view class="history-list">
				<view v-if="isLoadingTasks" class="loading-message">
					<text>正在加载任务...</text>
				</view>

				<view v-else-if="taskRecords.length === 0" class="empty-message">
					<text>暂无创作记录</text>
				</view>
				
				<view v-else>
					<view v-for="(item, index) in taskRecords" :key="item.id" class="history-item" @click="viewTaskDetail(item)">
						<view class="item-header">
							<view class="item-avatar">
								<text class="avatar-text">{{ getTaskIcon(item.taskType) }}</text>
							</view>
							<view class="item-info">
								<text class="item-title">{{ item.taskTypeDetail || item.taskType }}</text>
								<text class="item-time">{{ item.createTime }}</text>
							</view>
							<view class="task-status" :style="getStatusStyle(item.status)">
								{{ item.status }}
							</view>
						</view>
						<view class="item-content">
							<view class="task-details">
								<view class="task-input" v-if="item.input">
									<text class="detail-label">输入：</text>
									<text class="detail-text">{{ item.input }}</text>
								</view>
								<view class="task-output" v-if="item.output">
									<text class="detail-label">输出：</text>
									<text class="detail-text">{{ item.output }}</text>
								</view>
								<view class="task-meta">
									<text class="meta-item">{{ item.taskType }}</text>
									<text class="meta-item">{{ item.taskSource }}</text>
									<text class="meta-item" v-if="item.consumption && item.consumption.tokens > 0">{{ item.consumption.tokens }} tokens</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 加载更多 -->
				<view v-if="taskPagination.hasMore && !isLoadingTasks" class="load-more" @click="loadMoreTasks">
					<text class="load-more-text">加载更多</text>
				</view>
			</view>
		</view>
		
		<!-- 魔法导航栏 -->
		<MagicNavigation
			:current="2"
			:items="navItems"
			@change="onNavChange"
		/>

		<!-- 智能登录弹窗 -->
		<smart-login-modal
			:visible="showLoginModalFlag"
			@close="hideLoginModal"
			@login-success="handleLoginSuccess"
		></smart-login-modal>
	</view>
</template>

<script>
import MagicNavigation from '@/components/MagicNavigation.vue'
import SmartLoginModal from '@/components/smart-login-modal.vue'
import { userStore } from '@/api/members.js'
import tasksApi from '@/api/tasks.js'

export default {
	name: 'HistoryClean',
	components: {
		MagicNavigation,
		SmartLoginModal
	},
	data() {
		return {
			statusMessage: '初始化中...', // 状态消息
			activeFilter: 'all', // 当前选中的任务类型筛选
			taskRecords: [], // 任务记录列表
			isLoadingTasks: false, // 任务加载状态
			taskPagination: { // 任务分页
				page: 1,
				pageSize: 20,
				total: 0,
				hasMore: true
			},
			taskStats: { // 任务统计信息
				totalTasks: 0,
				completedTasks: 0,
				weeklyTokens: 0,
				totalTokens: 0,
				remainingTokens: 0
			},
			// 导航配置
			navItems: [
				{ text: '初页', icon: 'icon-home', path: '/pages/index/index' },
				{ text: '会享', icon: 'icon-message', path: '/pages/chat/index' },
				{ text: '往档', icon: 'icon-history', path: '/pages/history/index' },
				{ text: '吾界', icon: 'icon-user', path: '/pages/profile/index' }
			],
			// 登录相关状态
			showLoginModalFlag: false, // 控制登录弹窗显示
			isLoggedIn: false // 用户登录状态
		}
	},
	onLoad() {
		this.statusMessage = '页面加载完成';
		console.log('往档页面加载');
		try {
			// 检查登录状态
			this.checkLoginStatus();
			// 加载任务历史
			this.loadTaskHistory();
		} catch (error) {
			console.error('页面初始化失败:', error);
			this.statusMessage = '初始化失败: ' + error.message;
		}
	},
	onShow() {
		try {
			// 页面显示时重新检查登录状态
			this.checkLoginStatus();
			// 刷新任务历史
			this.refreshTaskHistory();
		} catch (error) {
			console.error('页面显示失败:', error);
		}
	},
	methods: {
		// 导航变化处理
		onNavChange(index) {
			console.log('导航变化:', index);
		},

		// 切换任务类型筛选
		switchFilter(filter) {
			try {
				this.activeFilter = filter;
				this.statusMessage = `切换到: ${filter}`;
				console.log('切换任务类型筛选:', filter);
				// 重新加载任务历史
				this.refreshTaskHistory();
			} catch (error) {
				console.error('切换筛选失败:', error);
				this.statusMessage = '切换失败: ' + error.message;
			}
		},

		// 刷新任务历史
		refreshTaskHistory() {
			this.taskPagination.page = 1;
			this.taskPagination.hasMore = true;
			this.taskRecords = [];
			this.loadTaskHistory();
		},

		// 去生成精彩内容
		goToGenerate() {
			console.log('去生成精彩内容');
			// 检查登录状态，未登录则弹出登录弹窗
			if (!this.isLoggedIn) {
				console.log('用户未登录，显示登录弹窗');
				this.showLoginModal();
				return;
			}
			// 跳转到首页
			uni.navigateTo({
				url: '/pages/index/index'
			});
		},

		// 检查登录状态
		checkLoginStatus() {
			this.isLoggedIn = userStore.isLoggedIn();
			console.log('当前登录状态:', this.isLoggedIn);
		},

		// 显示登录弹窗
		showLoginModal() {
			this.showLoginModalFlag = true;
		},

		// 隐藏登录弹窗
		hideLoginModal() {
			this.showLoginModalFlag = false;
		},

		// 登录成功处理
		handleLoginSuccess(memberData) {
			console.log('登录成功:', memberData);
			// 刷新登录状态
			this.checkLoginStatus();
			// 隐藏登录弹窗
			this.hideLoginModal();
			// 显示成功提示
			uni.showToast({
				title: '登录成功',
				icon: 'success'
			});
			// 重新加载任务历史
			this.refreshTaskHistory();
		},

		// 加载任务历史
		async loadTaskHistory() {
			if (this.isLoadingTasks || !this.taskPagination.hasMore) {
				return;
			}

			try {
				this.isLoadingTasks = true;
				console.log('加载任务历史，页码:', this.taskPagination.page, '筛选:', this.activeFilter);

				// 构建请求参数
				const params = {
					page: this.taskPagination.page,
					pageSize: this.taskPagination.pageSize
				};

				// 根据任务类型筛选添加参数
				if (this.activeFilter === 'agent') {
					params.type = 'agent'; // 智能体
				} else if (this.activeFilter === 'workflow') {
					params.type = 'workflow'; // 工作流
				}

				// 调用API获取任务历史
				const response = await tasksApi.getTasks(params);

				if (response.success && response.data) {
					const { tasks, pagination, stats } = response.data;

					// 格式化任务记录
					const formattedRecords = tasksApi.formatTaskRecords(tasks);

					// 如果是第一页，替换数据；否则追加数据
					if (this.taskPagination.page === 1) {
						this.taskRecords = formattedRecords;
					} else {
						this.taskRecords.push(...formattedRecords);
					}

					// 更新分页信息
					this.taskPagination = {
						...this.taskPagination,
						total: pagination.total,
						hasMore: this.taskPagination.page * this.taskPagination.pageSize < pagination.total
					};

					// 更新统计信息
					if (stats) {
						this.taskStats = stats;
					}

					console.log('任务历史加载成功:', formattedRecords.length, '条记录');
				} else {
					throw new Error(response.message || '获取任务历史失败');
				}

				// 设置默认的空数据
				this.taskRecords = [];
				this.taskPagination.total = 0;
				this.taskStats = {
					totalTasks: 0,
					completedTasks: 0,
					weeklyTokens: 0,
					totalTokens: 0,
					remainingTokens: 0
				};

				console.log('使用默认空数据');
			} catch (error) {
				console.error('加载任务历史失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.isLoadingTasks = false;
			}
		},

		// 加载更多任务历史
		async loadMoreTasks() {
			if (this.taskPagination.hasMore && !this.isLoadingTasks) {
				this.taskPagination.page++;
				await this.loadTaskHistory();
			}
		},

		// 查看任务详情
		viewTaskDetail(taskItem) {
			try {
				console.log('查看任务详情:', taskItem);
				this.statusMessage = '查看任务详情';

				// 构建详情内容
				let content = `任务类型：${taskItem.taskType || '未知'}\n来源：${taskItem.taskSource || '未知'}\n状态：${taskItem.status || '未知'}`;

				if (taskItem.input) {
					content += `\n\n输入：${taskItem.input}`;
				}

				if (taskItem.output) {
					content += `\n\n输出：${taskItem.output}`;
				}

				if (taskItem.consumption && taskItem.consumption.tokens > 0) {
					content += `\n\n消耗：${taskItem.consumption.tokens} tokens`;
				}

				// 显示任务详情弹窗
				uni.showModal({
					title: '任务详情',
					content: content,
					showCancel: false,
					confirmText: '关闭'
				});
			} catch (error) {
				console.error('查看详情失败:', error);
				this.statusMessage = '查看详情失败: ' + error.message;
			}
		},

		// 获取任务图标
		getTaskIcon(taskType) {
			const iconMap = {
				'智能体': '🤖',
				'工作流': '⚙️',
				'文本助手': '📝',
				'代码助手': '💻',
				'图像助手': '🎨'
			};
			return iconMap[taskType] || '📋';
		},

		// 获取任务状态样式
		getStatusStyle(status) {
			return tasksApi.getStatusStyle(status);
		}
	}
}
</script>

<style scoped>
.container {
	width: 100%;
	min-height: 100vh;
	padding: 0 0 90px 0;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
}

.header {
	padding: 20px;
	background: #fff;
	text-align: left;
}

.title {
	font-size: 24px;
	font-weight: bold;
	color: #333;
}

.content {
	flex: 1;
	padding: 0 20px 20px 20px;
}

.status-bar {
	background: #e8f5e8;
	padding: 10px;
	margin-bottom: 20px;
	border-radius: 5px;
	font-size: 14px;
	color: #333;
}

/* 任务类型筛选标签 */
.type-filter {
	display: flex;
	justify-content: center;
	gap: 20px;
	margin-bottom: 30px;
}

.filter-item {
	padding: 8px 20px;
	border-radius: 20px;
	background: #f5f5f5;
	transition: all 0.3s ease;
}

.filter-item.active {
	background: #333;
	color: #fff;
}

.filter-text {
	font-size: 14px;
	color: #666;
}

.filter-item.active .filter-text {
	color: #fff;
}

/* 去生成精彩内容按钮 */
.generate-button {
	background: linear-gradient(135deg, #4A90E2 0%, #50E3C2 100%);
	color: #fff;
	padding: 15px;
	border-radius: 10px;
	text-align: center;
	margin-bottom: 20px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.generate-text {
	font-size: 16px;
	font-weight: 500;
}

/* 历史记录列表 */
.history-list {
	background: #fff;
	border-radius: 10px;
	overflow: hidden;
}

.loading-message, .empty-message {
	text-align: center;
	padding: 40px 20px;
	color: #666;
	font-size: 16px;
}

.history-item {
	padding: 15px;
	border-bottom: 1px solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.history-item:hover {
	background-color: #f8f9fa;
}

.history-item:last-child {
	border-bottom: none;
}

.item-header {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.item-avatar {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
}

.avatar-text {
	font-size: 18px;
}

.item-info {
	flex: 1;
}

.item-title {
	font-weight: bold;
	font-size: 16px;
	color: #333;
	margin-bottom: 4px;
}

.item-time {
	font-size: 12px;
	color: #999;
}

.task-status {
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	color: white;
}

.item-content {
	margin-left: 52px;
}

.task-details {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}

.task-input, .task-output {
	margin-bottom: 8px;
}

.detail-label {
	font-weight: bold;
	color: #333;
}

.detail-text {
	margin-left: 8px;
}

.task-meta {
	margin-top: 8px;
	font-size: 12px;
	color: #999;
	display: flex;
	gap: 10px;
}

.meta-item {
	background: #f0f0f0;
	padding: 2px 6px;
	border-radius: 3px;
}

.load-more {
	text-align: center;
	padding: 15px;
	color: #007AFF;
	cursor: pointer;
}

.load-more-text {
	font-size: 14px;
}
</style>
