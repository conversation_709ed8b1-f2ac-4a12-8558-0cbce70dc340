<template>
  <n-message-provider>
    <div class="members-container">
      <!-- 统计卡片 -->
      <div class="cards-section">
        <n-grid :cols="4" :x-gap="16" class="stat-cards">
          <n-grid-item>
            <n-tooltip placement="top" trigger="hover">
              <template #trigger>
                <div class="stat-card-container">
                  <div class="icon-container">
                    <n-icon size="20">
                      <PeopleCircleOutline />
                    </n-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ members.length }}</div>
                    <div class="stat-label">会员总数</div>
                  </div>
                </div>
              </template>
              <span>当前平台会员总数量</span>
            </n-tooltip>
          </n-grid-item>
          
          <n-grid-item>
            <n-tooltip placement="top" trigger="hover">
              <template #trigger>
                <div class="stat-card-container">
                  <div class="icon-container green">
                    <n-icon size="20">
                      <CalendarOutline />
                    </n-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ todayRegistrations }}</div>
                    <div class="stat-label">今日注册</div>
                  </div>
                </div>
              </template>
              <span>今天新注册的会员数量</span>
            </n-tooltip>
          </n-grid-item>
          
          <n-grid-item>
            <n-tooltip placement="top" trigger="hover">
              <template #trigger>
                <div class="stat-card-container">
                  <div class="icon-container blue">
                    <n-icon size="20">
                      <CheckmarkCircleOutline />
                    </n-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ activeMembers }}</div>
                    <div class="stat-label">活跃会员</div>
                  </div>
                </div>
              </template>
              <span>当前平台活跃会员数量</span>
            </n-tooltip>
          </n-grid-item>
          
          <n-grid-item>
            <n-tooltip placement="top" trigger="hover">
              <template #trigger>
                <div class="stat-card-container">
                  <div class="icon-container purple">
                    <n-icon size="20">
                      <GitNetworkOutline />
                    </n-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ partnerCount }}</div>
                    <div class="stat-label">合伙人数</div>
                  </div>
                </div>
              </template>
              <span>平台当前合伙人数量</span>
            </n-tooltip>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 主要内容卡片 -->
      <n-card class="main-table-card" title="会员列表">
        <template #header-extra>
          <n-space>
            <n-input v-model:value="searchKeyword" placeholder="输入ID精确匹配或账号/邮箱模糊搜索" clearable class="search-input">
              <template #prefix>
                <n-icon><SearchOutline /></n-icon>
              </template>
            </n-input>
            <n-button @click="handleSearch" type="primary" ghost>搜索</n-button>
            <n-button @click="clearSearch" v-if="searchKeyword || filteredMembers.length !== members.length">重置</n-button>
            <n-button type="primary" @click="handleAddMember">
              <template #icon>
                <n-icon><AddOutline /></n-icon>
              </template>
              添加会员
            </n-button>
          </n-space>
        </template>

        <div v-if="filteredMembers.length > 0 && filteredMembers.length !== members.length" class="search-result-tip">
          <n-icon size="16" color="#1890ff"><SearchOutline /></n-icon>
          <span>找到 {{ filteredMembers.length }} 个匹配会员</span>
          <n-button text type="primary" @click="clearSearch">显示全部</n-button>
        </div>

        <n-data-table
          :columns="columns"
          :data="filteredMembers"
          :pagination="pagination"
          :bordered="false"
          :striped="true"
          class="compact-table"
        >
          <template #empty>
            <div class="empty-state">
              <n-icon size="48" color="#d9d9d9"><SearchOutline /></n-icon>
              <p>未找到匹配的会员</p>
            </div>
          </template>
        </n-data-table>
      </n-card>

      <!-- 添加/编辑会员弹窗 -->
      <n-modal
        v-model:show="showAddMemberModal"
        preset="card"
        title="会员信息"
        :style="{ width: '500px', maxWidth: '90vw' }"
        :mask-closable="false"
        class="member-info-modal"
      >
        <n-form ref="formRef" :model="memberForm" :rules="rules" label-placement="left" label-width="100" label-align="right">
          <n-form-item label="手机号" path="phone">
            <n-input v-model:value="memberForm.phone" placeholder="请输入手机号" />
          </n-form-item>
          <n-form-item label="邮箱" path="email">
            <n-input v-model:value="memberForm.email" placeholder="请输入邮箱（可选）" />
          </n-form-item>
          <n-form-item label="密码" path="password">
            <n-input v-model:value="memberForm.password" type="password" placeholder="请输入密码（留空则使用默认密码123456）" />
          </n-form-item>
          <n-form-item label="账户类型" path="accountType">
            <n-select v-model:value="memberForm.accountType" :options="accountTypeOptions" />
          </n-form-item>
          <n-form-item label="会员套餐" path="packageId">
            <n-select v-model:value="memberForm.packageId" :options="memberPackages" placeholder="请选择会员套餐" />
            <!-- 调试信息 -->
            <div style="font-size: 12px; color: #666; margin-top: 4px;">
              调试: 套餐选项数量: {{ memberPackages.length }}, 数据: {{ JSON.stringify(memberPackages) }}
            </div>
          </n-form-item>
          <n-form-item label="注册时间" path="createdAt">
            <n-date-picker v-model:value="memberForm.createdAt" type="datetime" clearable />
          </n-form-item>
          <n-form-item label="状态" path="status">
            <n-switch v-model:value="memberForm.status" />
          </n-form-item>
          <n-form-item label="合伙人" path="isPartner">
            <n-switch v-model:value="memberForm.isPartner" />
          </n-form-item>
        </n-form>

        <template #footer>
          <div class="modal-footer">
            <n-button @click="showAddMemberModal = false">取消</n-button>
            <n-button type="primary" @click="handleSaveMember">保存</n-button>
          </div>
        </template>
      </n-modal>

      <!-- 删除确认框 -->
      <n-modal
        v-model:show="showDeleteConfirm"
        preset="dialog"
        title="确认删除"
        content="确定要删除该会员吗？此操作不可撤销。"
        positive-text="确认"
        negative-text="取消"
        @positive-click="confirmDelete"
        @negative-click="cancelDelete"
      />

      <!-- 推广统计弹窗 -->
      <n-modal
        v-model:show="showPromotionStatsModal"
        preset="card"
        title="推广统计详情"
        :style="{ width: '700px' }"
        :mask-closable="true"
      >
        <div v-if="currentPromoter" class="promotion-stats-container">
          <div class="promoter-info">
            <h3>推广员信息</h3>
            <div>ID: {{ currentPromoter.id }}</div>
            <div>账户: {{ currentPromoter.phone }}</div>
          </div>
          
          <div class="stats-cards">
            <div class="stats-card primary">
              <div class="stats-card-header">
                <n-icon size="24" class="stats-icon"><PeopleCircleOutline /></n-icon>
                <h3>一级推广</h3>
              </div>
              <div class="stats-card-value">{{ firstLevelReferrals.length }}人</div>
              <div class="stats-card-list">
                <div v-if="firstLevelReferrals.length === 0" class="empty-tip">暂无推广用户</div>
                <div v-for="user in firstLevelReferrals" :key="user.id" class="user-item">
                  <n-tooltip placement="top">
                    <template #trigger>
                      <div class="user-id">{{ user.id }}</div>
                    </template>
                    {{ user.name }} ({{ user.phone }})
                  </n-tooltip>
                </div>
              </div>
            </div>
            
            <div class="stats-card secondary">
              <div class="stats-card-header">
                <n-icon size="24" class="stats-icon"><GitNetworkOutline /></n-icon>
                <h3>二级推广</h3>
              </div>
              <div class="stats-card-value">{{ secondLevelReferrals.length }}人</div>
              <div class="stats-card-list">
                <div v-if="secondLevelReferrals.length === 0" class="empty-tip">暂无推广用户</div>
                <div v-for="user in secondLevelReferrals" :key="user.id" class="user-item">
                  <n-tooltip placement="top">
                    <template #trigger>
                      <div class="user-id">{{ user.id }}</div>
                    </template>
                    {{ user.name }} ({{ user.phone }})
                  </n-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <template #footer>
          <div class="modal-footer">
            <n-button @click="showPromotionStatsModal = false">关闭</n-button>
          </div>
        </template>
      </n-modal>

      <!-- 点数管理弹窗 -->
      <n-modal
        v-model:show="showPointsManageModal"
        preset="card"
        :style="{ width: '500px' }"
        :mask-closable="false"
        class="points-modal"
      >
        <template #header>
          <div class="modal-custom-header">
            <span class="modal-title">点数管理</span>
          </div>
        </template>

        <div v-if="currentPointsMember" class="points-manage-container">
          <!-- 会员信息和当前点数 -->
          <div class="top-section">
            <div class="member-info">
              <div class="member-avatar">
                <span>{{ currentPointsMember.name.charAt(0) }}</span>
              </div>
              <div class="member-details">
                <div class="member-name">{{ currentPointsMember.name }}</div>
                <div class="member-id">ID: {{ currentPointsMember.id }}</div>
              </div>
            </div>
            
            <div class="points-card">
              <div class="points-label">当前点数</div>
              <div class="points-value">
                <span class="points-number">{{ currentPointsMember.points }}</span>
                <span class="points-unit">点</span>
              </div>
            </div>
          </div>

          <!-- 点数操作 -->
          <div class="operation-section">
            <div class="points-input-box">
              <div class="input-label">调整点数</div>
              <div class="input-with-buttons">
                <n-button 
                  quaternary 
                  circle 
                  class="control-button"
                  @click="decreaseInputValue"
                  :disabled="!pointsAdjustValue || pointsAdjustValue <= 1"
                >
                  <n-icon size="18"><RemoveOutline /></n-icon>
                </n-button>
                
                <n-input
                  v-model:value="pointsAdjustValue"
                  placeholder="输入点数"
                  class="points-input"
                />
                
                <n-button 
                  quaternary 
                  circle 
                  class="control-button"
                  @click="increaseInputValue"
                >
                  <n-icon size="18"><AddOutline /></n-icon>
                </n-button>
              </div>
            </div>
            
            <n-input
              v-model:value="pointsAdjustRemark"
              placeholder="操作备注（可选）"
              class="remark-input"
            />
          </div>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <n-button 
              class="increase-button action-button"
              @click="handleIncreasePoints"
              :disabled="!pointsAdjustValue || pointsAdjustValue <= 0"
            >
              <n-icon class="button-icon" size="18"><AddCircleOutline /></n-icon>
              <span>增加点数</span>
            </n-button>
            
            <n-button 
              class="decrease-button action-button"
              @click="handleDecreasePoints"
              :disabled="!pointsAdjustValue || pointsAdjustValue <= 0"
            >
              <n-icon class="button-icon" size="18"><RemoveCircleOutline /></n-icon>
              <span>扣除点数</span>
            </n-button>
          </div>
        </div>

        <template #footer>
          <div class="modal-footer">
            <n-button quaternary size="large" @click="showPointsManageModal = false">关闭</n-button>
          </div>
        </template>
      </n-modal>

      <!-- 资产配置弹窗 -->
      <n-modal
        v-model:show="showAssetsConfigModal"
        preset="card"
        title="资产配置"
        :style="{ width: '500px' }"
        :mask-closable="false"
      >
        <div class="assets-config-container">
          <!-- 会员信息和当前资产 -->
          <div class="member-assets-header">
            <div class="member-basic-info">
              <span class="member-name">{{ currentAssetsMember?.name || currentAssetsMember?.phone || '未命名会员' }}</span>
              <span class="member-id">(ID: {{ currentAssetsMember?.id }})</span>
            </div>
            <div class="current-assets">
              <span class="asset-info">点数: <strong>{{ formatPoints(currentAssetsMember?.points || 0) }}</strong></span>
              <span class="asset-info">余额: <strong>¥{{ formatBalance(currentAssetsMember?.balance || 0) }}</strong></span>
            </div>
          </div>

          <!-- 操作标签页 -->
          <n-tabs type="line" animated>
            <n-tab-pane name="points" tab="点数调整">
              <n-form
                ref="pointsFormRef"
                :model="pointsAdjustForm"
                label-placement="left"
                label-width="60"
                class="compact-form"
              >
                <div class="operation-tip">
                  <n-text depth="3" style="font-size: 12px;">
                    输入正数增加点数，输入负数减少点数（如：+100 或 -50）
                  </n-text>
                </div>

                <n-form-item label="数量" path="amount">
                  <n-input-number
                    v-model:value="pointsAdjustForm.amount"
                    size="small"
                    clearable
                    style="width: 100%"
                    placeholder="如：+100 或 -50"
                    :show-button="false"
                  />
                </n-form-item>

                <n-form-item label="原因" path="reason">
                  <n-input
                    v-model:value="pointsAdjustForm.reason"
                    size="small"
                    placeholder="调整原因"
                  />
                </n-form-item>

                <div class="form-actions">
                  <n-button
                    type="primary"
                    size="small"
                    @click="handleAdjustPoints"
                    :disabled="!pointsAdjustForm.amount || !pointsAdjustForm.reason || pointsAdjustForm.amount === 0"
                    :loading="adjustingPoints"
                  >
                    执行调整
                  </n-button>
                </div>
              </n-form>
            </n-tab-pane>

            <n-tab-pane name="balance" tab="余额调整">
              <n-form
                ref="balanceFormRef"
                :model="balanceAdjustForm"
                label-placement="left"
                label-width="60"
                class="compact-form"
              >
                <div class="operation-tip">
                  <n-text depth="3" style="font-size: 12px;">
                    输入正数增加余额，输入负数减少余额（如：+100.50 或 -50.00）
                  </n-text>
                </div>

                <n-form-item label="金额" path="amount">
                  <n-input-number
                    v-model:value="balanceAdjustForm.amount"
                    :precision="2"
                    size="small"
                    clearable
                    style="width: 100%"
                    placeholder="如：+100.50 或 -50.00"
                    :show-button="false"
                  />
                </n-form-item>

                <n-form-item label="原因" path="reason">
                  <n-input
                    v-model:value="balanceAdjustForm.reason"
                    size="small"
                    placeholder="调整原因"
                  />
                </n-form-item>

                <div class="form-actions">
                  <n-button
                    type="warning"
                    size="small"
                    @click="handleAdjustBalance"
                    :disabled="!balanceAdjustForm.amount || !balanceAdjustForm.reason || balanceAdjustForm.amount === 0"
                    :loading="adjustingBalance"
                  >
                    执行调整
                  </n-button>
                </div>
              </n-form>
            </n-tab-pane>
          </n-tabs>
        </div>

        <template #footer>
          <div class="modal-footer">
            <n-button @click="showAssetsConfigModal = false">关闭</n-button>
          </div>
        </template>
      </n-modal>
    </div>
  </n-message-provider>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h, onMounted, watch } from 'vue'
import {
  NButton,
  NInput,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NSelect,
  NDatePicker,
  NSwitch,
  NTabs,
  NTabPane,
  NText,
  NIcon,
  NTag,
  NTooltip,
  NDivider,
  NInputNumber,
  useMessage,
  NGrid,
  NGridItem,
  NCard,
  NSpace
} from 'naive-ui'
import { 
  AddOutline, 
  TrashOutline, 
  PencilOutline, 
  SearchOutline,
  PeopleOutline,
  WalletOutline,
  PhonePortraitOutline,
  PersonOutline,
  LogoWechat,
  AppsOutline,
  PeopleCircleOutline,
  GitNetworkOutline,
  CashOutline,
  RemoveOutline,
  AddCircleOutline,
  RemoveCircleOutline,
  CalendarOutline,
  CheckmarkCircleOutline,
  PersonAddOutline
} from '@vicons/ionicons5'
import { fetchMemberPackages } from '@/api/agent-packages'
import { 
  getAllMembers, 
  getMemberDetail, 
  createMember,
  updateMember,
  deleteMember,
  getTodayRegistrations,
  getActiveMembers,
  getPartnerCount,
  searchMembers,
  updateMemberAssets,
  adjustMemberPoints,
  adjustMemberBalance,
  fixMemberBalances
} from '@/api/members'

const message = useMessage()

// 会员数据
interface Member {
  id: string
  name: string
  phone: string
  email: string
  packageId?: number
  packageInfo?: {
    id: number
    title: string
    price: number
  } | null
  points: number
  balance: number
  status: boolean
  accountType: string
  isPartner: boolean
  referrerId?: string | null
  referrerName?: string | null
  createdAt?: string
  updatedAt?: string
}

// 会员等级选项
const levelOptions = [
  { label: '普通会员', value: 1 },
  { label: '银卡会员', value: 2 },
  { label: '金卡会员', value: 3 },
  { label: '钻石会员', value: 4 }
]

// 统计数据
const todayRegistrations = ref(0);
const activeMembers = ref(0);
const partnerCount = ref(0);

// 会员列表数据
const members = ref<Member[]>([])
const filteredMembers = ref<Member[]>([])

// 搜索关键词
const searchKeyword = ref('')

// 监听会员数据变化，自动更新过滤后的数据
watch(() => members.value, (newMembers) => {
  filteredMembers.value = newMembers;
}, { immediate: true });

// 清除搜索
function clearSearch() {
  searchKeyword.value = '';
  filteredMembers.value = members.value;
  message.info('已重置搜索结果');
}

// 表格分页
const pagination = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  onChange: (page: number) => {
    pagination.value.page = page
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.page = 1
  }
})

// 表格列定义
const columns = [
  {
    title: 'ID/编号',
    key: 'id',
    width: 100
  },
  {
    title: '联系方式',
    key: 'contact',
    render(row: any) {
      // 优先显示手机号，其次显示邮箱，最后显示姓名
      const contact = row.phone || row.email || row.name || '未设置';
      const contactType = row.phone ? '📱' : (row.email ? '📧' : '👤');

      return h('div', { class: 'contact-cell' }, [
        h('span', { class: 'contact-icon' }, contactType),
        h('span', { class: 'contact-text' }, contact)
      ]);
    }
  },
  {
    title: '账户',
    key: 'accountType',
    render(row: any) {
      const accountTypeMap: Record<string, { icon: any, text: string }> = {
        wechat: { icon: 'icon-wechat', text: '微信' },
        miniprogram: { icon: 'icon-miniprogram', text: '微信小程序' },
        qq: { icon: 'icon-qq', text: 'QQ' },
        account: { icon: 'icon-account', text: '账号' }
      };
      const account = accountTypeMap[row.accountType] || { icon: 'icon-account', text: '账号' };
      return h('div', { class: 'account-cell' }, [
        h('i', { class: `${account.icon} account-icon` }),
        h('span', {}, account.text)
      ]);
    }
  },
  {
    title: '资产',
    key: 'assets',
    render(row: any) {
      console.log('行数据:', row);
      console.log('行数据余额:', row.balance, typeof row.balance);
      
      // 处理余额显示
      let balanceValue = '0.00';
      if (row.balance !== null && row.balance !== undefined) {
        const numBalance = Number(row.balance);
        if (!isNaN(numBalance)) {
          balanceValue = numBalance.toFixed(2);
        }
      }
      
      return h('div', { class: 'assets-cell' }, [
        h('div', { class: 'asset-box points-box' }, [
          h('span', { class: 'asset-value points-value' }, row.points || 0),
          h('span', { class: 'asset-label points-label' }, '点')
        ]),
        h('div', { class: 'asset-box balance-box' }, [
          h('span', { class: 'asset-value balance-value' }, balanceValue),
          h('span', { class: 'asset-label balance-label' }, '元')
        ])
      ]);
    }
  },
  {
    title: '会员套餐',
    key: 'packageName',
    render(row: any) {
      const packageName = row.packageInfo ? row.packageInfo.title : '未开通';
      const colorClass = row.packageInfo ? 
        (row.packageInfo.title.includes('银卡') ? 'silver-package' : 
         (row.packageInfo.title.includes('金卡') ? 'gold-package' : '')) : '';
      
      return h('div', { class: `package-tag ${colorClass}` }, packageName);
    }
  },
  {
    title: '合伙人',
    key: 'isPartner',
    render(row: any) {
      return h('div', { class: 'partner-cell' }, row.isPartner ? '是' : '否');
    }
  },
  {
    title: '上级推广员',
    key: 'referrerId',
    render(row: any) {
      if (!row.referrerId) {
        return h('span', { class: 'no-referrer' }, '-');
      }
      
      return h('div', { class: 'referrer-cell' }, [
        h('span', { class: 'referrer-id' }, row.referrerId),
      ]);
    }
  },
  {
    title: '注册时间',
    key: 'createdAt',
    render(row: any) {
      const date = new Date(row.createdAt);
      const formattedDate = `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;
      const formattedTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
      
      return h('div', { class: 'time-cell' }, [
        h('div', { class: 'date' }, formattedDate),
        h('div', { class: 'time' }, formattedTime)
      ]);
    }
  },
  {
    title: '状态',
    key: 'status',
    render(row: any) {
      const statusClass = row.status ? 'status-active' : 'status-inactive';
      const statusText = row.status ? '启用' : '禁用';
      
      return h('div', { class: `status-tag ${statusClass}` }, statusText);
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row: any) {
      return h('div', { class: 'action-buttons' }, [
        h(
          NTooltip,
          { trigger: 'hover', placement: 'top' },
          {
            trigger: () => h(
              NButton,
              {
                quaternary: true,
                circle: true,
                type: 'info',
                size: 'small',
                onClick: () => handleShowPromotionStats(row)
              },
              { icon: () => h(GitNetworkOutline) }
            ),
            default: () => '查看推广统计'
          }
        ),
        h(
          NTooltip,
          { trigger: 'hover', placement: 'top' },
          {
            trigger: () => h(
              NButton,
              {
                quaternary: true,
                circle: true,
                type: 'warning',
                size: 'small',
                onClick: () => handleManageAssets(row)
              },
              { icon: () => h(WalletOutline) }
            ),
            default: () => '资产配置'
          }
        ),
        h(
          NTooltip,
          { trigger: 'hover', placement: 'top' },
          {
            trigger: () => h(
              NButton,
              {
                quaternary: true,
                circle: true,
                type: 'success',
                size: 'small',
                onClick: () => handleEditMember(row)
              },
              { icon: () => h(PencilOutline) }
            ),
            default: () => '编辑会员'
          }
        ),
        h(
          NTooltip,
          { trigger: 'hover', placement: 'top' },
          {
            trigger: () => h(
              NButton,
              {
                quaternary: true,
                circle: true,
                type: 'error',
                size: 'small',
                onClick: () => handleDeleteMember(row)
              },
              { icon: () => h(TrashOutline) }
            ),
            default: () => '删除会员'
          }
        )
      ]);
    }
  }
]

// 表单相关
const showAddMemberModal = ref(false)
const formRef = ref(null)
const isEditing = ref(false)
const currentMemberId = ref('')

const memberForm = ref({
  id: null as number | null,
  phone: '',
  email: '',
  password: '',
  name: '',
  accountType: 'wechat',
  packageId: null as number | null,
  isPartner: false,
  status: true,
  points: 0,
  balance: 0
})

const rules = {
  phone: {
    required: true,
    message: '请输入账号',
    trigger: 'blur'
  },
  email: {
    required: false,
    message: '请输入邮箱',
    trigger: 'blur'
  },
  password: {
    required: false,
    message: '请输入密码',
    trigger: 'blur'
  }
}

// 搜索
function handleSearch() {
  console.log('执行搜索，关键词:', searchKeyword.value);
  
  if (!searchKeyword.value.trim()) {
    // 如果搜索关键词为空，显示所有会员
    message.info('请输入搜索关键词');
    return;
  }
  
  // 本地筛选
  const keyword = searchKeyword.value.trim().toLowerCase();
  const filtered = members.value.filter(member => {
    // ID需要精确匹配
    if (keyword === String(member.id)) {
      return true;
    }
    
    // 其他字段继续使用模糊匹配
    return (
      (member.phone && member.phone.toLowerCase().includes(keyword)) ||
      (member.email && member.email.toLowerCase().includes(keyword)) ||
      (member.name && member.name.toLowerCase().includes(keyword))
    );
  });
  
  if (filtered.length === 0) {
    message.info('未找到匹配的会员');
  } else {
    message.success(`找到 ${filtered.length} 个匹配会员`);
  }
  
  // 更新表格数据
  filteredMembers.value = filtered;
  
  // 重置分页
  pagination.value.page = 1;
}

// 删除相关
const showDeleteConfirm = ref(false)
const memberToDelete = ref<string | null>(null)

function handleDeleteMember(member: any) {
  memberToDelete.value = member.id;
  showDeleteConfirm.value = true;
}

async function confirmDelete() {
  if (memberToDelete.value) {
    try {
      await deleteMember(parseInt(memberToDelete.value));
      message.success('会员删除成功');
      
      // 刷新数据
      await fetchAllData();
    } catch (error) {
      console.error('删除会员失败', error);
      message.error('删除会员失败');
    }
  }
  showDeleteConfirm.value = false;
  memberToDelete.value = null;
}

function cancelDelete() {
  showDeleteConfirm.value = false;
  memberToDelete.value = null;
}

// 编辑
function handleEditMember(member: any) {
  console.log('编辑会员:', member);
  
  // 设置为编辑模式
  isEditing.value = true;
  
  // 保存当前编辑的会员ID
  currentMemberId.value = String(member.id);
  
  // 复制会员数据到表单
  memberForm.value = {
    id: member.id,
    phone: member.phone || '',
    email: member.email || '',
    password: '', // 编辑时不显示原密码，留空表示不修改
    name: member.name || '',
    accountType: member.accountType || 'wechat',
    packageId: member.packageId || null,
    isPartner: member.isPartner === true,
    status: member.status === true,
    points: Number(member.points || 0),
    balance: Number(member.balance || 0)
  };
  
  console.log('设置表单数据:', memberForm.value);
  
  // 显示编辑弹窗
  showAddMemberModal.value = true;
}

// 重置表单
function resetForm() {
  console.log('重置表单');
  
  // 重置表单数据
  memberForm.value = {
    id: null,
    phone: '',
    email: '',
    password: '',
    name: '',
    accountType: 'wechat',
    packageId: null,
    isPartner: false,
    status: true,
    points: 0,
    balance: 0
  };
  
  // 重置编辑状态
  isEditing.value = false;
  currentMemberId.value = '';
  
  console.log('表单已重置:', memberForm.value);
}

// 保存会员信息
async function handleSaveMember() {
  console.log('保存会员信息:', memberForm.value, '是否编辑模式:', isEditing.value);
  
  try {
    // 确保数据格式正确，不包含点数和余额
    const formData = {
      ...memberForm.value,
      // 移除点数和余额
      status: memberForm.value.status === true || memberForm.value.status === 'true',
      isPartner: memberForm.value.isPartner === true || memberForm.value.isPartner === 'true',
      // 确保packageId是数字类型，如果为null则设为undefined
      packageId: memberForm.value.packageId ? Number(memberForm.value.packageId) : undefined,
    };

    // 删除点数和余额字段，使用资产配置功能来专门管理
    delete formData.points;
    delete formData.balance;

    console.log('处理后的表单数据:', formData);
    
    if (isEditing.value) {
      // 编辑现有会员
      if (!currentMemberId.value) {
        message.error('会员ID无效，无法更新');
        return;
      }
      
      // 确保ID是数值
      const memberId = parseInt(currentMemberId.value);
      if (isNaN(memberId)) {
        message.error('会员ID格式无效');
        return;
      }
      
      console.log('更新会员ID:', memberId, '数据:', formData);
      
      await updateMember(memberId, formData);
      message.success('会员信息更新成功');
    } else {
      // 创建新会员
      console.log('创建新会员:', formData);
      await createMember(formData);
      message.success('会员添加成功');
    }
    
    showAddMemberModal.value = false;
    resetForm();
    
    // 重新加载数据
    await fetchAllData();
  } catch (error) {
    console.error('保存会员信息失败:', error);
    message.error('保存会员信息失败: ' + (error instanceof Error ? error.message : '未知错误'));
  }
}

// 添加推广统计相关状态
const showPromotionStatsModal = ref(false)
const currentPromoter = ref<Member | null>(null)
const firstLevelReferrals = ref<Member[]>([])
const secondLevelReferrals = ref<Member[]>([])

// 添加计算推广统计的函数
function calculateReferrals(promoterId: string) {
  // 一级推广用户
  firstLevelReferrals.value = members.value.filter(member => member.referrerId === promoterId)
  
  // 二级推广用户
  secondLevelReferrals.value = members.value.filter(member => {
    const firstLevelIds = firstLevelReferrals.value.map(m => m.id)
    return firstLevelIds.includes(member.referrerId || '')
  })
}

// 添加处理推广统计的函数
function handleShowPromotionStats(row: Member) {
  currentPromoter.value = row;
  calculateReferrals(row.id);
  showPromotionStatsModal.value = true;
}

// 添加解析图标的工具函数
function resolveIcon(name: string) {
  const icons = {
    'LogoWechat': LogoWechat,
    'AppsOutline': AppsOutline,
    'PhonePortraitOutline': PhonePortraitOutline,
    'PersonOutline': PersonOutline
  };
  return icons[name as keyof typeof icons];
}

// 添加账户类型选项
const accountTypeOptions = [
  { label: '微信', value: 'wechat' },
  { label: '微信小程序', value: 'miniprogram' },
  { label: 'QQ', value: 'qq' },
  { label: '账号密码', value: 'account' }
]

// 会员套餐选项
const memberPackages = ref<Array<{label: string, value: number}>>([])

// 点数管理相关状态
const showPointsManageModal = ref(false)
const currentPointsMember = ref<Member | null>(null)
const pointsAdjustValue = ref(0)
const pointsAdjustRemark = ref('')

// 资产配置相关状态
const showAssetsConfigModal = ref(false)
const currentAssetsMember = ref<Member | null>(null)

// 点数调整表单
const pointsAdjustForm = ref({
  amount: null as number | null,
  reason: ''
})

// 余额调整表单
const balanceAdjustForm = ref({
  amount: null as number | null,
  reason: ''
})

// 加载状态
const adjustingPoints = ref(false)
const adjustingBalance = ref(false)

// 生命周期钩子
onMounted(async () => {
  try {
    // 简化界面，使用默认值，避免初始化时的计算错误
    members.value = [];
    filteredMembers.value = [];
    
    // 异步加载数据
    setTimeout(() => {
      fetchAllData();
    }, 100);
  } catch (error) {
    console.error('初始化失败', error);
    message.error('数据加载失败，请刷新页面重试');
  }
});

// 方法定义
async function fetchAllData() {
  try {
    // 重置数据，防止旧数据干扰
    members.value = [];
    filteredMembers.value = [];
    
    // 修复会员余额问题 - 静默处理可能的错误
    try {
      const fixResult = await fixMemberBalances();
      console.log('会员余额处理结果:', fixResult);
    } catch (fixError) {
      // 静默处理错误，不影响主流程
      console.log('会员余额处理跳过，继续加载数据');
    }
    
    // 获取会员列表
    const membersResponse = await getAllMembers();
    console.log('会员响应数据:', membersResponse);
    
    // 处理会员数据
    if (membersResponse && membersResponse.data && Array.isArray(membersResponse.data)) {
      members.value = membersResponse.data;
      filteredMembers.value = membersResponse.data;
    } else {
      console.error('会员数据格式不正确:', membersResponse);
    }
    
    // 尝试加载统计数据
    try {
      const [todayResponse, activeResponse, partnerResponse] = await Promise.all([
        getTodayRegistrations(),
        getActiveMembers(),
        getPartnerCount()
      ]);
      
      todayRegistrations.value = todayResponse?.data?.count || 0;
      activeMembers.value = activeResponse?.data?.count || 0;
      partnerCount.value = partnerResponse?.data?.count || 0;
    } catch (statsError) {
      console.log('加载统计数据跳过，使用默认值');
      // 使用默认值
      todayRegistrations.value = 0;
      activeMembers.value = 0;
      partnerCount.value = 0;
    }
    
    // 加载会员套餐
    await loadMemberPackages();
    
  } catch (error) {
    console.error('获取数据失败', error);
    message.error('获取数据失败，请重试');
    members.value = [];
  }
}

async function loadMemberPackages() {
  try {
    // 使用API导入的函数
    const response = await fetchMemberPackages();
    console.log('会员套餐响应数据:', response);

    // 处理不同的响应格式
    let packagesData = [];

    if (response && Array.isArray(response)) {
      // 如果response直接是数组
      packagesData = response;
    } else if (response && response.data) {
      // 如果response有data字段
      if (Array.isArray(response.data)) {
        packagesData = response.data;
      } else if (response.data.list && Array.isArray(response.data.list)) {
        packagesData = response.data.list;
      }
    }

    console.log('提取的套餐数据:', packagesData);

    if (Array.isArray(packagesData) && packagesData.length > 0) {
      memberPackages.value = packagesData.map((pkg: any) => ({
        label: pkg.title || '未命名套餐',
        value: pkg.id
      }));
      console.log('处理后的会员套餐选项:', memberPackages.value);
    } else {
      console.error('会员套餐数据格式不正确或为空:', response);
      memberPackages.value = [];
    }
  } catch (error) {
    console.error('获取会员套餐失败', error);
    message.error('获取会员套餐失败');
    memberPackages.value = []; // 确保设置为空数组
  }
}

// 添加会员
function handleAddMember() {
  resetForm();
  showAddMemberModal.value = true;
}

function handleViewStatistics(member: any) {
  currentPromoter.value = member;
  
  // 示例数据，实际项目中需要从后端获取
  firstLevelReferrals.value = members.value.filter(m => m.referrerId === member.id) || [];
  
  // 获取二级推广用户
  const firstLevelIds = firstLevelReferrals.value.map(m => m.id);
  secondLevelReferrals.value = members.value.filter(m => 
    firstLevelIds.includes(m.referrerId || 0)
  ) || [];
  
  showPromotionStatsModal.value = true;
}

function handleManagePoints(member: any) {
  currentPointsMember.value = member;
  pointsAdjustValue.value = 0;
  pointsAdjustRemark.value = '';
  showPointsManageModal.value = true;
}

function increaseInputValue() {
  if (pointsAdjustValue.value === null || isNaN(pointsAdjustValue.value)) {
    pointsAdjustValue.value = 1;
  } else {
    pointsAdjustValue.value++;
  }
}

function decreaseInputValue() {
  if (pointsAdjustValue.value === null || isNaN(pointsAdjustValue.value) || pointsAdjustValue.value <= 1) {
    pointsAdjustValue.value = 1;
  } else {
    pointsAdjustValue.value--;
  }
}

async function handleIncreasePoints() {
  if (!currentPointsMember.value || !pointsAdjustValue.value || pointsAdjustValue.value <= 0) return;
  
  try {
    const updatedPoints = (currentPointsMember.value.points || 0) + pointsAdjustValue.value;
    
    await updateMember(parseInt(currentPointsMember.value.id), { points: updatedPoints });
    message.success('点数增加成功');
    showPointsManageModal.value = false;
    
    // 刷新数据
    await fetchAllData();
  } catch (error) {
    console.error('更新点数失败', error);
    message.error('更新点数失败');
  }
}

async function handleDecreasePoints() {
  if (!currentPointsMember.value || !pointsAdjustValue.value || pointsAdjustValue.value <= 0) return;
  
  try {
    const currentPoints = currentPointsMember.value.points || 0;
    const updatedPoints = Math.max(0, currentPoints - pointsAdjustValue.value);
    
    await updateMember(parseInt(currentPointsMember.value.id), { points: updatedPoints });
    message.success('点数扣除成功');
    showPointsManageModal.value = false;
    
    // 刷新数据
    await fetchAllData();
  } catch (error) {
    console.error('更新点数失败', error);
    message.error('更新点数失败');
  }
}

// 格式化余额显示
function formatBalance(value: any): string {
  console.log('格式化余额:', value, typeof value);
  if (value === null || value === undefined) return '0.00';

  // 尝试转换为数字
  const num = Number(value);
  if (isNaN(num)) {
    console.log('余额无效，显示为0.00');
    return '0.00';
  }

  console.log('格式化后的余额:', num.toFixed(2));
  return num.toFixed(2);
}

// 格式化点数显示
function formatPoints(value: any): string {
  if (value === null || value === undefined) return '0';
  const num = Number(value);
  if (isNaN(num)) return '0';
  return num.toString();
}

// 资产配置弹窗处理函数
function handleManageAssets(member: any) {
  console.log('管理资产，原始数据:', member);
  currentAssetsMember.value = member;

  // 重置表单数据
  pointsAdjustForm.value = {
    amount: null,
    reason: ''
  };

  balanceAdjustForm.value = {
    amount: null,
    reason: ''
  };

  showAssetsConfigModal.value = true;
}

// 点数调整
async function handleAdjustPoints() {
  if (!currentAssetsMember.value || !pointsAdjustForm.value.amount || !pointsAdjustForm.value.reason) {
    message.error('请填写完整的点数调整信息');
    return;
  }

  if (pointsAdjustForm.value.amount === 0) {
    message.error('调整数量不能为0');
    return;
  }

  adjustingPoints.value = true;
  try {
    const amount = Math.abs(pointsAdjustForm.value.amount);
    const type = pointsAdjustForm.value.amount > 0 ? 'increase' : 'decrease';

    const adjustData = {
      type,
      amount,
      reason: pointsAdjustForm.value.reason
    };

    console.log('点数调整数据:', adjustData);

    // 调用点数调整API
    const result = await adjustMemberPoints(parseInt(currentAssetsMember.value.id), adjustData);
    console.log('点数调整响应:', result);

    if (result && result.success) {
      message.success(`点数${type === 'increase' ? '增加' : '减少'}成功`);

      // 重置表单
      pointsAdjustForm.value = {
        amount: null,
        reason: ''
      };

      // 刷新数据
      await fetchAllData();
    } else {
      throw new Error('点数调整失败');
    }
  } catch (error) {
    console.error('点数调整失败:', error);
    message.error('点数调整失败，请稍后重试');
  } finally {
    adjustingPoints.value = false;
  }
}

// 余额调整
async function handleAdjustBalance() {
  if (!currentAssetsMember.value || !balanceAdjustForm.value.amount || !balanceAdjustForm.value.reason) {
    message.error('请填写完整的余额调整信息');
    return;
  }

  if (balanceAdjustForm.value.amount === 0) {
    message.error('调整金额不能为0');
    return;
  }

  adjustingBalance.value = true;
  try {
    const amount = Math.abs(balanceAdjustForm.value.amount);
    const type = balanceAdjustForm.value.amount > 0 ? 'increase' : 'decrease';

    const adjustData = {
      type,
      amount,
      reason: balanceAdjustForm.value.reason
    };

    console.log('余额调整数据:', adjustData);

    // 调用余额调整API
    const result = await adjustMemberBalance(parseInt(currentAssetsMember.value.id), adjustData);
    console.log('余额调整响应:', result);

    if (result && result.success) {
      message.success(`余额${type === 'increase' ? '增加' : '减少'}成功`);

      // 重置表单
      balanceAdjustForm.value = {
        amount: null,
        reason: ''
      };

      // 刷新数据
      await fetchAllData();
    } else {
      throw new Error('余额调整失败');
    }
  } catch (error) {
    console.error('余额调整失败:', error);
    message.error('余额调整失败，请稍后重试');
  } finally {
    adjustingBalance.value = false;
  }
}
</script>

<style scoped>
.members-container {
  padding: 12px 24px 20px;
  height: 100vh;
  position: relative;
  overflow-y: auto;
  box-sizing: border-box;
  max-width: 1400px;
  margin: 0 auto;
  background-color: #f5f7fa;
}

/* 添加卡片区域容器 */
.cards-section {
  padding-bottom: 0;
  position: relative;
  margin-bottom: 28px;
}

.stat-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 4px;
  margin-bottom: 0;
}

.stat-card-container {
  background-color: white;
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  border-left: 4px solid transparent;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.stat-card-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  opacity: 0.1;
  z-index: 0;
  transform: translate(30%, 30%);
}

.stat-card-container:has(.icon-container)::after {
  background-color: #00a870;
}

.stat-card-container:has(.icon-container.yellow)::after {
  background-color: #ff9d00;
}

.stat-card-container:has(.icon-container.green)::after {
  background-color: #00b42a;
}

.stat-card-container:has(.icon-container.blue)::after {
  background-color: #2080f0;
}

.stat-card-container:has(.icon-container.purple)::after {
  background-color: #8b5cf6;
}

.stat-card-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background-color: rgba(0, 168, 112, 0.08);
  border-left: 3px solid #00a870;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.stat-card-container:has(.icon-container) {
  border-left-color: #00a870;
}

.icon-container.green {
  background-color: rgba(0, 180, 42, 0.08);
  border-left: 3px solid #00b42a;
}

.stat-card-container:has(.icon-container.green) {
  border-left-color: #00b42a;
}

.icon-container.blue {
  background-color: rgba(32, 128, 240, 0.08);
  border-left: 3px solid #2080f0;
}

.stat-card-container:has(.icon-container.blue) {
  border-left-color: #2080f0;
}

.icon-container.purple {
  background-color: rgba(139, 92, 246, 0.08);
  border-left: 3px solid #8b5cf6;
}

.stat-card-container:has(.icon-container.purple) {
  border-left-color: #8b5cf6;
}

.stat-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  color: #111827;
  margin-bottom: 2px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.stat-label {
  font-size: 12px;
  color: #4b5563;
  font-weight: 500;
}

.main-table-card {
  border-radius: 8px;
  overflow: hidden;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  background-color: white;
  margin-top: 8px;
}

.main-table-card :deep(.n-card__content) {
  padding: 0;
}

.main-table-card :deep(.n-card-header) {
  padding: 14px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

.main-table-card :deep(.n-card-header__main) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.main-table-card :deep(.n-card-header__extra) {
  display: flex;
  flex: 1;
}

.data-table {
  transition: all 0.3s ease;
}

/* 表格单元格样式优化 */
:deep(.n-data-table) {
  --n-th-padding: 8px 12px;
  --n-td-padding: 8px 12px;
}

:deep(.n-data-table-wrapper) {
  padding: 0;
}

:deep(.n-data-table-th) {
  padding: 10px 12px;
  font-size: 12px;
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

:deep(.n-data-table-td) {
  padding: 10px 12px;
  font-size: 12px;
  color: #4b5563;
}

:deep(.n-data-table-tr:hover) {
  background-color: rgba(0, 0, 0, 0.02);
}

:deep(.n-pagination) {
  padding: 12px 16px;
  justify-content: flex-end;
  border-top: 1px solid #f0f0f0;
}

/* 搜索框样式 */
.search-input {
  width: 240px;
  border-radius: 8px;
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* 会员信息弹窗样式 */
.member-info-modal :deep(.n-card__content) {
  padding: 24px;
}

.member-info-modal :deep(.n-form-item) {
  margin-bottom: 20px;
}

.member-info-modal :deep(.n-form-item-label) {
  padding-right: 12px;
  font-weight: 500;
}

.member-info-modal :deep(.n-card__footer) {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}

.promotion-stats-container {
  padding: 15px 0;
}

.promoter-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.promoter-info h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #333;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stats-card {
  flex: 1;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-card.primary {
  background-color: #e8f5e9;
  border-left: 4px solid #4caf50;
}

.stats-card.secondary {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.stats-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.stats-icon {
  margin-right: 10px;
}

.stats-card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.stats-card-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
}

.stats-card-list {
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
}

.user-item {
  padding: 6px 10px;
  margin-bottom: 6px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.user-id {
  font-family: monospace;
  font-weight: 500;
}

.empty-tip {
  text-align: center;
  color: #999;
  padding: 15px 0;
  font-style: italic;
}

.points-modal :deep(.n-card) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.points-modal :deep(.n-card-header) {
  padding: 0;
  border: none;
}

.modal-custom-header {
  padding: 20px 24px;
  background: linear-gradient(135deg, #1890ff, #46b3ff);
  color: white;
  border-radius: 12px 12px 0 0;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.points-manage-container {
  padding: 24px;
}

.top-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 30px;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.member-avatar {
  width: 50px;
  height: 50px;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.member-details {
  overflow: hidden;
}

.member-name {
  font-size: 16px;
  font-weight: 600;
  color: #000;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-id {
  font-size: 14px;
  color: #999;
}

.points-card {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 15px 20px;
  border-left: 4px solid #1890ff;
}

.points-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.points-value {
  display: flex;
  align-items: baseline;
}

.points-number {
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
}

.points-unit {
  margin-left: 4px;
  font-size: 14px;
  color: #999;
}

.operation-section {
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.points-input-box {
  margin-bottom: 15px;
}

.input-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.input-with-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.points-input {
  flex: 1;
}

.remark-input {
  margin-top: 15px;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.action-button {
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: rgba(0, 0, 0, 0.06);
  color: var(--primary-color);
}

:deep(.n-button.n-button--quaternary) {
  padding: 6px;
}

:deep(.n-data-table-tr:hover .n-button.n-button--quaternary) {
  opacity: 0.9;
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.member-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  margin-right: 4px;
  display: inline-block;
}

.member-tag.active {
  background-color: rgba(0, 180, 42, 0.08);
  color: #00b42a;
  border: 1px solid rgba(0, 180, 42, 0.2);
}

.member-tag.inactive {
  background-color: rgba(100, 116, 139, 0.08);
  color: #64748b;
  border: 1px solid rgba(100, 116, 139, 0.2);
}

.member-tag.partner {
  background-color: rgba(139, 92, 246, 0.08);
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
}

.empty-state p {
  margin-top: 16px;
  font-size: 14px;
}

.search-result-tip {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #e6f7ff;
  border-radius: 4px;
  margin-bottom: 12px;
  font-size: 13px;
  color: #1890ff;
}

.search-result-tip .n-icon {
  margin-right: 8px;
}

.search-result-tip span {
  flex: 1;
}

/* 资产和点数管理样式 */
.assets-cell {
  display: flex;
  gap: 6px;
}

.asset-box {
  display: flex;
  align-items: baseline;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.points-box {
  background-color: rgba(32, 128, 240, 0.08);
  border: 1px solid rgba(32, 128, 240, 0.2);
}

.balance-box {
  background-color: rgba(0, 180, 42, 0.08);
  border: 1px solid rgba(0, 180, 42, 0.2);
}

.asset-value {
  font-weight: 600;
  font-size: 12px;
}

.points-value {
  color: #2080f0;
}

.balance-value {
  color: #00b42a;
}

.asset-label {
  margin-left: 2px;
  color: #666;
  font-size: 10px;
}

.contact-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contact-icon {
  font-size: 16px;
}

.contact-text {
  color: #333;
  font-weight: 500;
}

.account-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 点数管理和资产配置弹窗样式 */
.points-modal :deep(.n-card),
.assets-config-modal :deep(.n-card) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.points-modal :deep(.n-card-header),
.assets-config-modal :deep(.n-card-header) {
  padding: 0;
  border: none;
}

.modal-custom-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.points-manage-container,
.assets-config-container {
  padding: 20px;
}

.member-info-section {
  margin-bottom: 20px;
}

.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(32, 128, 240, 0.1);
  color: #2080f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  margin-right: 12px;
}

.member-details {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.member-id {
  font-size: 12px;
  color: #6b7280;
}

.assets-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.asset-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  background-color: #f9fafb;
}

.points-card {
  border-left: 4px solid #2080f0;
}

.balance-card {
  border-left: 4px solid #00b42a;
}

.asset-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 12px;
}

.points-card .asset-icon {
  background-color: rgba(32, 128, 240, 0.08);
  color: #2080f0;
}

.balance-card .asset-icon {
  background-color: rgba(0, 180, 42, 0.08);
  color: #00b42a;
}

.asset-details {
  display: flex;
  flex-direction: column;
}

.asset-label {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 4px;
}

.asset-value {
  font-size: 18px;
  font-weight: 700;
}

.points-card .asset-value {
  color: #2080f0;
}

.balance-card .asset-value {
  color: #00b42a;
}

.assets-form {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #e0e0e0;
}

/* 点数操作 */
.top-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.points-card {
  background-color: rgba(32, 128, 240, 0.05);
  border-radius: 8px;
  padding: 12px 16px;
  border-left: 4px solid #2080f0;
}

.points-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.points-number {
  font-size: 20px;
  font-weight: 700;
  color: #2080f0;
}

.points-unit {
  font-size: 14px;
  color: #6b7280;
  margin-left: 2px;
}

.operation-section {
  margin-bottom: 20px;
}

.points-input-box {
  margin-bottom: 12px;
}

.input-label {
  font-size: 13px;
  color: #374151;
  margin-bottom: 8px;
}

.input-with-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.points-input {
  flex: 1;
}

.remark-input {
  margin-top: 15px;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

/* 点数增减按钮 */
.increase-button, .decrease-button {
  height: 44px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
}

.increase-button {
  background-color: rgba(32, 128, 240, 0.08);
  color: #2080f0;
  border: 1px solid rgba(32, 128, 240, 0.2);
}

.increase-button:hover:not(:disabled) {
  background-color: rgba(32, 128, 240, 0.15);
}

.decrease-button {
  background-color: rgba(255, 157, 0, 0.08);
  color: #ff9d00;
  border: 1px solid rgba(255, 157, 0, 0.2);
}

.decrease-button:hover:not(:disabled) {
  background-color: rgba(255, 157, 0, 0.15);
}

.button-icon {
  margin-right: 4px;
}

/* 推广统计样式 */
.promotion-stats-container {
  padding: 16px;
}

.promoter-info {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.promoter-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.stats-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stats-card {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid;
}

.stats-card.primary {
  border-left-color: #2080f0;
}

.stats-card.secondary {
  border-left-color: #8b5cf6;
}

.stats-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.stats-icon {
  margin-right: 8px;
}

.stats-card-header h3 {
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.stats-card-value {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 12px;
}

.stats-card-list {
  max-height: 150px;
  overflow-y: auto;
  border-top: 1px dashed #e0e0e0;
  padding-top: 12px;
}

.user-item {
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.user-id {
  color: #2080f0;
  cursor: pointer;
}

.empty-tip {
  color: #9ca3af;
  font-size: 12px;
  text-align: center;
  padding: 20px 0;
}

/* 套餐标签样式 */
.package-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e5e7eb;
}

.package-tag.silver-package {
  background-color: #f8fafc;
  color: #64748b;
  border-color: #cbd5e1;
}

.package-tag.gold-package {
  background-color: #fef3c7;
  color: #d97706;
  border-color: #fbbf24;
}

/* 未开通套餐样式 */
.package-tag:not(.silver-package):not(.gold-package) {
  background-color: #f9fafb;
  color: #9ca3af;
  border-color: #e5e7eb;
}

/* 资产调整相关样式 */
.assets-config-container {
  padding: 0;
}

.member-assets-header {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid #e9ecef;
}

.member-basic-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.member-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.member-id {
  font-size: 12px;
  color: #666;
}

.current-assets {
  display: flex;
  gap: 16px;
}

.asset-info {
  font-size: 13px;
  color: #666;
}

.asset-info strong {
  color: #2080f0;
  font-weight: 600;
}

.compact-form {
  padding: 16px 0;
}

.compact-form .n-form-item {
  margin-bottom: 12px;
}

.operation-tip {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  text-align: center;
}

.form-actions {
  margin-top: 16px;
  text-align: right;
}

.modal-footer {
  display: flex;
  justify-content: center;
  padding: 8px 0;
}
</style>