import { MemberController } from "./controller/MemberController";
import { TaskController } from "./controller/task.controller";
import { TestController } from './controller/TestController';
import { ConfigController } from './controller/config.controller';
import { PointsController } from './controller/PointsController';
import { ShareController } from './controller/ShareController';

// 定义路由
export const Routes = [
    // 会员相关路由
    {
        method: "get",
        route: "/api/members",
        controller: MemberController,
        action: "all"
    },
    {
        method: "get",
        route: "/api/members/stats/today",
        controller: MemberController,
        action: "getTodayRegistrations"
    },
    {
        method: "get",
        route: "/api/members/stats/active",
        controller: MemberController,
        action: "getActiveMembers"
    },
    {
        method: "get",
        route: "/api/members/stats/partners",
        controller: MemberController,
        action: "getPartnerCount"
    },
    {
        method: "get",
        route: "/api/members/search",
        controller: Member<PERSON><PERSON>roller,
        action: "search"
    },
    {
        method: "get",
        route: "/api/members/me",
        controller: MemberController,
        action: "getCurrentMember"
    },
    {
        method: "get",
        route: "/api/members/:id",
        controller: MemberController,
        action: "one"
    },
    {
        method: "post",
        route: "/api/members",
        controller: MemberController,
        action: "create"
    },
    {
        method: "post",
        route: "/api/members/register",
        controller: MemberController,
        action: "register"
    },
    {
        method: "post",
        route: "/api/members/login",
        controller: MemberController,
        action: "login"
    },
    {
        method: "post",
        route: "/api/members/set-test-password",
        controller: MemberController,
        action: "setTestPassword"
    },
    {
        method: "put",
        route: "/api/members/:id",
        controller: MemberController,
        action: "update"
    },
    {
        method: "put",
        route: "/api/members/:id/profile",
        controller: MemberController,
        action: "updateProfile"
    },
    {
        method: "delete",
        route: "/api/members/:id",
        controller: MemberController,
        action: "delete"
    },
    // 会员资产更新路由
    {
        method: "put",
        route: "/api/members/:id/assets",
        controller: MemberController,
        action: "updateAssets"
    },
    // 修复会员余额路由
    {
        method: "post",
        route: "/api/fix-member-balances",
        controller: MemberController,
        action: "fixMemberBalances"
    },
    // 余额明细路由
    {
        method: "get",
        route: "/api/members/:id/balance-records",
        controller: MemberController,
        action: "getBalanceRecords"
    },
    // 点数明细路由
    {
        method: "get",
        route: "/api/members/:id/points-records",
        controller: PointsController,
        action: "getPointsRecords"
    },
    // 申请提现路由
    {
        method: "post",
        route: "/api/members/:id/withdraw",
        controller: MemberController,
        action: "applyWithdraw"
    },
    // 提现记录路由
    {
        method: "get",
        route: "/api/members/:id/withdraw-records",
        controller: MemberController,
        action: "getWithdrawRecords"
    },
    // 管理员调整会员点数路由
    {
        method: "post",
        route: "/api/admin/members/:id/points/adjust",
        controller: MemberController,
        action: "adjustMemberPoints"
    },
    // 管理员调整会员余额路由
    {
        method: "post",
        route: "/api/admin/members/:id/balance/adjust",
        controller: MemberController,
        action: "adjustMemberBalance"
    },
    // 扣除用户点数路由
    {
        method: "post",
        route: "/api/members/:id/deduct-points",
        controller: MemberController,
        action: "deductPoints"
    },
    // 管理端提现管理路由
    {
        method: "get",
        route: "/api/withdrawals/stats",
        controller: MemberController,
        action: "getWithdrawalStats"
    },
    {
        method: "get",
        route: "/api/withdrawals",
        controller: MemberController,
        action: "getAllWithdrawals"
    },
    {
        method: "get",
        route: "/api/withdrawals/:id",
        controller: MemberController,
        action: "getWithdrawalDetail"
    },
    {
        method: "post",
        route: "/api/withdrawals/:id/review",
        controller: MemberController,
        action: "reviewWithdrawal"
    },
    {
        method: "delete",
        route: "/api/withdrawals/:id",
        controller: MemberController,
        action: "deleteWithdrawal"
    },

    // 任务管理相关路由
    {
        method: "get",
        route: "/api/tasks",
        controller: TaskController,
        action: "all"
    },
    {
        method: "get",
        route: "/api/tasks/:id",
        controller: TaskController,
        action: "one"
    },
    {
        method: "post",
        route: "/api/tasks",
        controller: TaskController,
        action: "save"
    },
    {
        method: "delete",
        route: "/api/tasks/:id",
        controller: TaskController,
        action: "remove"
    },

    // 测试相关路由
    {
        method: "get",
        route: "/api/test",
        controller: TestController,
        action: "test"
    },
    {
        method: "get",
        route: "/api/test/config",
        controller: TestController,
        action: "testConfig"
    },
    {
        method: "get",
        route: "/api/test/db",
        controller: TestController,
        action: "testDB"
    },

    // 配置相关路由
    {
        method: "get",
        route: "/api/configs",
        controller: ConfigController,
        action: "all"
    },
    {
        method: "get",
        route: "/api/configs/:id",
        controller: ConfigController,
        action: "one"
    },
    {
        method: "post",
        route: "/api/configs",
        controller: ConfigController,
        action: "save"
    },
    {
        method: "delete",
        route: "/api/configs/:id",
        controller: ConfigController,
        action: "remove"
    },
    // 任务管理路由
    // 任务相关路由已移至 taskRoutes，避免重复注册

    // 分享相关路由
    {
        method: "post",
        route: "/api/share/generate-invite-code",
        controller: ShareController,
        action: "generateInviteCode"
    },
    {
        method: "post",
        route: "/api/share/handle-invite-code",
        controller: ShareController,
        action: "handleInviteCode"
    }
];