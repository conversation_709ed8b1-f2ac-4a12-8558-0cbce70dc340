<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow-x: hidden;
        }
        
        .profile-page {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            padding: 0;
            position: relative;
            overflow: hidden;
        }

        .profile-page::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 30%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 70%, rgba(30, 144, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 50% 50%, rgba(255, 20, 147, 0.05) 0%, transparent 50%);
            animation: gradientShift 10s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        /* 顶部用户信息 */
        .header-section {
            padding: 60px 20px 20px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .user-avatar {
            margin-bottom: 15px;
        }

        .avatar-img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        }

        .user-status {
            margin-bottom: 20px;
        }

        .status-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        /* 启元AI 会员卡片 */
        .member-card {
            margin: 0 20px 25px;
            background: linear-gradient(135deg, #00ff87 0%, #60efff 100%);
            border-radius: 16px;
            padding: 3px;
            position: relative;
            z-index: 1;
            animation: cardGlow 3s ease-in-out infinite;
        }

        @keyframes cardGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 135, 0.3); }
            50% { box-shadow: 0 0 30px rgba(96, 239, 255, 0.5); }
        }

        .card-content {
            background: rgba(26, 26, 46, 0.9);
            border-radius: 14px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .card-left {
            flex: 1;
        }

        .card-title {
            color: #00ff87;
            font-size: 18px;
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }

        .card-desc {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
        }

        .upgrade-btn {
            background: linear-gradient(135deg, #00ff87 0%, #60efff 100%);
            color: #1a1a2e;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0, 255, 135, 0.3);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .upgrade-btn:hover {
            transform: translateY(-2px);
        }

        /* 内容标签页 */
        .content-tabs {
            display: flex;
            align-items: center;
            padding: 0 20px 20px;
            position: relative;
            z-index: 1;
        }

        .tab-item {
            margin-right: 30px;
        }

        .tab-item.active .tab-text {
            color: #ffffff;
            border-bottom: 2px solid #00ff87;
            padding-bottom: 5px;
        }

        .tab-text {
            color: rgba(255, 255, 255, 0.6);
            font-size: 16px;
            font-weight: 500;
        }

        .edit-btn {
            margin-left: auto;
        }

        .edit-text {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        /* 功能按钮组 */
        .function-buttons {
            padding: 0 20px;
            position: relative;
            z-index: 1;
        }

        .btn-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .func-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 12px 20px;
            flex: 1;
            min-width: 80px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .func-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .btn-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 13px;
        }

        .storage-info {
            margin-bottom: 30px;
        }

        .storage-text {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }

        /* 暂无内容 */
        .empty-content {
            text-align: center;
            padding: 60px 20px;
            position: relative;
            z-index: 1;
        }

        .empty-icon {
            margin-bottom: 15px;
        }

        .icon {
            font-size: 48px;
            opacity: 0.3;
        }

        .empty-text {
            color: rgba(255, 255, 255, 0.5);
            font-size: 16px;
        }

        /* 移动端适配 */
        @media (max-width: 480px) {
            .func-btn {
                min-width: 70px;
                padding: 10px 15px;
            }
            
            .btn-text {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="profile-page">
        <!-- 顶部用户信息 -->
        <div class="header-section">
            <div class="user-avatar">
                <div class="avatar-img"></div>
            </div>
            <div class="user-status">
                <div class="status-text">您还不是会员</div>
            </div>
        </div>
        
        <!-- 启元AI 会员卡片 -->
        <div class="member-card">
            <div class="card-content">
                <div class="card-left">
                    <div class="card-title">启元AI ✨</div>
                    <div class="card-desc">成为会员解锁全部功能</div>
                </div>
                <div class="card-right">
                    <button class="upgrade-btn" onclick="alert('开通会员功能')">开通会员</button>
                </div>
            </div>
        </div>
        
        <!-- 我的作品和收藏 -->
        <div class="content-tabs">
            <div class="tab-item active">
                <div class="tab-text">我的作品</div>
            </div>
            <div class="tab-item">
                <div class="tab-text">我的收藏</div>
            </div>
            <div class="edit-btn">
                <div class="edit-text">✏️ 管理</div>
            </div>
        </div>
        
        <!-- 功能按钮组 -->
        <div class="function-buttons">
            <div class="btn-row">
                <div class="func-btn" onclick="alert('最近生成')">
                    <div class="btn-text">📱最近生成</div>
                </div>
                <div class="func-btn" onclick="alert('AI视频')">
                    <div class="btn-text">AI视频</div>
                </div>
                <div class="func-btn" onclick="alert('AI绘画')">
                    <div class="btn-text">AI绘画</div>
                </div>
                <div class="func-btn" onclick="alert('AI音乐')">
                    <div class="btn-text">AI音乐</div>
                </div>
                <div class="func-btn" onclick="alert('AI写真')">
                    <div class="btn-text">AI写真</div>
                </div>
                <div class="func-btn" onclick="alert('照片')">
                    <div class="btn-text">照片</div>
                </div>
            </div>
            <div class="storage-info">
                <div class="storage-text">🔊 作品将为您保存7天，请及时下载</div>
            </div>
        </div>
        
        <!-- 暂无内容 -->
        <div class="empty-content">
            <div class="empty-icon">
                <div class="icon">📁🔍</div>
            </div>
            <div class="empty-text">暂无内容</div>
        </div>
    </div>
</body>
</html>
