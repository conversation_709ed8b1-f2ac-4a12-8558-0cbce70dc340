<template>
	<view class="profile-page">
		<!-- 下拉刷新 -->
		<scroll-view
			class="scroll-container"
			scroll-y="true"
			refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
		>
		<!-- 顶部用户信息 - 仅登录后显示 -->
		<view v-if="isLoggedIn" class="header-section">
			<!-- 第一排：用户基本信息 -->
			<view class="user-basic-info">
				<view class="user-avatar" @click="handleAvatarClick">
					<image class="avatar-img" :src="userInfo.avatar || '/static/images/znt_avatar.png'"></image>
					<view class="avatar-border"></view>
					<view class="avatar-edit-hint">
						<text class="edit-icon">📷</text>
					</view>
				</view>
				<view class="user-text-info">
					<view class="username-row">
						<text class="username">{{ userInfo.nickname || userInfo.username }}</text>
					</view>
					<view class="uid-tags-row">
						<text class="user-uid">UID: {{ userInfo.id || '未知' }}</text>
						<!-- 合伙人标签 -->
						<view v-if="isPartner" class="user-tag partner-tag" @click="showPartnerModal">
							<text class="tag-text">合伙人</text>
						</view>
						<!-- 非合伙人标签 -->
						<view v-if="!isPartner" class="user-tag non-partner-tag" @click="showPartnerModal">
							<text class="tag-text">非合伙人</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 第二排：余额统计信息 -->
			<view class="user-stats-section">
				<view class="stats-container">
					<view class="stat-item">
						<text class="stat-value">{{ userInfo.balance || 0 }}</text>
						<text class="stat-label">余额</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-value">{{ userInfo.points || 0 }}</text>
						<text class="stat-label">点数</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-value">{{ userInfo.inviteCount || 0 }}</text>
						<text class="stat-label">邀请</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 未登录状态的用户信息 -->
		<view v-else class="unauth-user-section">
			<view class="unauth-user-info">
				<view class="unauth-avatar" @click="showLoginModal">
					<image class="unauth-avatar-img" src="/static/images/znt_avatar.png"></image>
				</view>
				<view class="unauth-text" @click="showLoginModal">
					<text class="unauth-title">登录/注册</text>
					<text class="unauth-desc">成为本站会员</text>
					<!-- 测试登录按钮 -->
					<button class="test-login-btn" @click.stop="testLogin">测试登录</button>
				</view>
			</view>
		</view>

		<!-- 启元AI 会员卡片 -->
		<view class="member-card">
			<view class="card-content">
				<view class="card-left">
					<text class="card-title">{{ isLoggedIn ? '成为合伙人 ✨' : '启元AI +' }}</text>
					<text class="card-desc">{{ isLoggedIn ? '邀请好友可获得高额佣金' : '邀请好友自动开通合伙人' }}</text>
				</view>
				<view class="card-right">
					<button class="upgrade-btn" @click="upgradeMembership">{{ isLoggedIn ? '立即推广' : '开启合伙' }}</button>
				</view>
			</view>
		</view>

		<!-- 功能模块 -->
		<view class="function-modules">
			<view class="modules-container">
				<!-- 第一排 -->
				<view class="module-row">
					<view class="module-item" @click="navigateToModule('wallet')">
						<view class="item-icon">
							<text class="icon-emoji">💰</text>
						</view>
						<text class="item-title">钱包</text>
						<text class="item-desc">余额明细</text>
					</view>

					<view class="module-item" @click="navigateToModule('points')">
						<view class="item-icon">
							<text class="icon-emoji">💎</text>
						</view>
						<text class="item-title">点数</text>
						<text class="item-desc">点数明细</text>
					</view>

					<view class="module-item" @click="navigateToModule('packages')">
						<view class="item-icon">
							<text class="icon-emoji">📦</text>
						</view>
						<text class="item-title">套餐</text>
						<text class="item-desc">套餐明细</text>
					</view>
				</view>

				<!-- 第二排 -->
				<view class="module-row">
					<view class="module-item" @click="navigateToModule('team')">
						<view class="item-icon">
							<text class="icon-emoji">👥</text>
						</view>
						<text class="item-title">团队</text>
						<text class="item-desc">团队管理</text>
					</view>

					<view class="module-item" @click="navigateToModule('customer-service')">
						<view class="item-icon">
							<text class="icon-emoji">💬</text>
						</view>
						<text class="item-title">客服</text>
						<text class="item-desc">联系客服</text>
					</view>

					<view class="module-item" @click="navigateToModule('settings')">
						<view class="item-icon">
							<text class="icon-emoji">⚙️</text>
						</view>
						<text class="item-title">设置</text>
						<text class="item-desc">系统设置</text>
					</view>
				</view>
			</view>
		</view>

		</scroll-view>
	</view>

	<!-- 底部导航栏 -->
	<MagicNavigation
		:items="navItems"
		:current="currentNavIndex"
		@change="handleNavChange"
	/>

	<!-- 智能登录弹窗 -->
	<smart-login-modal
		:visible="showLoginModalFlag"
		@close="hideLoginModal"
		@login-success="handleLoginSuccess"
	></smart-login-modal>

	<!-- 个人设置弹窗 -->
	<personal-settings-modal
		:visible="showPersonalSettingsModal"
		:user-info="userInfo"
		@close="closePersonalSettingsModal"
		@settings-updated="handleSettingsUpdated"
		@avatar-updated="handleAvatarUpdated"
	></personal-settings-modal>

	<!-- 账户安全弹窗 -->
	<account-security-modal
		:visible="showAccountSecurityModal"
		:user-info="userInfo"
		@close="closeAccountSecurityModal"
		@security-updated="handleSecurityUpdated"
	></account-security-modal>

	<!-- 客服弹窗 -->
	<customer-service-modal
		:visible="showCustomerServiceModal"
		@close="closeCustomerServiceModal"
	></customer-service-modal>

	<!-- 合伙人权益弹窗 -->
	<view v-if="showPartnerBenefitsModal" class="partner-benefits-modal-overlay" @click="closePartnerBenefitsModal">
		<view class="partner-benefits-modal-card" @click.stop>
			<view class="benefits-modal-header">
				<text class="benefits-modal-title">合伙人权益</text>
				<view class="benefits-modal-close" @click="closePartnerBenefitsModal">
					<text class="close-icon">✕</text>
				</view>
			</view>
			<view class="benefits-modal-content">
				<!-- 升级条件 -->
				<view class="benefits-section">
					<view class="section-header">
						<text class="section-icon">🎯</text>
						<text class="section-title">升级条件</text>
					</view>
					<view class="condition-item">
						<text class="condition-text">邀请用户数量达到 {{ partnerUpgradeCondition }} 人</text>
						<view class="condition-status" :class="{ 'achieved': userInviteCount >= partnerUpgradeCondition }">
							<text class="status-text">{{ userInviteCount >= partnerUpgradeCondition ? '已达成' : `${userInviteCount}/${partnerUpgradeCondition}` }}</text>
						</view>
					</view>
				</view>

				<!-- 权益介绍 -->
				<view class="benefits-section">
					<view class="section-header">
						<text class="section-icon">💎</text>
						<text class="section-title">专属权益</text>
					</view>
					<view class="benefits-list">
						<view class="benefit-item">
							<text class="benefit-icon">💰</text>
							<text class="benefit-text">直推用户消费返现 {{ promoterCashback }}%</text>
						</view>
						<view class="benefit-item">
							<text class="benefit-icon">🔄</text>
							<text class="benefit-text">下级推广员分成 {{ promoterCommission }}%</text>
						</view>
						<view class="benefit-item">
							<text class="benefit-icon">💳</text>
							<text class="benefit-text">余额提现功能（最低{{ minWithdrawal }}元）</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- VIP会员权益弹窗 -->
	<view v-if="showVipBenefitsModal" class="vip-benefits-modal-overlay" @click="closeVipBenefitsModal">
		<view class="vip-benefits-modal-card" @click.stop>
			<view class="benefits-modal-header vip-header">
				<text class="benefits-modal-title">VIP会员权益</text>
				<view class="benefits-modal-close" @click="closeVipBenefitsModal">
					<text class="close-icon">✕</text>
				</view>
			</view>
			<view class="benefits-modal-content">
				<!-- VIP套餐信息 -->
				<view class="benefits-section" v-if="userInfo.packageInfo">
					<view class="section-header">
						<text class="section-icon">👑</text>
						<text class="section-title">当前套餐</text>
					</view>
					<view class="vip-package-info">
						<text class="package-title">{{ userInfo.packageInfo.title }}</text>
						<text class="package-price">￥{{ userInfo.packageInfo.price }}</text>
					</view>
				</view>

				<!-- VIP权益 -->
				<view class="benefits-section">
					<view class="section-header">
						<text class="section-icon">✨</text>
						<text class="section-title">专属权益</text>
					</view>
					<view class="benefits-list">
						<view class="benefit-item">
							<text class="benefit-icon">🚀</text>
							<text class="benefit-text">专享AI智能体服务</text>
						</view>
						<view class="benefit-item">
							<text class="benefit-icon">⚡</text>
							<text class="benefit-text">更高的使用配额</text>
						</view>
						<view class="benefit-item">
							<text class="benefit-icon">🎯</text>
							<text class="benefit-text">优先客服支持</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 设置卡片弹窗 -->
	<view v-if="showSettingsModal" class="settings-modal-overlay" @click="closeSettingsModal">
		<view class="settings-modal-card" @click.stop>
			<view class="settings-modal-header">
				<text class="settings-modal-title">设置</text>
				<view class="settings-modal-close" @click="closeSettingsModal">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<view class="settings-modal-content">
				<view class="settings-option" @click="handlePersonalSettings">
					<view class="option-icon">
						<text class="icon-emoji">👤</text>
					</view>
					<view class="option-content">
						<text class="option-title">个人设置</text>
						<text class="option-desc">修改个人信息</text>
					</view>
					<view class="option-arrow">
						<text class="arrow-icon">›</text>
					</view>
				</view>

				<view class="settings-option" @click="handleAccountSecurity">
					<view class="option-icon">
						<text class="icon-emoji">🔒</text>
					</view>
					<view class="option-content">
						<text class="option-title">账户安全</text>
						<text class="option-desc">密码与安全设置</text>
					</view>
					<view class="option-arrow">
						<text class="arrow-icon">›</text>
					</view>
				</view>

				<view class="settings-option logout-option" @click="handleLogout">
					<view class="option-icon">
						<text class="icon-emoji">🚪</text>
					</view>
					<view class="option-content">
						<text class="option-title">退出登录</text>
						<text class="option-desc">安全退出当前账户</text>
					</view>
					<view class="option-arrow">
						<text class="arrow-icon">›</text>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 钱包管理弹窗 -->
	<view v-if="showWalletModal" class="modern-wallet-overlay" @click="closeWalletModal">
		<view class="modern-wallet-card" @click.stop>
			<!-- 头部区域 -->
			<view class="modern-wallet-header">
				<view class="header-content">
					<view class="header-icon">
						<text class="icon-wallet">💰</text>
					</view>
					<view class="header-text">
						<text class="header-title">钱包管理</text>
					</view>
				</view>
				<view class="close-button" @click="closeWalletModal">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<!-- 余额卡片 -->
			<view class="balance-card">
				<view class="balance-card-bg"></view>
				<view class="balance-content">
					<view class="balance-info">
						<text class="balance-label">当前余额</text>
						<text class="balance-amount">¥{{ userInfo.balance || 0 }}</text>
						<text class="balance-desc">来自邀请用户充值分成</text>
					</view>
					<view class="balance-actions">
						<view class="action-btn primary" @click.stop="handleWithdraw">
							<text class="action-icon">💸</text>
							<text class="action-text">立即提现</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 错误信息显示 -->
			<view v-if="errorMessage" class="error-banner">
				<view class="error-icon">⚠️</view>
				<text class="error-text">{{ errorMessage }}</text>
			</view>

			<!-- 选项卡导航 -->
			<view class="modern-tabs">
				<view class="tab-nav">
					<view class="tab-item" :class="{ active: activeTab === 'balance' }" @click="switchTab('balance')">
						<text class="tab-icon">📊</text>
						<text class="tab-text">余额明细</text>
					</view>
					<view class="tab-item" :class="{ active: activeTab === 'withdraw' }" @click="switchTab('withdraw')">
						<text class="tab-icon">📋</text>
						<text class="tab-text">提现记录</text>
					</view>
				</view>
			</view>

			<!-- 内容区域 -->
			<view class="tab-content">
				<!-- 余额明细 -->
				<view v-if="activeTab === 'balance'" class="content-section">
					<view class="records-container">
						<view v-if="balanceLoading" class="loading-state">
							<view class="loading-spinner"></view>
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="balanceHistory.length === 0" class="empty-state">
							<text class="empty-icon">📊</text>
							<text class="empty-title">暂无明细记录</text>
							<text class="empty-desc">您的余额变动记录将在这里显示</text>
						</view>
						<view v-else class="records-list">
							<view v-for="(item, index) in balanceHistory" :key="index" class="record-item">
								<view class="record-icon" :class="item.amount > 0 ? 'income' : 'expense'">
									<text class="icon-text">{{ item.amount > 0 ? '💰' : '💸' }}</text>
								</view>
								<view class="record-info">
									<text class="record-title">{{ item.description }}</text>
									<text class="record-time">{{ item.time }}</text>
								</view>
								<view class="record-amount" :class="item.amount > 0 ? 'income' : 'expense'">
									<text class="amount-text">{{ item.amount > 0 ? '+' : '' }}¥{{ item.amount }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 提现记录 -->
				<view v-if="activeTab === 'withdraw'" class="content-section">
					<view class="records-container">
						<view v-if="withdrawLoading" class="loading-state">
							<view class="loading-spinner"></view>
							<text class="loading-text">加载中...</text>
						</view>
						<view v-else-if="withdrawHistory.length === 0" class="empty-state">
							<text class="empty-icon">📋</text>
							<text class="empty-title">暂无提现记录</text>
							<text class="empty-desc">您的提现申请记录将在这里显示</text>
						</view>
						<view v-else class="records-list">
							<view v-for="(item, index) in withdrawHistory" :key="index" class="withdraw-record">
								<!-- 头部：详细信息和状态/金额 -->
								<view class="withdraw-header">
									<view class="withdraw-info-section">
										<view class="info-row">
											<text class="info-icon">💳</text>
											<text class="info-text">{{ item.withdrawType || '银行卡' }}</text>
										</view>
										<view class="info-row">
											<text class="info-icon">⏰</text>
											<text class="info-text">{{ item.time }}</text>
										</view>
									</view>
									<view class="withdraw-right-section">
										<view class="withdraw-status-badge" :class="item.statusClass">
											<text class="status-text">{{ item.status }}</text>
										</view>
										<view class="withdraw-amount-section">
											<text class="amount-value">¥{{ item.amount }}</text>
										</view>
									</view>
								</view>

								<!-- 驳回原因 -->
								<view v-if="item.rejectReason" class="reject-reason">
									<text class="reject-icon">❌</text>
									<text class="reject-label">驳回原因：</text>
									<text class="reject-text">{{ item.rejectReason }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 提现表单 -->
				<view v-if="activeTab === 'withdrawForm'" class="content-section">
					<view class="section-header">
						<text class="section-title">提现申请</text>
					</view>

					<view class="withdraw-form-container">
						<view class="form-card">
							<!-- 提现金额 -->
							<view class="form-group">
								<view class="form-label-row">
									<text class="form-label">提现金额</text>
									<text class="form-note">（全额提现）</text>
								</view>
								<view class="amount-display-card">
									<text class="amount-symbol">¥</text>
									<text class="amount-value">{{ userInfo.balance || 0 }}</text>
								</view>
							</view>

							<!-- 费用明细 -->
							<view class="fee-breakdown">
								<view class="breakdown-item">
									<text class="breakdown-label">提现金额</text>
									<text class="breakdown-value">¥{{ userInfo.balance || 0 }}</text>
								</view>
								<view v-if="partnerConfig && partnerConfig.withdrawalFee > 0" class="breakdown-item fee">
									<text class="breakdown-label">手续费 ({{ partnerConfig.withdrawalFee }}%)</text>
									<text class="breakdown-value fee-amount">-¥{{ calculateFee() }}</text>
								</view>
								<view class="breakdown-divider"></view>
								<view class="breakdown-item total">
									<text class="breakdown-label">实际到账</text>
									<text class="breakdown-value total-amount">¥{{ calculateActualAmount() }}</text>
								</view>
							</view>

							<!-- 支付宝信息 -->
							<view class="form-group">
								<text class="form-label">支付宝账号</text>
								<view class="input-wrapper">
									<textarea
										class="modern-input"
										v-model="withdrawForm.account"
										placeholder="请输入支付宝账号"
										rows="1"
										@input="validateAccount"
									></textarea>
									<view v-if="accountError" class="input-error">
										<text class="error-text">{{ accountError }}</text>
									</view>
								</view>
							</view>

							<view class="form-group">
								<text class="form-label">真实姓名</text>
								<view class="input-wrapper">
									<textarea
										class="modern-input"
										v-model="withdrawForm.name"
										placeholder="请输入真实姓名"
										rows="1"
										@input="validateName"
									></textarea>
									<view v-if="nameError" class="input-error">
										<text class="error-text">{{ nameError }}</text>
									</view>
								</view>
							</view>

							<!-- 微信提现说明 -->
							<view v-if="partnerConfig && partnerConfig.withdrawalChannel === 'merchant'" class="form-group">
								<view class="info-card">
									<view class="info-icon">💚</view>
									<text class="info-text">微信提现将自动转账到您的微信零钱</text>
								</view>
							</view>

							<!-- 操作按钮 -->
							<view class="form-actions">
								<view class="action-btn secondary" @click="cancelWithdraw">
									<text class="btn-text">取消</text>
								</view>
								<view class="action-btn primary" @click="submitWithdrawInModal" :class="{ disabled: !canSubmit() || isSubmitting }">
									<view v-if="isSubmitting" class="btn-loading">
										<view class="loading-spinner"></view>
										<text class="btn-text">提交中...</text>
									</view>
									<text v-else class="btn-text">{{ partnerConfig && partnerConfig.withdrawalChannel === 'merchant' ? '确认提现' : '申请提现' }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>



	<!-- 团队弹窗 - 现代化设计 -->
	<view v-if="showTeamModal" class="modern-team-modal-overlay" @click="closeTeamModal">
		<view class="modern-team-modal-card" @click.stop>
			<!-- 头部区域 -->
			<view class="modern-team-header">
				<view class="header-left">
					<view class="header-icon-wrapper">
						<text class="header-icon">🚀</text>
					</view>
					<view class="header-text">
						<text class="header-title">我的团队</text>
						<text class="header-subtitle">Team Dashboard</text>
					</view>
				</view>
				<view class="header-close" @click="closeTeamModal">
					<text class="close-text">×</text>
				</view>
			</view>

			<!-- 内容区域 -->
			<view class="modern-team-content">
				<!-- 统计概览 -->
				<view class="stats-overview">
					<view class="overview-header">
						<text class="overview-title">团队概览</text>
						<view class="total-badge">
							<text class="total-count">{{ teamData.totalTeamCount }}</text>
						</view>
					</view>

					<view class="stats-cards-row">
						<view class="modern-stat-card primary-card" @click="showTeamDetails">
							<view class="card-top">
								<view class="card-icon-bg">
									<text class="card-icon">👥</text>
								</view>
								<view class="trend-indicator">
									<text class="trend-arrow">↗</text>
								</view>
							</view>
							<view class="card-bottom">
								<text class="card-number">{{ teamData.firstLevelCount }}</text>
								<text class="card-label">直推成员</text>
							</view>
						</view>

						<view class="modern-stat-card secondary-card" @click="showTeamDetails">
							<view class="card-top">
								<view class="card-icon-bg">
									<text class="card-icon">🌐</text>
								</view>
								<view class="trend-indicator">
									<text class="trend-arrow">↗</text>
								</view>
							</view>
							<view class="card-bottom">
								<text class="card-number">{{ teamData.secondLevelCount }}</text>
								<text class="card-label">间推成员</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 成员列表 -->
				<view class="members-overview">
					<view class="members-header">
						<text class="members-title">最新成员</text>
						<view class="view-all" @click="showTeamDetails">
							<text class="view-all-text">查看全部</text>
							<text class="view-all-arrow">→</text>
						</view>
					</view>

					<view v-if="teamLoading" class="modern-loading">
						<view class="loading-spinner"></view>
						<text class="loading-text">加载中...</text>
					</view>

					<view v-else-if="teamData.recentMembers.length === 0" class="modern-empty">
						<view class="empty-icon-bg">
							<text class="empty-icon">🎯</text>
						</view>
						<text class="empty-title">开始邀请好友</text>
						<text class="empty-desc">建设您的专属团队</text>
					</view>

					<view v-else class="modern-members-list">
						<view class="modern-member-item" v-for="(member, index) in teamData.recentMembers.slice(0, 4)" :key="member.id">
							<view class="member-avatar-section">
								<view class="member-avatar-circle">
									<text class="avatar-letter">{{ getAvatarText(member) }}</text>
								</view>
								<view class="level-badge" :class="member.level === 1 ? 'level-one' : 'level-two'">
									<text class="level-text">{{ member.level === 1 ? 'L1' : 'L2' }}</text>
								</view>
							</view>
							<view class="member-info-section">
								<text class="member-name-text">{{ member.name || '用户' + member.id }}</text>
								<text class="member-phone-text">{{ formatPhone(member.phone) }}</text>
							</view>
							<view class="member-time-section">
								<text class="join-time-text">{{ formatTime(member.createdAt) }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 团队详情弹窗 -->
	<view v-if="showTeamDetailModal" class="team-detail-modal-overlay" @click="hideTeamDetails">
		<view class="team-detail-modal" @click.stop>
			<view class="modal-header">
				<text class="modal-title">推广统计详情</text>
				<text class="modal-close" @click="hideTeamDetails">×</text>
			</view>

			<view class="modal-content">
				<view class="promoter-info">
					<text class="info-title">推广员信息</text>
					<view class="info-item">
						<text class="info-label">ID:</text>
						<text class="info-value">{{ userInfo.id }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">账户:</text>
						<text class="info-value">{{ userInfo.phone }}</text>
					</view>
				</view>

				<view class="stats-detail">
					<view class="detail-card primary">
						<view class="detail-header">
							<text class="detail-icon">👥</text>
							<text class="detail-title">一级推广</text>
						</view>
						<text class="detail-count">{{ teamData.firstLevelReferrals.length }}人</text>
						<view class="detail-list">
							<view v-if="teamData.firstLevelReferrals.length === 0" class="empty-tip">
								暂无推广用户
							</view>
							<view v-for="user in teamData.firstLevelReferrals" :key="user.id" class="user-item">
								<text class="user-id">{{ user.id }}</text>
								<text class="user-info">{{ user.name || '用户' + user.id }} ({{ formatPhone(user.phone) }})</text>
							</view>
						</view>
					</view>

					<view class="detail-card secondary">
						<view class="detail-header">
							<text class="detail-icon">🌐</text>
							<text class="detail-title">二级推广</text>
						</view>
						<text class="detail-count">{{ teamData.secondLevelReferrals.length }}人</text>
						<view class="detail-list">
							<view v-if="teamData.secondLevelReferrals.length === 0" class="empty-tip">
								暂无推广用户
							</view>
							<view v-for="user in teamData.secondLevelReferrals" :key="user.id" class="user-item">
								<text class="user-id">{{ user.id }}</text>
								<text class="user-info">{{ user.name || '用户' + user.id }} ({{ formatPhone(user.phone) }})</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="modal-footer">
				<view class="modal-button" @click="hideTeamDetails">
					<text class="button-text">关闭</text>
				</view>
			</view>
		</view>
	</view>

	<!-- 现代化大气点数明细弹窗 -->
	<view v-if="showPointsModal" class="premium-points-overlay" @click="closePointsModal">
		<view class="premium-points-modal" @click.stop>
			<!-- 动态背景装饰 -->
			<view class="modal-bg-decoration">
				<view class="floating-orb orb-1"></view>
				<view class="floating-orb orb-2"></view>
				<view class="floating-orb orb-3"></view>
			</view>

			<!-- 弹窗头部区域 -->
			<view class="premium-header">
				<view class="header-content">
					<view class="header-icon-wrapper">
						<view class="icon-glow"></view>
						<text class="header-icon">💎</text>
					</view>
					<view class="header-text-group">
						<text class="header-title">点数资产</text>
						<text class="header-subtitle">Points Assets</text>
					</view>
				</view>
				<view class="close-btn" @click="closePointsModal">
					<text class="close-icon">✕</text>
				</view>
			</view>

			<!-- 点数概览仪表板 -->
			<view class="points-dashboard">
				<view class="dashboard-card main-balance">
					<view class="card-header">
						<text class="card-title">当前余额</text>
						<view class="balance-trend">
							<text class="trend-icon">📈</text>
						</view>
					</view>
					<view class="balance-display">
						<text class="balance-number">{{ getCurrentPoints() }}</text>
						<text class="balance-unit">点数</text>
					</view>
					<view class="balance-subtitle">可用点数余额</view>
				</view>
			</view>

			<!-- 交易记录区域 -->
			<view class="transaction-section">
				<view class="section-header">
					<text class="section-title">交易记录</text>
					<view class="record-filter">
						<text class="filter-text">总共{{ pointsHistory.length }}条</text>
					</view>
				</view>

				<view class="transaction-container">
					<!-- 加载状态 -->
					<view v-if="pointsLoading" class="loading-state">
						<view class="loading-animation">
							<view class="loading-dot dot1"></view>
							<view class="loading-dot dot2"></view>
							<view class="loading-dot dot3"></view>
						</view>
						<text class="loading-text">正在加载交易记录...</text>
					</view>

					<!-- 空状态 -->
					<view v-else-if="pointsHistory.length === 0" class="empty-state">
						<view class="empty-illustration">
							<text class="empty-icon">📊</text>
						</view>
						<text class="empty-title">暂无交易记录</text>
						<text class="empty-desc">您还没有任何点数交易记录</text>
					</view>

					<!-- 交易列表 -->
					<view v-else class="transaction-list">
						<view v-for="(item, index) in pointsHistory" :key="index" class="transaction-item">
							<view class="transaction-icon" :class="item.amount > 0 ? 'income' : 'expense'">
								<text class="icon-symbol">{{ getPointsTypeIcon(item.type) }}</text>
							</view>
							<view class="transaction-details">
								<view class="transaction-main">
									<text class="transaction-title">{{ item.description }}</text>
									<text class="transaction-amount" :class="item.amount > 0 ? 'income' : 'expense'">
										{{ item.amount > 0 ? '+' : '' }}{{ item.amount }}
									</text>
								</view>
								<view class="transaction-meta">
									<text class="transaction-time">{{ item.time }}</text>
									<text v-if="item.balance" class="transaction-balance">余额: {{ item.balance }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 海报生成弹窗 -->
	<view v-if="showPosterModal" class="poster-modal-overlay" @tap="closePosterModal">
		<view class="poster-modal" @tap.stop>
			<view class="poster-modal-header">
				<text class="poster-modal-title">推广海报</text>
				<text class="poster-modal-close" @tap="closePosterModal">×</text>
			</view>



			<view class="poster-modal-content">


				<!-- 加载状态 -->
				<view v-if="posterGenerating" class="poster-loading">
					<text class="loading-text">正在生成海报...</text>
				</view>

				<!-- 海报预览 - 只在有背景图片时显示 -->
				<view v-if="posterConfig.posterbackgroundimage && (qrCodeUrl || inviteCode)" class="poster-preview">

					<view class="poster-container" :style="posterContainerStyle">
						<!-- 背景图片 -->
						<view
							class="poster-background"
							:style="{
								backgroundImage: `url(${getFullImageUrl(posterConfig.posterbackgroundimage)})`,
								backgroundSize: 'cover',
								backgroundPosition: 'center',
								backgroundRepeat: 'no-repeat'
							}"
							@error="onBackgroundImageError"
							@load="onBackgroundImageLoad"
						></view>

						<!-- 二维码叠加 -->
						<view
							class="qrcode-overlay"
							:style="qrCodeOverlayStyle()"
						>
							<image
								v-if="qrCodeUrl"
								:src="qrCodeUrl"
								class="qrcode-image"
								mode="aspectFit"
								@error="onQRCodeImageError"
								@load="onQRCodeImageLoad"
							></image>
							<!-- 二维码占位符 -->
							<view v-else class="qrcode-placeholder">
								<text class="placeholder-text">二维码</text>
								<text class="placeholder-code">{{ inviteCode || '生成中...' }}</text>
							</view>
						</view>
					</view>

					<!-- 邀请信息 -->
					<view class="invite-info">
						<!-- 邀请码已隐藏，邀请链接包含完整信息 -->
						<view class="invite-item">
							<text class="invite-label">邀请链接：</text>
							<text class="invite-value">{{ inviteUrl }}</text>
							<text class="copy-btn" @tap="copyInviteUrl">复制</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="poster-actions">
						<button class="action-btn save-btn" @tap="savePosterToAlbum">保存到相册</button>
						<button class="action-btn share-btn" @tap="sharePoster">分享海报</button>
					</view>
				</view>

				<!-- 错误状态 -->
				<view v-else class="poster-error">
					<view class="error-icon">⚠️</view>
					<text class="error-text">海报生成失败</text>
					<text class="error-desc">请检查网络连接后重试</text>
					<button class="retry-btn" @tap="initPosterGeneration">重新生成</button>
				</view>
			</view>

			<!-- 隐藏的Canvas -->
			<!-- #ifdef H5 -->
			<canvas
				ref="posterCanvas"
				id="posterCanvas"
				width="375"
				height="667"
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px', position: 'fixed', left: '-2000px', top: '-2000px', zIndex: -999}"
			></canvas>
			<!-- #endif -->

			<!-- #ifndef H5 -->
			<canvas
				canvas-id="posterCanvas"
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px', position: 'fixed', left: '-2000px', top: '-2000px', zIndex: -999}"
			></canvas>
			<!-- #endif -->
		</view>
	</view>

	<!-- 🌟 现代化VIP套餐弹窗 -->
	<view v-if="showPackageModal" class="modern-package-modal-overlay" @click="closePackageModal">
		<view class="modern-package-modal-container" @click.stop>
			<!-- 弹窗头部 -->
			<view class="modern-package-modal-header">
				<view class="header-content">
					<text class="modal-title">✨ 我的VIP套餐</text>
				</view>
				<view class="close-button" @click="closePackageModal">
					<text class="close-icon">×</text>
				</view>
			</view>

			<!-- 弹窗内容 -->
			<scroll-view class="modern-package-modal-content" scroll-y="true">
				<!-- 加载状态 -->
				<view v-if="packageLoading" class="loading-state">
					<view class="loading-spinner"></view>
					<text class="loading-text">正在加载套餐信息...</text>
				</view>

				<!-- 空状态 -->
				<view v-else-if="packageHistory.length === 0" class="empty-state">
					<text class="empty-icon">📦</text>
					<text class="empty-text">暂无套餐记录</text>
					<text class="empty-desc">购买套餐后将在此显示</text>
				</view>

				<!-- 套餐列表 -->
				<view v-else class="modern-package-list">
					<!-- 当前使用中的套餐 -->
					<view v-for="(item, index) in packageHistory.filter(p => p.status === '使用中')" :key="'active-' + index" class="vip-package-card active">
						<view class="vip-card-header">
							<view class="package-title-section">
								<text class="package-name">{{ item.packageName }}</text>
								<view class="status-badge status-active">
									<text class="status-text">{{ item.status }}</text>
								</view>
							</view>
							<view class="package-price">
								<text class="price-symbol">¥</text>
								<text class="price-value">{{ item.amount }}</text>
							</view>
						</view>

						<view class="vip-card-body">
							<view class="info-grid">
								<view class="info-item">
									<text class="info-label">开通时间</text>
									<text class="info-value">{{ item.startTime }}</text>
								</view>
								<view class="info-item">
									<text class="info-label">到期时间</text>
									<text class="info-value">{{ item.endTime }}</text>
								</view>
								<view class="info-item">
									<text class="info-label">总点数</text>
									<text class="info-value highlight">{{ item.totalPoints }}点</text>
								</view>
								<view class="info-item">
									<text class="info-label">剩余天数</text>
									<text class="info-value remaining">{{ item.remainingDays }}天</text>
								</view>
							</view>

							<view class="daily-limit-section">
								<text class="limit-label">每日使用限制</text>
								<text class="limit-value">{{ item.dailyLimit === 0 ? '无限制' : item.dailyLimit + '点/天' }}</text>
							</view>
						</view>
					</view>

					<!-- 历史套餐 -->
					<view v-if="packageHistory.filter(p => p.status !== '使用中').length > 0" class="history-section">
						<text class="history-title">历史套餐</text>
						<view v-for="(item, index) in packageHistory.filter(p => p.status !== '使用中')" :key="'history-' + index" class="package-card history">
							<view class="card-header">
								<view class="package-info">
									<text class="package-name">{{ item.packageName }}</text>
									<view class="status-badge status-expired">
										<text class="status-text">{{ item.status }}</text>
									</view>
								</view>
								<text class="package-price">¥{{ item.amount }}</text>
							</view>

							<view class="card-details">
								<view class="detail-row">
									<text class="detail-label">使用期间</text>
									<text class="detail-value">{{ item.startTime }} - {{ item.endTime }}</text>
								</view>
								<view class="detail-row">
									<text class="detail-label">套餐配额</text>
									<text class="detail-value">{{ item.totalPoints }}点 ({{ item.dailyLimit === 0 ? '无限制' : item.dailyLimit + '点/天' }})</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>

			<!-- 弹窗底部 -->
			<view class="modern-package-modal-footer">
				<view class="footer-content">
					<text class="footer-text">💎 套餐点数到期未使用将清零</text>
					<text class="footer-desc">如有疑问请联系客服</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import memberAPI, { userStore } from '@/api/members.js'
import { packageAPI, orderAPI } from '@/api/packages.js'
import SmartLoginModal from '@/components/smart-login-modal.vue'
import MagicNavigation from '@/components/MagicNavigation.vue'
import PersonalSettingsModal from '@/components/personal-settings-modal.vue'
import AccountSecurityModal from '@/components/account-security-modal.vue'
import CustomerServiceModal from '@/components/customer-service-modal.vue'
import { API_BASE_URL, IMAGE_BASE_URL } from '@/config/index.js'

export default {
	name: 'ProfileIndex',
	components: {
		SmartLoginModal,
		MagicNavigation,
		PersonalSettingsModal,
		AccountSecurityModal,
		CustomerServiceModal
	},
	data: function() {
		return {
			API_BASE_URL,
			IMAGE_BASE_URL,
			userInfo: {},
			isLoggedIn: false,
			refreshing: false,
			showLoginModalFlag: false,
			showSettingsModal: false, // 设置弹窗显示状态
			showPersonalSettingsModal: false, // 个人设置弹窗显示状态
			showAccountSecurityModal: false, // 账户安全弹窗显示状态
			showCustomerServiceModal: false, // 客服弹窗显示状态
			partnerConfig: null, // 合伙人配置
			showPartnerBenefitsModal: false, // 合伙人权益弹窗显示状态
			showVipBenefitsModal: false, // VIP权益弹窗显示状态
			partnerUpgradeCondition: 30, // 合伙人升级条件（邀请人数）
			promoterCashback: 5, // 推广员返现比例
			promoterCommission: 2, // 推广员分成比例
			minWithdrawal: 10, // 最低提现金额
			userInviteCount: 0, // 用户邀请人数
			showWalletModal: false, // 钱包弹窗显示状态
			showPointsModal: false, // 点数弹窗显示状态
			showPackageModal: false, // 套餐弹窗显示状态
			showTeamModal: false, // 团队弹窗显示状态
			activeTab: 'balance', // 当前选中的标签页
			// 余额明细数据
			balanceHistory: [],
			balanceLoading: false,
			// 点数明细数据
			pointsHistory: [],
			pointsLoading: false,
			// 提现记录数据
			withdrawHistory: [],
			withdrawLoading: false,
			// 套餐记录数据
			packageHistory: [],
			packageLoading: false,
			// 团队数据
			teamData: {
				firstLevelCount: 0,
				secondLevelCount: 0,
				totalTeamCount: 0,
				firstLevelReferrals: [],
				secondLevelReferrals: [],
				recentMembers: [],
				allMembers: []
			},
			teamLoading: false,
			showTeamDetailModal: false, // 团队详情弹窗
			// 提现表单数据
			withdrawForm: {
				account: '',
				name: ''
			},
			// 错误信息
			errorMessage: '',
			// 提交状态
			isSubmitting: false,
			// 表单验证错误
			accountError: '',
			nameError: '',
			// 导航栏相关
			currentNavIndex: 3, // 个人中心对应索引3（吾界）
			navItems: [
				{ text: '初页', icon: 'icon-home', path: '/pages/index/index' },
				{ text: '会享', icon: 'icon-message', path: '/pages/chat/index' },
				{ text: '往档', icon: 'icon-history', path: '/pages/history/index' },
				{ text: '吾界', icon: 'icon-user', path: '/pages/profile/index' }
			],
			// 海报生成相关
			showPosterModal: false,
			posterGenerating: false,
			posterImageUrl: '',
			qrCodeUrl: '',
			inviteCode: '',
			inviteUrl: '',
			isSaving: false, // 防止重复保存
			// 二维码缓存
			qrCodeCache: {}, // 格式: { userId: { qrCodeUrl: '', inviteCode: '', inviteUrl: '', timestamp: 0 } }
			posterConfig: {
				backgroundImage: '', // 从后台配置加载
				posterbackgroundimage: '', // 模板中使用的属性名
				qrCodeSize: 150, // 二维码大小，匹配后台设置
				qrCodePosition: { x: 225, y: 517 } // 匹配后台设置的位置
			},
			backgroundImageError: false,
			shareConfig: {
				title: 'AI智能体使用邀请',
				description: '注册即可获得额外免费使用次数，快来体验智能AI助手！'
			},
			canvasWidth: 375,
			canvasHeight: 667
		}
	},

	computed: {
		// 判断是否为合伙人
		isPartner() {
			// 方法1：检查用户的isPartner字段
			if (this.userInfo.isPartner) {
				return true
			}
			// 方法2：检查邀请人数是否达到升级条件
			return this.userInviteCount >= this.partnerUpgradeCondition
		},

		// 判断是否为VIP会员
		isVipMember() {
			// 检查用户是否有有效的套餐
			return !!(this.userInfo.packageInfo && this.userInfo.packageInfo.id)
		}
	},

	async onLoad() {
		this.checkLoginStatus()
		// 如果已登录，刷新用户信息
		if (this.isLoggedIn) {
			try {
				await this.refreshUserInfo()
			} catch (error) {
				console.error('页面加载时刷新用户信息失败:', error)
			}
		}
		// 加载合伙人配置
		this.loadPartnerConfig()
		// 预加载海报配置
		this.loadPosterConfig()
	},

	onShow() {
		this.checkLoginStatus()
		// 如果已登录，刷新用户信息
		if (this.isLoggedIn) {
			this.refreshUserInfo()
		}
	},
	methods: {
		// 获取完整的图片URL
		getFullImageUrl(imagePath) {
			if (!imagePath) {
				console.log('⚠️ getFullImageUrl: imagePath为空')
				return ''
			}
			// 如果已经是完整URL，直接返回
			if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
				console.log('🔗 getFullImageUrl: 已是完整URL:', imagePath)
				return imagePath
			}
			// 拼接完整URL
			const fullUrl = `${this.IMAGE_BASE_URL}${imagePath.startsWith('/') ? '' : '/'}${imagePath}`
			console.log('🔗 getFullImageUrl: 拼接URL:', { imagePath, IMAGE_BASE_URL: this.IMAGE_BASE_URL, fullUrl })
			return fullUrl
		},

		// 计算海报容器样式 - 自适应显示
		posterContainerStyle() {
			// 获取屏幕宽度，设置海报最大宽度为屏幕宽度的70%
			const screenWidth = uni.getSystemInfoSync().windowWidth
			const maxWidth = Math.min(screenWidth * 0.7, 320) // 减小最大宽度，确保在弹窗中完整显示

			// 根据海报内容计算高度，使用更合适的比例
			// 考虑到需要显示标题、邀请文本、二维码等内容，使用3:4比例（宽:高）
			const aspectRatio = 3 / 4
			const calculatedHeight = maxWidth / aspectRatio

			// 设置最小高度，确保内容完整显示，但不要太高
			const minHeight = 400
			const maxHeight = 600 // 设置最大高度，避免超出弹窗
			const height = Math.max(minHeight, Math.min(calculatedHeight, maxHeight))

			console.log('📐 海报尺寸计算:', {
				screenWidth,
				maxWidth,
				aspectRatio,
				calculatedHeight,
				finalHeight: height
			})

			return {
				width: `${maxWidth}px`,
				height: `${height}px`,
				maxWidth: '90vw',
				margin: '0 auto'
			}
		},

		// 计算二维码叠加层样式
		qrCodeOverlayStyle() {
			const size = this.posterConfig.qrCodeSize || 150
			const position = this.posterConfig.qrCodePosition || { x: 225, y: 517 }

			console.log('🎯 计算二维码样式:', {
				size,
				position,
				posterConfig: this.posterConfig
			})

			// 获取海报容器的实际尺寸用于调试
			const container = this.$refs.posterContainer
			if (container) {
				const rect = container.getBoundingClientRect()
				console.log('📏 海报容器实际尺寸:', {
					width: rect.width,
					height: rect.height,
					ratio: rect.width / rect.height
				})
			}

			// 使用与后台管理界面完全一致的基准尺寸
			// 后台海报容器尺寸：375px × 667px
			const originalWidth = 375   // 与后台管理界面一致
			const originalHeight = 667  // 与后台管理界面一致

			// 直接按后台坐标计算百分比位置
			const leftPercent = (position.x / originalWidth) * 100
			const topPercent = (position.y / originalHeight) * 100

			console.log('📊 使用后台基准尺寸计算位置:', {
				backendSize: { width: originalWidth, height: originalHeight },
				backendPosition: { x: position.x, y: position.y },
				calculatedPercent: { left: leftPercent, top: topPercent }
			})
			const sizePercent = (size / originalWidth) * 100

			// 放宽尺寸限制，让后台设置的150px能够正确显示
			const minSizePercent = 5   // 最小5%
			const maxSizePercent = 50  // 最大50%，给更大的空间
			const finalSizePercent = Math.max(minSizePercent, Math.min(sizePercent, maxSizePercent))

			// 直接使用设置的位置，不做边界限制（因为我们已经计算好了）
			const finalLeftPercent = leftPercent
			const finalTopPercent = topPercent

			const style = {
				left: `${finalLeftPercent}%`,
				top: `${finalTopPercent}%`,
				width: `${finalSizePercent}%`,
				height: 'auto', // 保持二维码正方形比例
				aspectRatio: '1/1'
			}

			console.log('🎨 计算出的二维码样式:', style)
			console.log('📐 原始位置:', position, '转换为百分比:', { leftPercent, topPercent, sizePercent })
			console.log('📏 最终样式:', { finalLeftPercent, finalTopPercent, finalSizePercent })
			return style
		},

		// 检查登录状态
		checkLoginStatus() {
			this.isLoggedIn = userStore.isLoggedIn()
			console.log('checkLoginStatus - isLoggedIn:', this.isLoggedIn)
			if (this.isLoggedIn) {
				this.userInfo = userStore.getCurrentUser()
				console.log('当前用户信息:', this.userInfo)
				console.log('用户ID:', this.userInfo?.id)
				console.log('用户ID类型:', typeof this.userInfo?.id)
			} else {
				this.userInfo = {}
				console.log('用户未登录，清空用户信息')
			}
		},

		// 下拉刷新
		async onRefresh() {
			this.refreshing = true
			try {
				await this.refreshUserInfo()
				uni.showToast({
					title: '刷新成功',
					icon: 'success',
					duration: 1000
				})
			} catch (error) {
				uni.showToast({
					title: '刷新失败',
					icon: 'none'
				})
			} finally {
				this.refreshing = false
			}
		},

		// 刷新用户信息
		async refreshUserInfo() {
			try {
				// 先从本地获取用户信息
				const currentUser = userStore.getCurrentUser()
				if (currentUser) {
					this.userInfo = currentUser
					console.log('本地用户信息:', this.userInfo)
				}

				// 调用后端API获取最新信息
				const response = await memberAPI.getCurrentUser()
				if (response && response.success) {
					this.userInfo = response.data
					userStore.setCurrentUser(response.data)
					console.log('从后端获取的最新用户信息:', this.userInfo)

					// 获取用户邀请人数
					await this.loadUserInviteCount()
				}
			} catch (error) {
				console.error('刷新用户信息失败:', error)
				// 如果API调用失败，继续使用本地数据
				const currentUser = userStore.getCurrentUser()
				if (currentUser) {
					this.userInfo = currentUser
				}
				throw error
			}
		},

		// 获取会员状态文本
		getMemberStatusText() {
			if (!this.userInfo.status) return '普通用户'
			switch (this.userInfo.status) {
				case 'active': return '活跃会员'
				case 'vip': return 'VIP会员'
				case 'partner': return '合伙人'
				default: return '普通用户'
			}
		},

		// 点击头像
		handleAvatarClick() {
			if (!this.isLoggedIn) {
				console.log('用户未登录，显示登录弹窗');
				this.showLoginModal();
			} else {
				// 直接选择图片（优先从相册选择，也支持拍照）
				this.chooseImage(['album', 'camera']);
			}
		},

		// 选择图片
		chooseImage(sourceType) {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'], // 压缩图片
				sourceType: sourceType, // 直接传入数组，支持多种来源
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0];
					console.log('选择的图片路径:', tempFilePath);

					// 直接上传，不显示确认弹窗
					this.uploadAvatar(tempFilePath);
				},
				fail: (err) => {
					console.error('选择图片失败:', err);
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					});
				}
			});
		},

		// 上传头像
		async uploadAvatar(filePath) {
			console.log('开始上传头像:', filePath);
			// 使用主服务器端口3030
			const uploadUrl = this.API_BASE_URL + '/api/members/upload-avatar';
			console.log('API地址:', uploadUrl);
			console.log('用户Token:', userStore.getToken());

			uni.showLoading({
				title: '上传中...'
			});

			try {
				// 使用uni.uploadFile上传图片
				const uploadResult = await new Promise((resolve, reject) => {
					uni.uploadFile({
						url: uploadUrl,
						filePath: filePath,
						name: 'avatar',
						header: {
							'Authorization': 'Bearer ' + userStore.getToken()
						},
						success: (res) => {
							console.log('上传响应完整信息:', res);
							console.log('响应状态码:', res.statusCode);
							console.log('响应数据:', res.data);

							if (res.statusCode === 200) {
								try {
									const data = JSON.parse(res.data);
									console.log('解析后的数据:', data);
									if (data.success) {
										resolve(data);
									} else {
										reject(new Error(data.message || '上传失败'));
									}
								} catch (e) {
									console.error('解析响应失败:', e);
									reject(new Error('解析响应失败: ' + e.message));
								}
							} else {
								reject(new Error(`服务器错误: ${res.statusCode}`));
							}
						},
						fail: (err) => {
							console.error('上传请求失败:', err);
							console.error('错误详情:', JSON.stringify(err));
							reject(new Error(`网络请求失败: ${err.errMsg || '未知错误'}`));
						}
					});
				});

				// 上传成功，更新本地用户信息
				console.log('完整上传结果:', JSON.stringify(uploadResult, null, 2));

				// 检查不同的响应格式
				let avatarUrl = null;
				if (uploadResult.data && uploadResult.data.avatarUrl) {
					avatarUrl = uploadResult.data.avatarUrl;
				} else if (uploadResult.avatarUrl) {
					avatarUrl = uploadResult.avatarUrl;
				} else if (uploadResult.data && uploadResult.data.avatar) {
					avatarUrl = uploadResult.data.avatar;
				}

				console.log('提取的头像URL:', avatarUrl);

				if (avatarUrl) {
					// 添加时间戳避免缓存问题
					const avatarUrlWithTimestamp = avatarUrl + '?t=' + Date.now();
					console.log('最终头像URL:', avatarUrlWithTimestamp);

					this.userInfo.avatar = avatarUrlWithTimestamp;
					userStore.setCurrentUser(this.userInfo);

					uni.showToast({
						title: '头像更换成功',
						icon: 'success'
					});

					// 强制刷新页面数据
					this.$forceUpdate();
				} else {
					console.error('未找到头像URL，响应结构:', uploadResult);
					uni.showToast({
						title: '头像URL获取失败',
						icon: 'none'
					});
				}

			} catch (error) {
				console.error('上传头像失败:', error);
				uni.showToast({
					title: error.message || '上传失败',
					icon: 'none',
					duration: 3000
				});
			} finally {
				uni.hideLoading();
			}
		},

		// 跳转到登录页面
		goToLogin() {
			uni.navigateTo({
				url: '/pages/auth/login'
			})
		},

		// 显示智能登录弹窗
		showLoginModal() {
			this.showLoginModalFlag = true
		},

		// 隐藏智能登录弹窗
		hideLoginModal() {
			this.showLoginModalFlag = false
		},

		// 处理登录成功
		handleLoginSuccess(memberData) {
			console.log('登录成功:', memberData)
			// 刷新页面状态
			this.checkLoginStatus()
			// 可以添加其他登录成功后的处理逻辑
		},

		// 测试登录功能
		testLogin() {
			// 模拟登录测试用户ID=11
			const testUser = {
				id: 11,
				phone: '13800138000',
				name: '测试用户',
				username: '测试用户',
				nickname: '测试用户',
				balance: 150,
				points: 1250, // 给测试用户一些点数
				inviteCount: 0,
				status: 'active'
			};
			const testToken = 'test-token';

			// 使用userStore保存用户信息
			userStore.setToken(testToken);
			userStore.setCurrentUser(testUser);

			// 刷新页面状态
			this.checkLoginStatus();

			uni.showToast({
				title: '测试登录成功',
				icon: 'success'
			});
		},

		// 退出登录
		logout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						userStore.clearCurrentUser()
						this.checkLoginStatus()
						uni.showToast({
							title: '已退出登录',
							icon: 'success'
						})
					}
				}
			})
		},

		upgradeMembership: async function() {
			if (!this.isLoggedIn) {
				console.log('用户未登录，显示登录弹窗');
				this.showLoginModal();
				return;
			}

			console.log('🎯 显示推广海报弹窗');
			// 显示海报生成弹窗
			this.showPosterModal = true;

			// 强制设置测试图片
			this.posterConfig.posterbackgroundimage = '/images/images/20250711/image_1752246578577.png';
			console.log('🧪 强制设置测试图片:', this.posterConfig.posterbackgroundimage);

			// 先加载海报配置
			await this.loadPosterConfig();

			// 然后初始化海报生成
			await this.initPosterGeneration();
		},
		async navigateToModule(moduleType) {
			console.log('导航到模块:', moduleType)
			console.log('当前登录状态:', this.isLoggedIn)
			console.log('当前用户信息:', this.userInfo)

			// 检查是否需要登录
			if (!this.isLoggedIn && ['wallet', 'points', 'team', 'packages'].includes(moduleType)) {
				console.log('用户未登录，显示登录弹窗');
				this.showLoginModal();
				return;
			}

			const moduleNames = {
				wallet: '钱包',
				points: '点数',
				packages: '套餐',
				team: '团队',
				'customer-service': '客服',
				settings: '设置',
				promotion: '推广'
			}

			// 特殊处理钱包模块
			if (moduleType === 'wallet') {
				if (this.isLoggedIn) {
					// 确保用户信息已加载
					if (!this.userInfo.id) {
						await this.refreshUserInfo()
					}
					// 清除其他弹窗状态
					this.showPointsModal = false
					this.showPackageModal = false
					this.showSettingsModal = false

					this.showWalletModal = true
					this.errorMessage = '' // 清除错误信息
					await this.loadBalanceHistory()
				} else {
					this.showLoginModal();
				}
				return
			}

			// 特殊处理点数模块
			if (moduleType === 'points') {
				console.log('=== 点击点数模块开始 ===')
				console.log('isLoggedIn:', this.isLoggedIn)
				console.log('showPointsModal 当前值:', this.showPointsModal)

				if (!this.isLoggedIn) {
					console.log('未登录，显示登录弹窗')
					this.showLoginModal()
					return
				}

				console.log('已登录，显示点数弹窗')
				// 清除其他弹窗状态
				this.showWalletModal = false
				this.showPackageModal = false
				this.showSettingsModal = false

				// 先显示弹窗
				this.showPointsModal = true
				console.log('showPointsModal 设置后:', this.showPointsModal)

				// 强制触发视图更新
				this.$forceUpdate()
				console.log('强制更新视图完成')

				// 加载真实的点数记录数据
				await this.loadPointsHistory()
				console.log('点数记录加载完成，当前数据:', this.pointsHistory)

				console.log('=== 点击点数模块结束 ===')
				return
			}

			// 特殊处理套餐模块 - 显示套餐明细记录
			if (moduleType === 'packages') {
				console.log('=== 点击套餐模块开始 ===')
				console.log('isLoggedIn:', this.isLoggedIn)
				console.log('showPackageModal 当前值:', this.showPackageModal)

				if (this.isLoggedIn) {
					console.log('已登录，显示套餐弹窗')
					// 确保用户信息已加载
					if (!this.userInfo.id) {
						await this.refreshUserInfo()
					}
					// 清除其他弹窗状态
					this.showWalletModal = false
					this.showPointsModal = false
					this.showSettingsModal = false

					// 先显示弹窗
					this.showPackageModal = true
					console.log('showPackageModal 设置后:', this.showPackageModal)

					// 强制触发视图更新
					this.$forceUpdate()
					console.log('强制更新视图完成')

					// 加载真实的套餐记录数据
					await this.loadPackageHistory()
				} else {
					console.log('用户未登录，显示登录弹窗');
					this.showLoginModal();
				}
				return
			}

			// 特殊处理设置模块
			if (moduleType === 'settings') {
				if (this.isLoggedIn) {
					// 清除其他弹窗状态
					this.showWalletModal = false
					this.showPointsModal = false
					this.showPackageModal = false

					this.showSettingsModal = true
				} else {
					console.log('用户未登录，显示登录弹窗');
					this.showLoginModal();
				}
				return
			}

			// 特殊处理团队模块
			if (moduleType === 'team') {
				console.log('=== 点击团队模块开始 ===')
				if (this.isLoggedIn) {
					console.log('已登录，显示团队弹窗')
					// 确保用户信息已加载
					if (!this.userInfo.id) {
						await this.refreshUserInfo()
					}
					// 清除其他弹窗状态
					this.showWalletModal = false
					this.showPointsModal = false
					this.showPackageModal = false
					this.showSettingsModal = false

					// 显示团队弹窗
					this.showTeamModal = true
					console.log('showTeamModal 设置后:', this.showTeamModal)

					// 强制触发视图更新
					this.$forceUpdate()
					console.log('强制更新视图完成')

					// 加载团队数据
					await this.loadTeamData()
				} else {
					console.log('用户未登录，显示登录弹窗');
					this.showLoginModal();
				}
				return
			}

			// 特殊处理客服模块
			if (moduleType === 'customer-service') {
				this.showCustomerServiceModal = true
				return
			}

			uni.showToast({
				title: `${moduleNames[moduleType]}功能开发中`,
				icon: 'none'
			})
		},

		// 导航栏点击处理
		handleNavChange(event) {
			const { index, item } = event
			this.currentNavIndex = index

			// 如果点击的不是当前页面，进行跳转
			if (item.path && item.path !== '/pages/profile/index') {
				if (item.path === '/pages/index/index' ||
					item.path === '/pages/chat/index' ||
					item.path === '/pages/history/index') {
					// 使用 switchTab 跳转到 tabBar 页面
					uni.switchTab({
						url: item.path
					})
				} else {
					// 使用 navigateTo 跳转到普通页面
					uni.navigateTo({
						url: item.path
					})
				}
			}
		},

		// 钱包弹窗相关方法
		closeWalletModal() {
			console.log('关闭钱包弹窗')
			this.showWalletModal = false
			this.activeTab = 'balance' // 重置为余额明细标签
		},

		// 关闭点数弹窗
		closePointsModal() {
			console.log('关闭点数弹窗')
			this.showPointsModal = false
		},

		// 加载点数明细
		async loadPointsHistory() {
			if (!this.isLoggedIn) {
				console.log('用户未登录，跳过加载点数明细')
				this.pointsHistory = []
				return
			}

			this.pointsLoading = true
			console.log('开始加载点数明细...')

			try {
				// 调用新的点数明细API获取完整的点数记录
				console.log('开始调用点数明细API...')
				console.log('用户ID:', this.userInfo.id)
				console.log('API_BASE_URL:', this.API_BASE_URL)
				console.log('Token:', uni.getStorageSync('token'))

				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/members/${this.userInfo.id}/points-records`,
					method: 'GET',
					data: {
						page: 1,
						pageSize: 50 // 获取最近50条记录
					},
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`
					}
				})

				console.log('点数明细API完整响应:', response)
				console.log('响应状态码:', response.statusCode)
				console.log('响应数据:', response.data)

				if (response.statusCode === 200 && response.data && response.data.code === 200) {
					console.log('API调用成功，开始处理数据...')

					// 直接使用API返回的点数记录
					const apiData = response.data.data
					const records = apiData.records || []

					console.log('获取到点数记录数量:', records.length)
					console.log('汇总信息:', apiData.summary)

					// 转换为前端需要的格式
					this.pointsHistory = records.map(record => ({
						id: record.id,
						description: record.description,
						amount: record.amount,
						time: this.formatDateTime(new Date(record.time)),
						balance: record.balance,
						type: record.type,
						appTitle: record.metadata?.agentTitle || record.metadata?.packageTitle || '',
						relatedId: record.relatedId,
						relatedType: record.relatedType
					}))

					console.log('点数明细设置完成，最终数据:', this.pointsHistory)
				} else {
					// API调用失败或返回错误状态码
					console.log('API调用失败，状态码:', response.statusCode)
					console.log('错误响应:', response)

					// 显示示例数据以便演示
					this.pointsHistory = [
						{
							id: 'demo1',
							description: '注册赠送',
							amount: 100,
							time: this.formatDateTime(new Date(Date.now() - 8 * 24 * 60 * 60 * 1000)),
							balance: 1500,
							type: 'register_gift'
						},
						{
							id: 'demo2',
							description: '购买VIP套餐',
							amount: 2000,
							time: this.formatDateTime(new Date(Date.now() - 6 * 24 * 60 * 60 * 1000)),
							balance: 3500,
							type: 'package_purchase'
						},
						{
							id: 'demo3',
							description: '管理员调整',
							amount: 500,
							time: this.formatDateTime(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
							balance: 4000,
							type: 'manual_adjust'
						},
						{
							id: 'demo4',
							description: '使用AI助手',
							amount: -50,
							time: this.formatDateTime(new Date(Date.now() - 4 * 24 * 60 * 60 * 1000)),
							balance: 3950,
							type: 'app_usage'
						},
						{
							id: 'demo5',
							description: '管理员调整',
							amount: -200,
							time: this.formatDateTime(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
							balance: 3750,
							type: 'manual_adjust'
						},
						{
							id: 'demo6',
							description: '基础套餐到期扣除',
							amount: -850,
							time: this.formatDateTime(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
							balance: 2900,
							type: 'package_expire'
						}
					]
					console.log('API调用失败，显示示例数据')
				}
			} catch (error) {
				console.error('加载点数明细失败:', error)
				console.error('错误详情:', error.message || error)

				// 显示示例数据以便演示
				this.pointsHistory = [
					{
						id: 'demo1',
						description: '注册赠送',
						amount: 100,
						time: this.formatDateTime(new Date(Date.now() - 8 * 24 * 60 * 60 * 1000)),
						balance: 1500,
						type: 'register_gift'
					},
					{
						id: 'demo2',
						description: '购买VIP套餐',
						amount: 2000,
						time: this.formatDateTime(new Date(Date.now() - 6 * 24 * 60 * 60 * 1000)),
						balance: 3500,
						type: 'package_purchase'
					},
					{
						id: 'demo3',
						description: '管理员调整',
						amount: 500,
						time: this.formatDateTime(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
						balance: 4000,
						type: 'manual_adjust'
					},
					{
						id: 'demo4',
						description: '使用AI助手',
						amount: -50,
						time: this.formatDateTime(new Date(Date.now() - 4 * 24 * 60 * 60 * 1000)),
						balance: 3950,
						type: 'app_usage'
					},
					{
						id: 'demo5',
						description: '管理员调整',
						amount: -200,
						time: this.formatDateTime(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
						balance: 3750,
						type: 'manual_adjust'
					},
					{
						id: 'demo6',
						description: '基础套餐到期扣除',
						amount: -850,
						time: this.formatDateTime(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
						balance: 2900,
						type: 'package_expire'
					}
				]
				console.log('API调用异常，显示示例数据')
			} finally {
				this.pointsLoading = false
				console.log('点数明细加载完成，最终数据条数:', this.pointsHistory.length)
			}
		},

		// 关闭套餐弹窗
		closePackageModal() {
			this.showPackageModal = false
		},

		// 关闭团队弹窗
		closeTeamModal() {
			console.log('关闭团队弹窗')
			this.showTeamModal = false
		},

		// 显示团队详情弹窗
		showTeamDetails() {
			this.showTeamDetailModal = true
		},

		// 隐藏团队详情弹窗
		hideTeamDetails() {
			this.showTeamDetailModal = false
		},

		// 加载团队数据
		async loadTeamData() {
			if (!this.userInfo.id) {
				console.log('用户未登录，无法加载团队数据')
				return
			}

			this.teamLoading = true

			try {
				console.log('开始加载团队数据...')

				// 获取所有会员数据
				const response = await memberAPI.getAllMembers()
				console.log('获取会员数据响应:', response)

				if (response && response.data) {
					this.teamData.allMembers = Array.isArray(response.data) ? response.data : []
					console.log('会员数据:', this.teamData.allMembers)

					// 计算推广统计
					this.calculateReferrals()

					// 获取最近加入的成员
					this.getRecentMembers()
				}
			} catch (error) {
				console.error('加载团队数据失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				})
			} finally {
				this.teamLoading = false
			}
		},

		// 计算推广统计（与后台逻辑保持一致）
		calculateReferrals() {
			const promoterId = this.userInfo.id
			console.log('计算推广统计，推广员ID:', promoterId)

			// 一级推广用户
			this.teamData.firstLevelReferrals = this.teamData.allMembers.filter(member =>
				member.referrerId === promoterId || member.referrerId === String(promoterId)
			)

			// 二级推广用户
			const firstLevelIds = this.teamData.firstLevelReferrals.map(m => m.id)
			this.teamData.secondLevelReferrals = this.teamData.allMembers.filter(member => {
				return firstLevelIds.includes(member.referrerId) ||
					   firstLevelIds.includes(String(member.referrerId))
			})

			// 更新统计数据
			this.teamData.firstLevelCount = this.teamData.firstLevelReferrals.length
			this.teamData.secondLevelCount = this.teamData.secondLevelReferrals.length
			this.teamData.totalTeamCount = this.teamData.firstLevelCount + this.teamData.secondLevelCount

			console.log('推广统计结果:', {
				firstLevel: this.teamData.firstLevelCount,
				secondLevel: this.teamData.secondLevelCount,
				total: this.teamData.totalTeamCount
			})
		},

		// 获取最近加入的成员
		getRecentMembers() {
			const allTeamMembers = [
				...this.teamData.firstLevelReferrals.map(m => ({ ...m, level: 1 })),
				...this.teamData.secondLevelReferrals.map(m => ({ ...m, level: 2 }))
			]

			// 按创建时间排序，取最近的10个
			this.teamData.recentMembers = allTeamMembers
				.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
				.slice(0, 10)
		},

		// 格式化手机号
		formatPhone(phone) {
			if (!phone) return '未知'
			const phoneStr = String(phone)
			if (phoneStr.length === 11) {
				return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
			}
			return phoneStr
		},

		// 获取头像文字
		getAvatarText(member) {
			if (member.name) {
				return member.name.charAt(0).toUpperCase()
			}
			return 'U'
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return ''
			const date = new Date(timeStr)
			const now = new Date()
			const diff = now - date

			if (diff < 60000) { // 1分钟内
				return '刚刚'
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前'
			} else if (diff < 86400000) { // 1天内
				return Math.floor(diff / 3600000) + '小时前'
			} else {
				return Math.floor(diff / 86400000) + '天前'
			}
		},

		// 切换标签页
		switchTab(tab) {
			this.activeTab = tab
			this.errorMessage = '' // 清除错误信息
			if (tab === 'withdraw' && this.withdrawHistory.length === 0) {
				this.loadWithdrawHistory()
			}
		},

		// 计算手续费
		calculateFee() {
			if (!this.partnerConfig || !this.userInfo.balance) return 0
			return (this.userInfo.balance * this.partnerConfig.withdrawalFee / 100).toFixed(2)
		},

		// 计算实际到账金额
		calculateActualAmount() {
			if (!this.userInfo.balance) return 0
			const fee = this.calculateFee()
			return (this.userInfo.balance - fee).toFixed(2)
		},

		// 检查是否可以提交
		canSubmit() {
			// 微信提现无需输入信息
			if (this.partnerConfig && this.partnerConfig.withdrawalChannel === 'merchant') {
				return true
			}

			// 支付宝提现需要账号和姓名，且无验证错误（默认为支付宝提现）
			return this.withdrawForm.account.trim() &&
				   this.withdrawForm.name.trim() &&
				   !this.accountError &&
				   !this.nameError
		},

		// 取消提现
		cancelWithdraw() {
			this.activeTab = 'balance'
			this.withdrawForm = {
				account: '',
				name: ''
			}
			// 清除验证错误
			this.accountError = ''
			this.nameError = ''
			this.errorMessage = ''
		},

		// 验证支付宝账号
		validateAccount() {
			this.accountError = ''
			if (this.withdrawForm.account.trim()) {
				const accountPattern = /^(1[3-9]\d{9}|[\w\.-]+@[\w\.-]+\.\w+)$/
				if (!accountPattern.test(this.withdrawForm.account.trim())) {
					this.accountError = '请输入正确的手机号或邮箱格式'
				}
			}
		},

		// 验证真实姓名
		validateName() {
			this.nameError = ''
			if (this.withdrawForm.name.trim()) {
				const namePattern = /^[\u4e00-\u9fa5]{2,10}$/
				if (!namePattern.test(this.withdrawForm.name.trim())) {
					this.nameError = '请输入2-10位中文姓名'
				}
			}
		},

		// 在弹窗中提交提现
		async submitWithdrawInModal() {
			// 防止重复提交
			if (this.isSubmitting) {
				return
			}

			// 检查最低提现金额
			if (this.partnerConfig.minWithdrawal && this.userInfo.balance < this.partnerConfig.minWithdrawal) {
				this.errorMessage = `最低提现金额为¥${this.partnerConfig.minWithdrawal}`
				return
			}

			// 检查表单验证
			if (!this.canSubmit()) {
				// 默认为支付宝提现验证
				if (!this.partnerConfig || this.partnerConfig.withdrawalChannel !== 'merchant') {
					if (!this.withdrawForm.account) {
						this.errorMessage = '请输入支付宝账号'
						return
					}
					if (!this.withdrawForm.name) {
						this.errorMessage = '请输入真实姓名'
						return
					}
					// 验证支付宝账号格式
					const accountPattern = /^(1[3-9]\d{9}|[\w\.-]+@[\w\.-]+\.\w+)$/
					if (!accountPattern.test(this.withdrawForm.account)) {
						this.errorMessage = '请输入正确的支付宝账号（手机号或邮箱）'
						return
					}
				}
			}

			// 清除错误信息
			this.errorMessage = ''
			this.isSubmitting = true

			try {
				const withdrawType = (this.partnerConfig && this.partnerConfig.withdrawalChannel === 'merchant') ? 'wechat' : 'alipay'
				const accountInfo = {
					account: withdrawType === 'wechat' ? 'auto' : this.withdrawForm.account,
					name: withdrawType === 'wechat' ? (this.userInfo.nickname || this.userInfo.username || this.userInfo.phone || '微信用户') : this.withdrawForm.name
				}

				await this.submitWithdrawRequest(withdrawType, accountInfo, this.userInfo.balance)
			} catch (error) {
				console.error('提现提交失败:', error)
				this.errorMessage = '提现申请失败，请稍后重试'
			} finally {
				this.isSubmitting = false
			}
		},

		// 加载余额明细
		async loadBalanceHistory() {
			console.log('loadBalanceHistory - userInfo:', this.userInfo)
			console.log('loadBalanceHistory - userInfo.id:', this.userInfo.id)
			if (!this.userInfo.id) {
				console.log('用户ID不存在，无法加载余额明细')
				return
			}

			this.balanceLoading = true
			try {
				const token = uni.getStorageSync('token')
				const url = `${this.API_BASE_URL}/api/members/${this.userInfo.id}/balance-records`
				console.log('发送余额记录请求:', url)
				console.log('使用Token:', token)

				const response = await uni.request({
					url: url,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${token}`
					}
				})

				console.log('余额记录API响应:', response)

				if (response.data.code === 200) {
					const records = response.data.data.records || []
					this.balanceHistory = records.map(record => ({
						type: this.getRecordTypeText(record.type),
						time: this.formatDateTime(record.created_at),
						amount: record.amount,
						description: record.description
					}))
					console.log('余额明细加载成功:', this.balanceHistory)
				} else {
					console.error('获取余额明细失败:', response.data.message)
					// 显示示例数据以便演示
					this.balanceHistory = [
						{
							type: '团队购买套餐分成',
							time: this.formatDateTime(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
							amount: 25.50,
							description: '下级用户购买套餐分成'
						},
						{
							type: '团队购买套餐分成',
							time: this.formatDateTime(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
							amount: 12.80,
							description: '二级用户购买套餐分成'
						},
						{
							type: '余额提现',
							time: this.formatDateTime(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),
							amount: -100.00,
							description: '提现到支付宝'
						}
					]
					console.log('API调用失败，显示示例数据')
				}
			} catch (error) {
				console.error('加载余额明细失败:', error)
				// 显示示例数据以便演示
				this.balanceHistory = [
					{
						type: '团队购买套餐分成',
						time: this.formatDateTime(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
						amount: 25.50,
						description: '下级用户购买套餐分成'
					},
					{
						type: '团队购买套餐分成',
						time: this.formatDateTime(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
						amount: 12.80,
						description: '二级用户购买套餐分成'
					},
					{
						type: '余额提现',
						time: this.formatDateTime(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),
						amount: -100.00,
						description: '提现到支付宝'
					}
				]
				console.log('API调用异常，显示示例数据')
			} finally {
				this.balanceLoading = false
			}
		},

		// 加载提现记录
		async loadWithdrawHistory() {
			if (!this.userInfo.id) return

			this.withdrawLoading = true
			try {
				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/members/${this.userInfo.id}/withdraw-records`,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`
					}
				})

				if (response.data.code === 200) {
					const records = response.data.data.records || []
					this.withdrawHistory = records.map(record => ({
						withdrawType: this.getWithdrawTypeText(record.withdrawType),
						time: this.formatDateTime(record.createdAt),
						amount: record.amount,
						status: this.getWithdrawStatusText(record.status),
						statusClass: this.getWithdrawStatusClass(record.status),
						rejectReason: record.rejectReason || null
					}))
				} else {
					console.error('获取提现记录失败:', response.data.message)
					this.withdrawHistory = []
				}
			} catch (error) {
				console.error('加载提现记录失败:', error)
				this.withdrawHistory = []
			} finally {
				this.withdrawLoading = false
			}
		},



		// 加载套餐记录
		async loadPackageHistory() {
			console.log('loadPackageHistory - userInfo:', this.userInfo)
			if (!this.userInfo.id) {
				console.log('用户ID不存在，无法加载套餐记录')
				this.packageHistory = []
				return
			}

			this.packageLoading = true
			try {
				// 使用订单API获取用户的套餐购买记录
				const response = await orderAPI.getUserOrders(this.userInfo.id, {
					page: 1,
					pageSize: 20,
					status: 'Completed' // 只获取已完成的订单
				})

				console.log('套餐记录API响应:', response)

				if (response && response.data && response.data.orders) {
					this.packageHistory = response.data.orders.map(order => {
						console.log('处理订单数据:', order)

						// 计算套餐状态和剩余天数
						const startDate = new Date(order.completedTime || order.paymentTime)
						// 从套餐信息中获取有效期，注意字段名是 packageInfo 而不是 agentPackage
						const packageInfo = order.packageInfo || order.agentPackage
						const validityDays = packageInfo ? (packageInfo.duration || packageInfo.validityDays || 30) : 30
						const endDate = new Date(startDate.getTime() + (validityDays * 24 * 60 * 60 * 1000))

						// 按24小时精确计算剩余时间
						const now = new Date()
						const timeDiff = endDate.getTime() - now.getTime()

						// 计算剩余天数（按24小时计算，精确到小数）
						const remainingDays = Math.max(0, Math.ceil(timeDiff / (24 * 60 * 60 * 1000)))
						const status = remainingDays > 0 ? '使用中' : '已过期'

						// 如果套餐已过期，点数清零
						const totalPoints = remainingDays > 0 ?
							(packageInfo ? packageInfo.totalQuota || 1000 : 1000) : 0
						const dailyLimit = remainingDays > 0 ?
							(packageInfo ? packageInfo.dailyMaxConsumption || 50 : 50) : 0

						const result = {
							packageName: order.itemName,
							startTime: this.formatDateTime(startDate),
							endTime: this.formatDateTime(endDate),
							totalPoints: totalPoints,
							dailyLimit: dailyLimit,
							status: status,
							remainingDays: remainingDays,
							orderNo: order.orderNo,
							amount: order.amount
						}

						console.log('套餐计算详情:', {
							订单号: order.orderNo,
							开通时间: startDate.toLocaleString('zh-CN'),
							到期时间: endDate.toLocaleString('zh-CN'),
							当前时间: now.toLocaleString('zh-CN'),
							时间差毫秒: timeDiff,
							时间差小时: (timeDiff / (60 * 60 * 1000)).toFixed(2),
							剩余天数: remainingDays,
							套餐状态: status
						})
						console.log('处理后的套餐数据:', result)
						return result
					})
					console.log('套餐记录加载成功:', this.packageHistory)
				} else {
					// 如果没有订单数据，显示示例数据
					this.packageHistory = [
						{
							packageName: 'VIP套餐',
							startTime: this.formatDateTime(new Date()),
							endTime: this.formatDateTime(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
							totalPoints: 1000,
							dailyLimit: 50,
							status: '使用中',
							remainingDays: 30,
							orderNo: 'DEMO001',
							amount: 99.00
						},
						{
							packageName: '基础套餐',
							startTime: this.formatDateTime(new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)),
							endTime: this.formatDateTime(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
							totalPoints: 1000, // 显示原始套餐配额
							dailyLimit: 50,     // 显示原始每日限制
							status: '已过期',
							remainingDays: 0,
							orderNo: 'DEMO002',
							amount: 49.00
						},
						{
							packageName: '专业套餐',
							startTime: this.formatDateTime(new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)),
							endTime: this.formatDateTime(new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)),
							totalPoints: 2000,
							dailyLimit: 100,
							status: '使用中',
							remainingDays: 15,
							orderNo: 'DEMO003',
							amount: 199.00
						}
					]
					console.log('使用默认套餐记录数据')
				}
			} catch (error) {
				console.error('加载套餐记录失败:', error)
				// 出错时显示默认数据
				this.packageHistory = [
					{
						packageName: 'VIP套餐',
						startTime: this.formatDateTime(new Date()),
						endTime: this.formatDateTime(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
						totalPoints: 1000,
						dailyLimit: 50,
						status: '使用中',
						remainingDays: 30,
						orderNo: 'DEMO001',
						amount: 99.00
					},
					{
						packageName: '基础套餐',
						startTime: this.formatDateTime(new Date(Date.now() - 60 * 24 * 60 * 60 * 1000)),
						endTime: this.formatDateTime(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
						totalPoints: 500,
						dailyLimit: 20,
						status: '已过期',
						remainingDays: 0,
						orderNo: 'DEMO002',
						amount: 49.00
					}
				]
				console.log('使用默认套餐记录数据')
			} finally {
				this.packageLoading = false
			}
		},

		// 获取记录类型文本
		getRecordTypeText(type) {
			const typeMap = {
				'commission': '团队购买套餐分成',
				'withdraw': '余额提现',
				'withdraw_reject': '提现驳回退款',
				'withdraw_fee': '提现手续费',
				'manual_adjust': '管理员调整',
				'purchase': '购买套餐',
				'redeem': '兑换码'
			}
			return typeMap[type] || type
		},

		// 获取任务描述
		getTaskDescription(task) {
			const typeMap = {
				'chat': '智能对话',
				'image': '图片生成',
				'text': '文本处理',
				'code': '代码生成',
				'analysis': '数据分析',
				'translation': '翻译服务'
			}

			let description = typeMap[task.taskType] || task.taskTypeDetail || task.taskType || '智能服务'

			// 如果有任务来源，添加到描述中
			if (task.taskSource && task.taskSource !== task.taskType) {
				description += ` (${task.taskSource})`
			}

			return description
		},

		// 获取任务类型显示名称
		getTaskTypeDisplay(taskType) {
			const typeMap = {
				'chat': '智能对话',
				'image': '图片生成',
				'text': '文本处理',
				'code': '代码生成',
				'analysis': '数据分析',
				'translation': '翻译服务'
			}
			return typeMap[taskType] || '智能服务'
		},

		// 获取当前点数
		getCurrentPoints() {
			if (!this.userInfo) return 0

			// 优先使用points字段，如果没有则使用totalQuota - usedQuota
			if (this.userInfo.points !== undefined) {
				return this.userInfo.points || 0
			}

			// 备用方案：使用totalQuota - usedQuota
			const total = this.userInfo.totalQuota || 0
			const used = this.userInfo.usedQuota || 0
			return Math.max(0, total - used)
		},

		// 获取点数记录类型文本
		getPointsTypeText(type) {
			const typeMap = {
				'purchase': '购买',
				'consume': '消费扣除',
				'gift': '赠送获得',
				'refund': '退款返还',
				'manual_adjust': '管理员调整'
			}
			return typeMap[type] || type
		},

		// 获取点数记录类型图标
		getPointsTypeIcon(type) {
			console.log('获取图标，类型:', type)
			const iconMap = {
				'register_gift': '🎁',      // 注册赠送
				'invite_gift': '👥',        // 邀请赠送
				'package_purchase': '📦',   // 购买套餐
				'package_redeem': '🎫',     // 兑换套餐
				'package_expire': '⏰',     // 套餐到期扣除
				'app_usage': '🤖',          // 使用应用
				'purchase': '💰',           // 购买
				'consume': '💸',            // 消费
				'gift': '🎁',               // 赠送
				'refund': '↩️',             // 退款
				'manual_adjust': '⚙️'       // 管理员调整
			}
			const icon = iconMap[type] || '📝'
			console.log('返回图标:', icon)
			return icon
		},

		// 获取提现方式文本
		getWithdrawTypeText(type) {
			const typeMap = {
				'alipay': '支付宝',
				'bank_card': '银行卡',
				'wechat': '微信'
			}
			return typeMap[type] || type
		},

		// 获取提现状态文本
		getWithdrawStatusText(status) {
			const statusMap = {
				'pending': '待审核',
				'processing': '处理中',
				'completed': '已完成',
				'rejected': '已驳回'
			}
			return statusMap[status] || status
		},

		// 获取提现状态样式类
		getWithdrawStatusClass(status) {
			const classMap = {
				'pending': 'status-pending',
				'processing': 'status-processing',
				'completed': 'status-completed',
				'rejected': 'status-rejected'
			}
			return classMap[status] || 'status-default'
		},

		// 获取点数描述
		getPointsDescription(type, originalDescription) {
			const typeMap = {
				'register_gift': '注册赠送',
				'invite_gift': '邀请赠送',
				'package_purchase': '购买套餐',
				'package_redeem': '兑换套餐',
				'package_expire': '套餐到期扣除',
				'app_usage': originalDescription || '应用使用',
				'manual_adjust': '管理员调整'
				// 注意：点数不可提现，推广佣金是现金收入，都不在点数明细中
			}
			return typeMap[type] || originalDescription || '点数变动'
		},

		// 格式化日期时间
		formatDateTime(dateStr) {
			if (!dateStr) return ''
			const date = new Date(dateStr)
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hours = String(date.getHours()).padStart(2, '0')
			const minutes = String(date.getMinutes()).padStart(2, '0')
			return `${year}-${month}-${day} ${hours}:${minutes}`
		},

		// 加载合伙人配置
		async loadPartnerConfig() {
			console.log('开始加载合伙人配置...')
			// 如果没有token，使用默认配置
			const token = uni.getStorageSync('token')
			console.log('当前token:', token ? '存在' : '不存在')
			if (!token) {
				console.log('未登录，使用默认合伙人配置')
				this.partnerConfig = {
					minWithdrawal: 10,
					withdrawalChannel: 'alipay',
					withdrawalFee: 0
				}
				console.log('设置默认配置:', this.partnerConfig)
				return
			}

			try {
				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/config/type/referral`,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token') || ''}`,
						'Content-Type': 'application/json'
					}
				})

				console.log('合伙人配置API响应:', response.data)

				// 处理不同的响应格式
				let configArray = []
				if (response.data.code === 200 && response.data.data) {
					configArray = response.data.data
				} else if (response.data.success && response.data.data) {
					configArray = response.data.data
				} else if (response.data && Array.isArray(response.data)) {
					configArray = response.data
				}

				if (configArray.length > 0) {
					// 转换配置数据格式
					const configData = {}
					configArray.forEach(item => {
						configData[item.key] = item.value
					})

					this.partnerConfig = {
						minWithdrawal: parseInt(configData.min_withdrawal || '10'),
						withdrawalChannel: configData.withdrawal_channel || 'alipay',
						withdrawalFee: parseFloat(configData.withdrawal_fee || '0')
					}

					// 更新合伙人升级条件和权益配置
					this.partnerUpgradeCondition = parseInt(configData.invitee_reward || '30')
					this.promoterCashback = parseInt(configData.promoter_cashback || '5')
					this.promoterCommission = parseInt(configData.promoter_commission || '2')
					this.minWithdrawal = parseInt(configData.min_withdrawal || '10')
					console.log('合伙人配置加载成功:', this.partnerConfig)
				} else {
					console.warn('合伙人配置加载失败，使用默认配置')
					this.partnerConfig = {
						minWithdrawal: 10,
						withdrawalChannel: 'alipay',
						withdrawalFee: 0
					}
				}
			} catch (error) {
				console.error('加载合伙人配置失败:', error)
				console.error('错误详情:', error.errMsg || error.message || error)
				// 使用默认配置
				this.partnerConfig = {
					minWithdrawal: 10,
					withdrawalChannel: 'alipay',
					withdrawalFee: 0
				}
			}
		},

		// 余额提现
		async handleWithdraw() {
			console.log('handleWithdraw 被调用')
			console.log('userInfo.balance:', this.userInfo.balance)
			console.log('partnerConfig:', this.partnerConfig)

			// 检查余额
			if (!this.userInfo.balance || this.userInfo.balance <= 0) {
				// 在弹窗内显示错误信息
				this.errorMessage = '余额不足，无法提现'
				console.log('设置错误信息:', this.errorMessage)
				return
			}

			// 如果合伙人配置未加载，尝试重新加载
			if (!this.partnerConfig) {
				console.log('合伙人配置未加载，尝试重新加载')
				await this.loadPartnerConfig()
				console.log('重新加载后的partnerConfig:', this.partnerConfig)
			}

			// 检查合伙人配置
			if (!this.partnerConfig || !this.partnerConfig.withdrawalChannel) {
				this.errorMessage = '提现配置未加载，请稍后重试'
				console.log('设置错误信息:', this.errorMessage)
				return
			}

			// 检查最低提现金额
			if (this.partnerConfig.minWithdrawal && this.userInfo.balance < this.partnerConfig.minWithdrawal) {
				this.errorMessage = `最低提现金额为¥${this.partnerConfig.minWithdrawal}`
				console.log('设置错误信息:', this.errorMessage)
				return
			}

			// 清除错误信息
			this.errorMessage = ''
			// 切换到提现表单标签页
			this.activeTab = 'withdrawForm'
			console.log('切换到提现表单，activeTab:', this.activeTab)
			// 清空表单数据
			this.withdrawForm = {
				account: '',
				name: ''
			}
		},

		// 微信提现处理（无需输入账号，直接全额提现）
		async processWechatWithdraw() {
			const fullAmount = this.userInfo.balance

			// 检查最低提现金额
			if (this.partnerConfig.minWithdrawal && fullAmount < this.partnerConfig.minWithdrawal) {
				this.errorMessage = `最低提现金额为¥${this.partnerConfig.minWithdrawal}`
				return
			}

			// 清除错误信息
			this.errorMessage = ''

			const fee = this.partnerConfig ? (fullAmount * this.partnerConfig.withdrawalFee / 100) : 0
			const actualAmount = fullAmount - fee

			const confirmText = `确认提现到微信零钱？\n提现金额：¥${fullAmount}\n手续费：¥${fee.toFixed(2)}\n实际到账：¥${actualAmount.toFixed(2)}`

			uni.showModal({
				title: '微信提现确认',
				content: confirmText,
				success: (res) => {
					if (res.confirm) {
						this.submitWithdrawRequest('wechat', {
							account: 'auto', // 微信自动到账，无需账号
							name: this.userInfo.nickname || this.userInfo.username || this.userInfo.phone || '微信用户'
						}, fullAmount)
					}
				}
			})
		},



		// 显示提现表单（支付宝全额提现）
		showWithdrawForm(withdrawType) {
			const typeNames = {
				'alipay': '支付宝',
				'bank_card': '银行卡',
				'wechat': '微信'
			}

			// 全额提现
			const fullAmount = this.userInfo.balance

			// 检查最低提现金额
			if (this.partnerConfig.minWithdrawal && fullAmount < this.partnerConfig.minWithdrawal) {
				this.errorMessage = `最低提现金额为¥${this.partnerConfig.minWithdrawal}`
				return
			}

			// 清除错误信息
			this.errorMessage = ''

			const feeRate = this.partnerConfig ? this.partnerConfig.withdrawalFee : 0
			const fee = fullAmount * feeRate / 100
			const actualAmount = fullAmount - fee

			// 构建确认内容
			let content = `将提现全部余额到${typeNames[withdrawType]}\n`
			content += `提现金额：¥${fullAmount}\n`
			if (feeRate > 0) {
				content += `手续费：¥${fee.toFixed(2)} (${feeRate}%)\n`
				content += `实际到账：¥${actualAmount.toFixed(2)}`
			} else {
				content += `实际到账：¥${actualAmount.toFixed(2)}`
			}

			// 确认全额提现
			uni.showModal({
				title: `${typeNames[withdrawType]}提现确认`,
				content: content,
				success: (res) => {
					if (res.confirm) {
						this.showAccountForm(withdrawType, fullAmount)
					}
				}
			})
		},

		// 显示账户信息表单（支付宝）
		showAccountForm(withdrawType, amount) {
			// 只处理支付宝提现，收集账号
			uni.showModal({
				title: '支付宝账号',
				content: '请输入支付宝账号（手机号或邮箱）',
				editable: true,
				placeholderText: '支付宝账号',
				success: (res) => {
					if (res.confirm && res.content) {
						// 收集真实姓名
						uni.showModal({
							title: '真实姓名',
							content: '请输入支付宝账号对应的真实姓名',
							editable: true,
							placeholderText: '真实姓名',
							success: (nameRes) => {
								if (nameRes.confirm && nameRes.content) {
									this.submitWithdrawRequest(withdrawType, {
										account: res.content,
										name: nameRes.content
									}, amount)
								}
							}
						})
					}
				}
			})
		},

		// 提交提现申请
		async submitWithdrawRequest(withdrawType, accountInfo, amount) {
			try {
				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/members/${this.userInfo.id}/withdraw`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`,
						'Content-Type': 'application/json'
					},
					data: {
						amount,
						withdrawType,
						accountInfo
					}
				})

				if (response.data.code === 200) {
					// 显示成功提示
					uni.showToast({
						title: '提现申请提交成功',
						icon: 'success',
						duration: 2000
					})

					// 清空表单
					this.withdrawForm = {
						account: '',
						name: ''
					}

					// 刷新用户信息和记录
					await this.refreshUserInfo()
					this.loadWithdrawHistory()
					this.loadBalanceHistory()

					// 切换到提现记录标签
					this.activeTab = 'withdraw'

					return true
				} else {
					// 设置错误信息到弹窗中显示
					this.errorMessage = response.data.message || '提现申请失败，请稍后重试'
					return false
				}
			} catch (error) {
				console.error('提现申请失败:', error)
				this.errorMessage = '网络错误，请检查网络连接后重试'
				return false
			}
		},

		// 设置弹窗相关方法
		closeSettingsModal() {
			this.showSettingsModal = false
		},

		handlePersonalSettings() {
			this.closeSettingsModal()
			this.showPersonalSettingsModal = true
		},

		handleAccountSecurity() {
			this.closeSettingsModal()
			this.showAccountSecurityModal = true
		},

		// 个人设置弹窗相关方法
		closePersonalSettingsModal() {
			this.showPersonalSettingsModal = false
		},

		handleSettingsUpdated(updateData) {
			// 更新本地用户信息
			Object.assign(this.userInfo, updateData)
			// 刷新用户信息
			this.refreshUserInfo()
		},

		handleAvatarUpdated(avatarUrl) {
			// 更新头像
			this.userInfo.avatar = avatarUrl
			// 刷新用户信息
			this.refreshUserInfo()
		},

		// 账户安全弹窗相关方法
		closeAccountSecurityModal() {
			this.showAccountSecurityModal = false
		},

		handleSecurityUpdated(updateData) {
			// 更新本地用户信息
			Object.assign(this.userInfo, updateData)
			// 刷新用户信息
			this.refreshUserInfo()
		},

		// 客服弹窗相关方法
		closeCustomerServiceModal() {
			this.showCustomerServiceModal = false
		},

		// 合伙人权益弹窗相关方法
		showPartnerModal() {
			this.showPartnerBenefitsModal = true
		},

		closePartnerBenefitsModal() {
			this.showPartnerBenefitsModal = false
		},

		// VIP权益弹窗相关方法
		showVipModal() {
			this.showVipBenefitsModal = true
		},

		closeVipBenefitsModal() {
			this.showVipBenefitsModal = false
		},

		// 获取用户邀请人数
		async loadUserInviteCount() {
			if (!this.userInfo.id) {
				console.log('用户未登录，无法获取邀请人数')
				return
			}

			try {
				// 获取所有会员数据
				const response = await memberAPI.getAllMembers()
				if (response && response.data) {
					const allMembers = Array.isArray(response.data) ? response.data : []

					// 计算直推人数（referrerId等于当前用户ID的成员）
					const directReferrals = allMembers.filter(member =>
						member.referrerId && Number(member.referrerId) === Number(this.userInfo.id)
					)

					this.userInviteCount = directReferrals.length
					console.log('用户邀请人数:', this.userInviteCount)
				}
			} catch (error) {
				console.error('获取邀请人数失败:', error)
				this.userInviteCount = 0
			}
		},

		handleLogout() {
			this.closeSettingsModal()
			this.logout()
		},

		// 从数据库获取用户的二维码信息
		async getUserQRCodeFromDB() {
			try {
				console.log('📡 从数据库获取用户二维码信息...')
				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/members/info`,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${userStore.getToken()}`
					}
				})

				if (response.statusCode === 200 && response.data.success) {
					const userInfo = response.data.data
					console.log('📋 用户信息:', userInfo)

					if (userInfo.qrCodePath && userInfo.inviteCode) {
						return {
							qrCodePath: userInfo.qrCodePath,
							inviteCode: userInfo.inviteCode,
							inviteUrl: userInfo.inviteUrl
						}
					}
				}
				return null
			} catch (error) {
				console.error('❌ 获取用户二维码信息失败:', error)
				return null
			}
		},

		// 初始化海报生成
		async initPosterGeneration() {
			try {
				console.log('🎯 开始初始化海报生成流程')
				this.posterGenerating = true

				// 首先检查数据库中是否已有二维码
				const userId = this.userInfo.id
				if (userId) {
					console.log('📋 检查数据库中的二维码信息...')
					const userQRCode = await this.getUserQRCodeFromDB()
					if (userQRCode && userQRCode.qrCodePath) {
						console.log('✅ 使用数据库中的二维码:', userQRCode.qrCodePath)
						this.inviteCode = userQRCode.inviteCode
						this.inviteUrl = userQRCode.inviteUrl
						this.qrCodeUrl = this.getFullImageUrl(userQRCode.qrCodePath)
						console.log('🎉 海报生成流程完成（使用数据库二维码）！')
						return
					}
				}

				// 检查本地缓存
				if (userId && this.checkQRCodeCache(userId)) {
					console.log('📋 使用本地缓存的二维码信息')
					const cached = this.qrCodeCache[userId]
					this.inviteCode = cached.inviteCode
					this.inviteUrl = cached.inviteUrl
					this.qrCodeUrl = cached.qrCodeUrl
					console.log('🎉 海报生成流程完成（使用本地缓存）！')
					return
				}

				// 获取邀请码和链接
				await this.getInviteInfo()
				console.log('📋 邀请信息获取完成:', { inviteCode: this.inviteCode, inviteUrl: this.inviteUrl })

				// 生成二维码
				const qrCodePath = await this.generateQRCodeFromServer()
				// 如果是完整的URL（降级方案），直接使用；否则拼接完整URL
				if (qrCodePath.startsWith('http')) {
					this.qrCodeUrl = qrCodePath
				} else {
					this.qrCodeUrl = this.getFullImageUrl(qrCodePath)
				}
				console.log('🔗 二维码生成完成:', this.qrCodeUrl)

				// 缓存二维码信息
				if (userId) {
					this.cacheQRCodeInfo(userId, this.qrCodeUrl, this.inviteCode, this.inviteUrl)
				}

				console.log('🎉 海报生成流程全部完成！')

			} catch (error) {
				console.error('❌ 初始化海报失败:', error)

				// 即使出错也尝试生成一个基本的二维码
				try {
					if (!this.qrCodeUrl && this.inviteUrl) {
						console.log('🔄 尝试生成降级二维码...')
						this.qrCodeUrl = await this.generateQRCodeFallback()
						console.log('✅ 降级二维码生成成功:', this.qrCodeUrl)
					}
				} catch (fallbackError) {
					console.error('❌ 降级二维码也失败:', fallbackError)
				}

				uni.showToast({
					title: '生成海报失败: ' + error.message,
					icon: 'none'
				})
			} finally {
				this.posterGenerating = false
			}
		},

		// 加载后台海报配置
		async loadPosterConfig() {
			try {
				console.log('📋 请求后台海报配置...')
				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/config/referral`,
					method: 'GET'
				})

				console.log('📋 后台配置响应状态:', response.statusCode)
				console.log('📋 后台配置响应数据:', response.data)

				if (response.statusCode === 200 && response.data && response.data.success) {
					const config = response.data.data
					console.log('📋 获取到的配置:', config)

					// 直接设置海报背景图片
					if (config.poster_background_image) {
						this.posterConfig.posterbackgroundimage = config.poster_background_image
						console.log('🖼️ 设置背景图片:', config.poster_background_image)
						console.log('🖼️ 完整图片URL:', this.getFullImageUrl(config.poster_background_image))
					} else {
						console.log('⚠️ 后台配置中没有poster_background_image字段')
						console.log('📋 完整配置:', config)
						// 使用默认背景图片
						this.posterConfig.posterbackgroundimage = '/images/banners/banner1.jpg'
					}
					if (config.poster_qr_code_size) {
						this.posterConfig.qrCodeSize = parseInt(config.poster_qr_code_size)
						console.log('📏 设置二维码尺寸:', this.posterConfig.qrCodeSize)
					}
					if (config.poster_qr_code_position_x !== undefined && config.poster_qr_code_position_y !== undefined) {
						this.posterConfig.qrCodePosition.x = parseInt(config.poster_qr_code_position_x)
						this.posterConfig.qrCodePosition.y = parseInt(config.poster_qr_code_position_y)
						console.log('📍 设置二维码位置:', this.posterConfig.qrCodePosition)
					}

					// 更新分享配置
					if (config.share_title) {
						this.shareConfig.title = config.share_title
						console.log('📝 设置分享标题:', config.share_title)
					}
					if (config.share_description) {
						this.shareConfig.description = config.share_description
						console.log('📄 设置分享描述:', config.share_description)
					}

					console.log('✅ 海报配置加载完成:', {
						posterConfig: this.posterConfig,
						shareConfig: this.shareConfig
					})
				} else {
					console.warn('⚠️ 后台配置响应异常:', response.data)
				}
			} catch (error) {
				console.error('❌ 加载海报配置失败:', error)
				// 使用默认配置
			}
		},

		// 生成邀请码
		async generateInviteCode() {
			try {
				const token = userStore.getToken()
				const response = await uni.request({
					url: `${API_BASE_URL}/api/config/generate-invite-code`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${token}`,
						'Content-Type': 'application/json'
					}
				})

				if (response.statusCode === 200 && response.data.success) {
					this.inviteCode = response.data.data.inviteCode
					this.inviteUrl = response.data.data.inviteUrl
				} else {
					throw new Error(response.data.message || '生成邀请码失败')
				}
			} catch (error) {
				console.error('生成邀请码失败:', error)
				throw error
			}
		},

		// 获取邀请信息
		async getInviteInfo() {
			try {
				// 首先尝试调用后端API生成邀请码
				const token = userStore.getToken()
				if (token) {
					const response = await uni.request({
						url: `${this.API_BASE_URL}/api/config/generate-invite-code`,
						method: 'POST',
						header: {
							'Authorization': `Bearer ${token}`,
							'Content-Type': 'application/json'
						}
					})

					if (response.statusCode === 200 && response.data.success) {
						this.inviteCode = response.data.data.inviteCode
						this.inviteUrl = response.data.data.inviteUrl
						console.log('✅ 从后端获取邀请信息成功:', { inviteCode: this.inviteCode, inviteUrl: this.inviteUrl })
						return
					}
				}
			} catch (error) {
				console.warn('⚠️ 后端获取邀请信息失败，使用本地生成:', error)
			}

			// 降级方案：本地生成邀请码和链接
			const userId = this.userInfo.id || 'user123'
			this.inviteCode = `INV${userId}${Date.now().toString().slice(-6)}`

			// 生成正确的邀请链接
			const baseUrl = window.location.origin
			this.inviteUrl = `${baseUrl}/#/pages/auth/register?inviteCode=${this.inviteCode}&inviter=${userId}`
			console.log('📋 本地生成邀请信息:', { inviteCode: this.inviteCode, inviteUrl: this.inviteUrl })
		},

		// 调用后端生成二维码图片文件
		async generateQRCodeFromServer() {
			try {
				console.log('📡 调用后端API生成二维码...')

				const response = await uni.request({
					url: `${this.API_BASE_URL}/api/config/generate-qrcode`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${userStore.getToken()}`,
						'Content-Type': 'application/json'
					},
					data: {
						inviteCode: this.inviteCode,
						size: this.posterConfig.qrCodeSize || 200
					}
				})

				console.log('📡 后端响应:', response)

				if (response.statusCode === 200 && response.data.success) {
					const qrCodePath = response.data.data.qrCodePath
					console.log('✅ 二维码生成成功，路径:', qrCodePath)
					return qrCodePath
				} else {
					throw new Error(response.data.message || '生成二维码失败')
				}
			} catch (error) {
				console.error('❌ 调用后端生成二维码失败:', error)
				// 降级方案：使用在线API
				return this.generateQRCodeFallback()
			}
		},

		// 降级方案：使用Canvas生成二维码
		async generateQRCodeFallback() {
			console.log('🔄 使用Canvas降级方案生成二维码')
			try {
				const inviteUrl = `${window.location.origin}/#/pages/auth/register?inviteCode=${this.inviteCode}`
				const size = this.posterConfig.qrCodeSize || 200

				// 使用Canvas生成二维码，避免跨域问题
				const canvas = document.createElement('canvas')
				canvas.width = size
				canvas.height = size
				const ctx = canvas.getContext('2d')

				// 简单的二维码替代：绘制一个带文字的方块
				ctx.fillStyle = '#ffffff'
				ctx.fillRect(0, 0, size, size)

				ctx.fillStyle = '#000000'
				ctx.strokeStyle = '#000000'
				ctx.lineWidth = 2
				ctx.strokeRect(10, 10, size - 20, size - 20)

				// 绘制提示文字
				ctx.font = '14px Arial'
				ctx.textAlign = 'center'
				ctx.fillText('扫码邀请', size / 2, size / 2)

				return canvas.toDataURL('image/png')
			} catch (error) {
				console.error('❌ Canvas降级方案失败:', error)
				// 最后的降级方案：返回一个简单的data URL
				return 'data:image/svg+xml;base64,' + btoa(`
					<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
						<rect width="200" height="200" fill="white" stroke="black" stroke-width="2"/>
						<text x="100" y="105" text-anchor="middle" font-family="Arial" font-size="16">扫码邀请</text>
					</svg>
				`)
			}
		},

		// 背景图片加载成功
		onBackgroundImageLoad(e) {
			console.log('✅ 背景图片加载成功:', e)
			console.log('🖼️ 背景图片配置:', this.posterConfig)
			console.log('🖼️ 图片尺寸:', e.detail)
			this.backgroundImageError = false
			uni.showToast({
				title: '图片加载成功',
				icon: 'success',
				duration: 1000
			});
		},

		// 背景图片加载失败
		onBackgroundImageError(e) {
			const fullUrl = this.getFullImageUrl(this.posterConfig.backgroundImage || this.posterConfig.posterbackgroundimage)
			console.log('❌ 背景图片加载失败，将显示默认背景:', e)
			console.log('🔍 尝试加载的图片URL:', fullUrl)
			console.log('🔍 posterConfig.backgroundImage:', this.posterConfig.backgroundImage)
			console.log('🔍 posterConfig.posterbackgroundimage:', this.posterConfig.posterbackgroundimage)
			console.log('🔍 完整posterConfig:', this.posterConfig)
			uni.showToast({
				title: '图片加载失败',
				icon: 'error',
				duration: 1000
			});
			// 背景图片加载失败时，默认背景会自动显示（因为背景图片不会覆盖它）
		},

		// 二维码图片加载错误处理
		onQRCodeImageError(e) {
			console.error('❌ 二维码图片加载失败:', e)
			console.log('🔍 二维码URL:', this.qrCodeUrl)
			uni.showToast({
				title: '二维码加载失败',
				icon: 'none'
			})
		},

		// 二维码图片加载成功处理
		onQRCodeImageLoad(e) {
			console.log('✅ 二维码图片加载成功:', e)
			console.log('🔍 二维码URL:', this.qrCodeUrl)
		},

		// 生成海报
		async generatePoster() {
			console.log('🚀 开始生成海报...')
			this.posterGenerating = true

			try {
				// 先加载后台海报配置
				console.log('📋 加载后台海报配置...')
				await this.loadPosterConfig()
				console.log('✅ 后台配置加载完成')

				// 生成邀请码
				console.log('🎫 生成邀请码...')
				await this.generateInviteCode()
				console.log('✅ 邀请码生成完成:', this.inviteCode)

				// #ifdef H5
				// H5环境使用原生Canvas
				await this.generatePosterH5()
				// #endif

				// #ifndef H5
				// 非H5环境使用uni-app Canvas
				await this.generatePosterUniApp()
				// #endif

				// 海报已经在模板中显示，不需要额外设置
				console.log('🎉 海报生成完成！')

			} catch (error) {
				console.error('❌ 生成海报失败:', error)
				uni.showToast({
					title: '生成海报失败: ' + error.message,
					icon: 'none'
				})
			} finally {
				this.posterGenerating = false
			}
		},

		// H5环境海报生成
		async generatePosterH5() {
			console.log('🌐 H5环境：生成Canvas海报')

			// 调用后端API生成二维码图片文件
			console.log('🔲 调用后端生成二维码图片...')
			const qrCodePath = await this.generateQRCodeFromServer()

			// 使用完整URL显示二维码
			this.qrCodeUrl = this.getFullImageUrl(qrCodePath)

			// 绘制Canvas海报
			console.log('🎨 开始绘制Canvas海报...')
			await this.drawCanvasH5()

			console.log('✅ H5海报生成完成')
			console.log('🔲 二维码图片路径:', qrCodePath)
			console.log('🔲 二维码完整URL:', this.qrCodeUrl)
			console.log('🖼️ 背景图片路径:', this.posterConfig.backgroundImage)
			console.log('🖼️ 完整背景图片URL:', this.getFullImageUrl(this.posterConfig.backgroundImage))
		},

		// H5环境绘制Canvas
		async drawCanvasH5() {
			try {
				console.log('🎨 开始绘制Canvas...')

				// 等待DOM更新
				await this.$nextTick()

				const canvas = this.$refs.posterCanvas
				console.log('🔍 获取Canvas元素:', canvas)

				if (!canvas) {
					console.error('❌ Canvas元素未找到，$refs:', this.$refs)
					throw new Error('Canvas元素未找到')
				}

				const ctx = canvas.getContext('2d')
				console.log('🔍 Canvas上下文:', ctx)
				console.log('🔍 Canvas尺寸:', canvas.width, 'x', canvas.height)

				// 清空Canvas
				ctx.clearRect(0, 0, canvas.width, canvas.height)
				console.log('🧹 Canvas已清空')

				// 绘制背景
				console.log('🎨 开始绘制背景...')
				await this.drawBackgroundH5(ctx, canvas)

				// 绘制二维码
				console.log('🔲 开始绘制二维码...')
				await this.drawQRCodeH5(ctx, canvas)

				// 绘制文字信息
				console.log('📝 开始绘制文字...')
				this.drawTextH5(ctx, canvas)

				console.log('✅ Canvas绘制完成')
			} catch (error) {
				console.error('❌ Canvas绘制失败:', error)
				console.error('❌ 错误详情:', error.message)
				throw error
			}
		},

		// H5环境绘制背景
		async drawBackgroundH5(ctx, canvas) {
			try {
				if (this.posterConfig.backgroundImage) {
					// 有背景图片，加载并绘制
					const img = new Image()
					// 移除crossOrigin设置，避免跨域问题

					await new Promise((resolve, reject) => {
						img.onload = () => {
							try {
								ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
								console.log('✅ 背景图片绘制成功')
								resolve()
							} catch (drawError) {
								console.warn('⚠️ 背景图片绘制失败:', drawError)
								reject(drawError)
							}
						}
						img.onerror = (error) => {
							console.warn('⚠️ 背景图片加载失败:', error)
							reject(error)
						}

						// 设置超时
						setTimeout(() => {
							reject(new Error('背景图片加载超时'))
						}, 10000)

						img.src = this.getFullImageUrl(this.posterConfig.backgroundImage)
					})
				} else {
					// 没有背景图片，使用渐变背景
					const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
					gradient.addColorStop(0, '#667eea')
					gradient.addColorStop(1, '#764ba2')

					ctx.fillStyle = gradient
					ctx.fillRect(0, 0, canvas.width, canvas.height)
					console.log('✅ 渐变背景绘制成功')
				}
			} catch (error) {
				console.warn('⚠️ 背景绘制失败，使用默认背景:', error)
				// 使用纯色背景作为后备
				ctx.fillStyle = '#667eea'
				ctx.fillRect(0, 0, canvas.width, canvas.height)
				console.log('✅ 默认背景绘制成功')
			}
		},

		// H5环境绘制二维码
		async drawQRCodeH5(ctx, canvas) {
			try {
				if (!this.qrCodeUrl) {
					console.warn('⚠️ 二维码URL为空，跳过绘制')
					return
				}

				// 计算二维码位置和大小
				const qrSize = this.posterConfig.qrCodeSize || 120
				const position = this.posterConfig.qrCodePosition || { x: 225, y: 517 }

				// 绘制白色背景
				ctx.fillStyle = '#ffffff'
				ctx.fillRect(position.x - 5, position.y - 5, qrSize + 10, qrSize + 10)

				// 如果是data URL，直接绘制
				if (this.qrCodeUrl.startsWith('data:')) {
					const img = new Image()
					await new Promise((resolve, reject) => {
						img.onload = () => {
							try {
								ctx.drawImage(img, position.x, position.y, qrSize, qrSize)
								console.log('✅ 二维码绘制成功（data URL）')
								resolve()
							} catch (drawError) {
								console.warn('⚠️ 二维码绘制失败:', drawError)
								reject(drawError)
							}
						}
						img.onerror = (error) => {
							console.warn('⚠️ 二维码图片加载失败:', error)
							reject(error)
						}
						img.src = this.qrCodeUrl
					})
				} else {
					// 对于外部URL，尝试加载，如果失败则绘制文字替代
					try {
						const img = new Image()
						// 移除crossOrigin设置

						await Promise.race([
							new Promise((resolve, reject) => {
								img.onload = () => {
									try {
										ctx.drawImage(img, position.x, position.y, qrSize, qrSize)
										console.log('✅ 二维码绘制成功（外部URL）')
										resolve()
									} catch (drawError) {
										reject(drawError)
									}
								}
								img.onerror = reject
								img.src = this.qrCodeUrl
							}),
							new Promise((_, reject) =>
								setTimeout(() => reject(new Error('二维码加载超时')), 5000)
							)
						])
					} catch (error) {
						console.warn('⚠️ 二维码加载失败，使用文字替代:', error)
						// 绘制文字替代
						ctx.fillStyle = '#000000'
						ctx.font = '14px Arial'
						ctx.textAlign = 'center'
						ctx.fillText('扫码邀请', position.x + qrSize/2, position.y + qrSize/2)
						console.log('✅ 二维码文字替代绘制成功')
					}
				}
			} catch (error) {
				console.warn('⚠️ 二维码绘制失败:', error)
			}
		},

		// H5环境绘制文字
		drawTextH5(ctx, canvas) {
			try {
				// 绘制标题
				ctx.fillStyle = '#ffffff'
				ctx.font = 'bold 24px Arial'
				ctx.textAlign = 'center'
				ctx.fillText(this.shareConfig.title, canvas.width / 2, 80)

				// 绘制描述
				ctx.font = '16px Arial'
				ctx.fillText(this.shareConfig.description, canvas.width / 2, 120)

				// 邀请码已隐藏，二维码包含完整邀请信息

				console.log('✅ 文字绘制成功')
			} catch (error) {
				console.warn('⚠️ 文字绘制失败:', error)
			}
		},

		// 非H5环境海报生成
		async generatePosterUniApp() {
			console.log('📱 非H5环境：使用uni-app Canvas生成海报')
			throw new Error('非H5环境暂不支持')
		},

		// 绘制海报内容
		async drawPosterContent(ctx) {
			try {
				console.log('🎨 开始绘制海报内容...')

				// 先绘制一个简单的背景，确保有内容显示
				console.log('🎨 绘制简单背景')
				ctx.setFillStyle('#667eea')
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

				// 绘制标题
				console.log('📝 开始绘制标题...')
				this.drawTitle(ctx)
				console.log('✅ 标题绘制完成')

				// 绘制描述
				console.log('📄 开始绘制描述...')
				this.drawDescription(ctx)
				console.log('✅ 描述绘制完成')

				// 邀请码已隐藏，二维码包含完整邀请信息

				// 绘制二维码占位符
				console.log('🔲 开始绘制二维码占位符...')
				this.drawQRCodePlaceholder(ctx,
					this.posterConfig.qrCodePosition.x,
					this.posterConfig.qrCodePosition.y,
					this.posterConfig.qrCodeSize)
				console.log('✅ 二维码占位符绘制完成')

				console.log('🎉 海报内容绘制全部完成')

			} catch (error) {
				console.error('❌ 绘制海报内容失败:', error)
				// 即使出错也不抛出异常，让海报能够显示
				console.log('⚠️ 继续导出海报，忽略绘制错误')
			}
		},

		// 绘制背景
		async drawBackground(ctx) {
			console.log('🖼️ 背景图片URL:', this.posterConfig.backgroundImage)

			// 暂时先使用默认背景，确保海报能正常显示
			console.log('🎨 使用默认渐变背景')
			// 使用默认渐变背景
			const gradient = ctx.createLinearGradient(0, 0, 0, this.canvasHeight)
			gradient.addColorStop(0, '#667eea')
			gradient.addColorStop(1, '#764ba2')

			ctx.setFillStyle(gradient)
			ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

			// 如果有背景图片配置，尝试加载（但不阻塞）
			if (this.posterConfig.backgroundImage) {
				try {
					console.log('📥 尝试下载背景图片...')
					const downloadResult = await Promise.race([
						uni.downloadFile({
							url: this.posterConfig.backgroundImage
						}),
						new Promise((_, reject) =>
							setTimeout(() => reject(new Error('下载超时')), 3000)
						)
					])

					console.log('📥 下载结果:', downloadResult)

					if (downloadResult.statusCode === 200) {
						console.log('✅ 背景图片下载成功，重新绘制')
						// 重新绘制背景图片
						ctx.drawImage(downloadResult.tempFilePath, 0, 0, this.canvasWidth, this.canvasHeight)
						// 重新绘制canvas
						ctx.draw()
					}
				} catch (error) {
					console.warn('⚠️ 背景图片加载失败，使用默认背景:', error.message)
				}
			}
		},

		// 绘制标题
		drawTitle(ctx) {
			ctx.setFillStyle('#ffffff')
			ctx.setFontSize(24)
			ctx.setTextAlign('center')
			ctx.fillText(this.shareConfig.title, this.canvasWidth / 2, 80)
		},

		// 绘制描述
		drawDescription(ctx) {
			ctx.setFillStyle('#ffffff')
			ctx.setFontSize(16)
			ctx.setTextAlign('center')

			// 处理长文本换行
			const maxWidth = this.canvasWidth - 40
			const lines = this.wrapText(ctx, this.shareConfig.description, maxWidth)

			lines.forEach((line, index) => {
				ctx.fillText(line, this.canvasWidth / 2, 120 + index * 25)
			})
		},



		// 绘制二维码
		async drawQRCode(ctx) {
			const qrX = this.posterConfig.qrCodePosition.x
			const qrY = this.posterConfig.qrCodePosition.y
			const qrSize = this.posterConfig.qrCodeSize

			console.log('🔲 二维码位置:', qrX, qrY, '尺寸:', qrSize)
			console.log('🔗 邀请链接:', this.inviteUrl)

			// 先绘制占位符，确保海报能正常显示
			this.drawQRCodePlaceholder(ctx, qrX, qrY, qrSize)

			// 如果有邀请链接，尝试生成真实二维码（异步，不阻塞海报生成）
			if (this.inviteUrl) {
				try {
					// 设置较短的超时时间，避免阻塞
					const qrCodePath = await this.generateQRCodeFromServer()
					const qrCodeUrl = this.getFullImageUrl(qrCodePath)
					console.log('🔲 二维码URL:', qrCodeUrl)

					if (qrCodeUrl) {
						// 下载二维码图片
						const downloadResult = await Promise.race([
							uni.downloadFile({
								url: qrCodeUrl
							}),
							new Promise((_, reject) =>
								setTimeout(() => reject(new Error('下载超时')), 5000)
							)
						])

						console.log('📥 二维码下载结果:', downloadResult)

						if (downloadResult.statusCode === 200) {
							// 重新绘制白色背景
							ctx.setFillStyle('#ffffff')
							ctx.fillRect(qrX - 5, qrY - 5, qrSize + 10, qrSize + 10)

							// 绘制二维码图片
							ctx.drawImage(downloadResult.tempFilePath, qrX, qrY, qrSize, qrSize)
							console.log('✅ 二维码绘制成功')

							// 重新绘制canvas
							ctx.draw()
						} else {
							console.warn('⚠️ 二维码下载失败，状态码:', downloadResult.statusCode)
						}
					}
				} catch (error) {
					console.warn('⚠️ 二维码生成失败，使用占位符:', error.message)
				}
			}
		},

		// 绘制二维码占位符
		drawQRCodePlaceholder(ctx, qrX, qrY, qrSize) {
			// 绘制白色背景
			ctx.setFillStyle('#ffffff')
			ctx.fillRect(qrX - 5, qrY - 5, qrSize + 10, qrSize + 10)

			// 绘制二维码占位符边框
			ctx.setStrokeStyle('#cccccc')
			ctx.setLineWidth(2)
			ctx.strokeRect(qrX, qrY, qrSize, qrSize)

			// 绘制占位符内容
			ctx.setFillStyle('#666666')
			ctx.setFontSize(14)
			ctx.setTextAlign('center')
			ctx.fillText('扫码注册', qrX + qrSize / 2, qrY + qrSize / 2 - 10)

			ctx.setFontSize(12)
			ctx.setFillStyle('#999999')
			ctx.fillText(this.inviteCode || 'INV123456', qrX + qrSize / 2, qrY + qrSize / 2 + 10)
		},

		// 绘制邀请码功能已移除，邀请信息包含在二维码中

		// 文本换行处理
		wrapText(ctx, text, maxWidth) {
			const words = text.split('')
			const lines = []
			let currentLine = ''

			for (let i = 0; i < words.length; i++) {
				const testLine = currentLine + words[i]
				const metrics = ctx.measureText(testLine)
				const testWidth = metrics.width

				if (testWidth > maxWidth && i > 0) {
					lines.push(currentLine)
					currentLine = words[i]
				} else {
					currentLine = testLine
				}
			}
			lines.push(currentLine)
			return lines
		},

		// 关闭海报弹窗
		closePosterModal() {
			this.showPosterModal = false
			this.posterGenerating = false // 重置生成状态
			console.log('🚪 关闭海报弹窗，保留缓存数据')
			// 注意：不清除 qrCodeUrl、inviteCode、inviteUrl，保持缓存
		},

		// 图片加载成功
		onImageLoad(e) {
			console.log('✅ 图片加载成功:', e)
		},

		// 图片加载失败
		onImageError(e) {
			console.error('❌ 图片加载失败:', e)
			console.log('🔍 当前 posterImageUrl:', this.posterImageUrl)
		},



		// 保存海报到相册
		async savePosterToAlbum() {
			// 防抖：如果正在保存，直接返回
			if (this.isSaving) {
				console.log('⚠️ 正在保存中，忽略重复点击')
				return
			}

			if (!this.qrCodeUrl) {
				uni.showToast({
					title: '海报未生成',
					icon: 'none'
				})
				return
			}

			try {
				this.isSaving = true
				console.log('🎯 开始保存海报到相册...')

				// 显示加载提示
				uni.showLoading({
					title: '保存中...',
					mask: true
				})

				// #ifdef H5
				// H5环境：直接下载背景图片
				await this.saveOriginalImageH5()
				// #endif

				// #ifndef H5
				// 非H5环境：将Canvas转换为图片并保存到相册
				await this.saveCanvasAsImageUniApp()
				// #endif

			} catch (error) {
				console.error('❌ 保存海报失败:', error)
				uni.showToast({
					title: '保存失败: ' + error.message,
					icon: 'none'
				})
			} finally {
				this.isSaving = false
				uni.hideLoading()
			}
		},

		// H5环境：将Canvas转换为图片并下载
		async saveCanvasAsImageH5() {
			try {
				console.log('🎯 开始H5保存流程...')

				// 创建一个最简单的测试Canvas
				const canvas = document.createElement('canvas')
				canvas.width = 375
				canvas.height = 667
				const ctx = canvas.getContext('2d')

				console.log('🎨 创建新Canvas:', canvas.width, 'x', canvas.height)
				console.log('🎨 Canvas上下文:', ctx)

				// 绘制渐变背景
				const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
				gradient.addColorStop(0, '#667eea')
				gradient.addColorStop(1, '#764ba2')
				ctx.fillStyle = gradient
				ctx.fillRect(0, 0, canvas.width, canvas.height)
				console.log('✅ 渐变背景绘制完成')

				// 绘制标题区域
				ctx.fillStyle = '#ffffff'
				ctx.textAlign = 'center'
				ctx.shadowColor = 'rgba(0, 0, 0, 0.3)'
				ctx.shadowBlur = 4
				ctx.shadowOffsetX = 2
				ctx.shadowOffsetY = 2

				// 主标题
				ctx.font = 'bold 32px Arial, sans-serif'
				ctx.fillText('邀请好友', canvas.width / 2, 80)
				console.log('✅ 主标题绘制完成')

				// 副标题
				ctx.font = '20px Arial, sans-serif'
				ctx.fillText('扫码注册享受优惠', canvas.width / 2, 120)
				console.log('✅ 副标题绘制完成')

				// 绘制二维码区域
				await this.drawSimpleQRCode(ctx, canvas)

				// 邀请码已隐藏，二维码包含完整邀请信息

				// 绘制底部提示
				ctx.font = '14px Arial, sans-serif'
				ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
				ctx.fillText('长按保存图片，分享给好友', canvas.width / 2, canvas.height - 15)
				console.log('✅ 底部提示绘制完成')

				// 检查Canvas内容
				const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
				const hasContent = imageData.data.some(pixel => pixel !== 0)
				console.log('🔍 Canvas是否有内容:', hasContent)

				if (!hasContent) {
					throw new Error('Canvas内容为空，无法生成海报')
				}

				// 将Canvas转换为图片数据
				console.log('🖼️ 开始转换Canvas为图片...')
				const dataURL = canvas.toDataURL('image/jpeg', 0.95)

				if (!dataURL || dataURL === 'data:,') {
					throw new Error('Canvas转换失败，无法生成图片数据')
				}

				console.log('✅ Canvas转换成功，数据长度:', dataURL.length)
				console.log('✅ 数据URL前缀:', dataURL.substring(0, 50))

				// 创建下载链接
				const link = document.createElement('a')
				link.download = `邀请海报_${new Date().getTime()}.jpg`
				link.href = dataURL

				console.log('🔗 创建下载链接:', link.download)

				// 触发下载
				document.body.appendChild(link)
				console.log('📎 添加链接到DOM')

				link.click()
				console.log('👆 触发点击下载')

				document.body.removeChild(link)
				console.log('🗑️ 移除下载链接')

				console.log('✅ H5环境海报下载成功')

				// 显示成功提示
				uni.showToast({
					title: '海报已下载到本地',
					icon: 'success',
					duration: 3000
				})

			} catch (error) {
				console.error('❌ H5环境保存失败:', error)
				console.error('❌ 错误类型:', typeof error)
				console.error('❌ 错误消息:', error.message)
				console.error('❌ 错误堆栈:', error.stack)
				throw error
			}
		},

		// H5环境：Canvas合成完整海报（背景图+二维码）
		async saveOriginalImageH5() {
			try {
				console.log('🎯 开始H5 Canvas合成海报流程...')

				// 获取背景图片URL和二维码URL
				const backgroundImageUrl = this.getFullImageUrl(this.posterConfig.posterbackgroundimage)
				const qrCodeUrl = this.qrCodeUrl

				if (!backgroundImageUrl) {
					throw new Error('背景图片URL为空')
				}
				if (!qrCodeUrl) {
					throw new Error('二维码URL为空')
				}

				console.log('🖼️ 背景图片URL:', backgroundImageUrl)
				console.log('📱 二维码URL:', qrCodeUrl)

				// 创建Canvas
				const canvas = document.createElement('canvas')
				const ctx = canvas.getContext('2d')

				// 加载背景图片
				const backgroundImg = new Image()
				backgroundImg.crossOrigin = 'anonymous'

				await new Promise((resolve, reject) => {
					backgroundImg.onload = resolve
					backgroundImg.onerror = reject
					backgroundImg.src = backgroundImageUrl
				})

				console.log('✅ 背景图片加载完成:', backgroundImg.width, 'x', backgroundImg.height)

				// 设置Canvas尺寸为背景图片尺寸
				canvas.width = backgroundImg.width
				canvas.height = backgroundImg.height

				// 绘制背景图片
				ctx.drawImage(backgroundImg, 0, 0, canvas.width, canvas.height)
				console.log('✅ 背景图片绘制完成')

				// 加载二维码图片
				const qrImg = new Image()
				qrImg.crossOrigin = 'anonymous'

				await new Promise((resolve, reject) => {
					qrImg.onload = resolve
					qrImg.onerror = reject
					qrImg.src = qrCodeUrl
				})

				console.log('✅ 二维码图片加载完成:', qrImg.width, 'x', qrImg.height)

				// 计算二维码位置（根据配置或默认位置）
				const qrSize = Math.min(canvas.width, canvas.height) * 0.2 // 二维码大小为画布的20%
				const qrX = canvas.width - qrSize - 30 // 右下角，留30px边距
				const qrY = canvas.height - qrSize - 30

				// 绘制二维码
				ctx.drawImage(qrImg, qrX, qrY, qrSize, qrSize)
				console.log('✅ 二维码绘制完成，位置:', qrX, qrY, '尺寸:', qrSize)

				// 转换为图片并下载
				const dataURL = canvas.toDataURL('image/jpeg', 0.95)

				const link = document.createElement('a')
				link.href = dataURL
				link.download = `邀请海报_${new Date().getTime()}.jpg`

				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)

				console.log('✅ H5环境完整海报下载成功')
				uni.showToast({
					title: '海报已下载',
					icon: 'success'
				})

			} catch (error) {
				console.error('❌ H5 Canvas合成失败:', error)
				// 降级到DOM截图方案
				console.log('🔄 降级到DOM截图方案...')
				await this.saveCanvasAsImageH5DOM()
			}
		},

		// H5环境：直接截取海报DOM元素并下载（备用方案）
		async saveCanvasAsImageH5DOM() {
			try {
				console.log('🎯 开始H5 DOM截取保存流程...')

				// 精确查找海报容器元素
				const posterElement = document.querySelector('.poster-container')
				if (!posterElement) {
					throw new Error('找不到海报容器元素')
				}

				console.log('📦 找到海报容器元素:', posterElement)
				console.log('📄 容器HTML内容:', posterElement.innerHTML.substring(0, 300))

				// 检查背景图片元素
				const backgroundElement = posterElement.querySelector('.poster-background')
				if (backgroundElement) {
					console.log('🖼️ 背景元素存在:', backgroundElement)
					const bgStyle = window.getComputedStyle(backgroundElement)
					console.log('🎨 背景图片:', bgStyle.backgroundImage)
					console.log('📏 背景元素尺寸:', backgroundElement.offsetWidth, 'x', backgroundElement.offsetHeight)
				} else {
					console.warn('⚠️ 未找到背景元素 .poster-background')
				}

				// 检查二维码元素
				const qrElement = posterElement.querySelector('.qrcode-overlay')
				if (qrElement) {
					console.log('📱 二维码元素存在:', qrElement)
					console.log('📏 二维码尺寸:', qrElement.offsetWidth, 'x', qrElement.offsetHeight)
				} else {
					console.warn('⚠️ 未找到二维码元素 .qrcode-overlay')
				}

				// 获取实际的渲染尺寸
				const rect = posterElement.getBoundingClientRect()
				const computedStyle = window.getComputedStyle(posterElement)

				console.log('📏 容器offsetSize:', posterElement.offsetWidth, 'x', posterElement.offsetHeight)
				console.log('📏 容器boundingRect:', rect.width, 'x', rect.height)
				console.log('📏 容器computedStyle:', computedStyle.width, 'x', computedStyle.height)

				// 使用实际的渲染尺寸
				const actualWidth = Math.max(posterElement.offsetWidth, rect.width)
				const actualHeight = Math.max(posterElement.offsetHeight, rect.height)
				console.log('📏 实际使用尺寸:', actualWidth, 'x', actualHeight)

				// 等待图片加载完成
				await this.waitForImagesLoad(posterElement)

				// 使用html2canvas截取海报
				console.log('📸 开始截取海报...')

				// 动态导入html2canvas
				let html2canvas
				try {
					// 尝试从CDN加载html2canvas
					if (!window.html2canvas) {
						console.log('📥 加载html2canvas库...')
						await this.loadHtml2Canvas()
					}
					html2canvas = window.html2canvas
				} catch (error) {
					console.warn('⚠️ 无法加载html2canvas，使用Canvas降级方案')
					return await this.saveCanvasAsImageH5()
				}

				// 再次检查html2canvas是否可用
				if (!html2canvas) {
					console.warn('⚠️ html2canvas不可用，使用Canvas降级方案')
					return await this.saveCanvasAsImageH5()
				}

				let canvas
				try {
					// 临时移除圆角样式，避免截取时出现问题
					const originalBorderRadius = posterElement.style.borderRadius || ''
					posterElement.style.borderRadius = '0'

					// 高质量截取海报容器
					canvas = await html2canvas(posterElement, {
						useCORS: true,
						allowTaint: true,
						backgroundColor: null,
						scale: 4, // 提高到4倍分辨率，确保清晰度
						logging: false,
						width: actualWidth,
						height: actualHeight,
						// 高质量渲染选项
						foreignObjectRendering: true, // 启用以提高质量
						removeContainer: false,
						// 精确定位
						x: 0,
						y: 0,
						scrollX: 0,
						scrollY: 0,
						// 图片处理优化
						proxy: undefined,
						imageTimeout: 15000,
						// 渲染优化
						letterRendering: true,
						// 不忽略任何元素
						ignoreElements: function(element) {
							return false
						}
					})

					// 恢复圆角样式
					posterElement.style.borderRadius = originalBorderRadius

				} catch (html2canvasError) {
					console.warn('⚠️ html2canvas截取失败，使用Canvas降级方案:', html2canvasError)
					// 恢复圆角样式
					if (posterElement.style.borderRadius === '0') {
						posterElement.style.borderRadius = originalBorderRadius || ''
					}
					return await this.saveCanvasAsImageH5()
				}

				console.log('✅ 海报截取成功')
				console.log('📏 原始Canvas尺寸:', canvas.width, 'x', canvas.height)

				// 检查Canvas是否有有效内容
				if (!canvas || canvas.width < 50 || canvas.height < 50) {
					console.warn('⚠️ 截取的Canvas尺寸异常，使用Canvas降级方案')
					return await this.saveCanvasAsImageH5()
				}

				// 检查Canvas内容是否为空
				const ctx = canvas.getContext('2d')
				const imageData = ctx.getImageData(0, 0, Math.min(canvas.width, 100), Math.min(canvas.height, 100))
				const hasContent = imageData.data.some((pixel, index) => {
					// 检查alpha通道，如果有非透明像素就认为有内容
					return index % 4 === 3 && pixel > 10
				})

				if (!hasContent) {
					console.warn('⚠️ 截取的Canvas内容为空，使用Canvas降级方案')
					return await this.saveCanvasAsImageH5()
				}

				// 智能裁剪去除白边和多余空间
				const croppedCanvas = this.smartCropCanvas(canvas)
				console.log('📏 裁剪后Canvas尺寸:', croppedCanvas.width, 'x', croppedCanvas.height)

				// 高质量转换为图片
				console.log('🖼️ 开始高质量转换为图片...')
				const dataURL = croppedCanvas.toDataURL('image/jpeg', 0.95) // 使用JPEG格式，95%质量
				console.log('✅ 转换成功，数据长度:', dataURL.length)

				// 创建下载链接
				const link = document.createElement('a')
				link.download = `邀请海报_${new Date().getTime()}.jpg`
				link.href = dataURL
				console.log('🔗 创建下载链接:', link.download)

				// 添加到DOM并触发下载
				document.body.appendChild(link)
				console.log('📎 添加链接到DOM')
				link.click()
				console.log('👆 触发点击下载')

				// 清理
				document.body.removeChild(link)
				console.log('🗑️ 移除下载链接')

				console.log('✅ H5环境海报下载成功')
				uni.showToast({
					title: '海报已下载',
					icon: 'success'
				})

			} catch (error) {
				console.error('❌ H5 DOM保存失败:', error)
				throw error
			}
		},

		// 加载html2canvas库
		async loadHtml2Canvas() {
			return new Promise((resolve, reject) => {
				if (window.html2canvas) {
					resolve()
					return
				}

				const script = document.createElement('script')
				script.src = 'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js'
				script.onload = () => {
					console.log('✅ html2canvas加载成功')
					resolve()
				}
				script.onerror = () => {
					console.error('❌ html2canvas加载失败')
					reject(new Error('html2canvas加载失败'))
				}
				document.head.appendChild(script)
			})
		},

		// 等待图片加载完成
		async waitForImagesLoad(element) {
			const images = element.querySelectorAll('img, [style*="background-image"]')
			console.log('🖼️ 找到图片元素数量:', images.length)

			const promises = Array.from(images).map(img => {
				return new Promise((resolve) => {
					if (img.tagName === 'IMG') {
						if (img.complete) {
							resolve()
						} else {
							img.onload = resolve
							img.onerror = resolve // 即使加载失败也继续
							setTimeout(resolve, 3000) // 3秒超时
						}
					} else {
						// 背景图片，直接resolve
						resolve()
					}
				})
			})

			await Promise.all(promises)
			console.log('✅ 所有图片加载完成')
		},

		// 智能裁剪Canvas，去除白边并保持比例
		smartCropCanvas(canvas) {
			console.log('🔍 开始智能裁剪，原始尺寸:', canvas.width, 'x', canvas.height)

			const ctx = canvas.getContext('2d')
			const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
			const data = imageData.data

			// 检测有效内容边界（更严格的阈值）
			let top = canvas.height, bottom = 0, left = canvas.width, right = 0
			let hasAnyContent = false

			for (let y = 0; y < canvas.height; y++) {
				for (let x = 0; x < canvas.width; x++) {
					const idx = (y * canvas.width + x) * 4
					const r = data[idx]
					const g = data[idx + 1]
					const b = data[idx + 2]
					const alpha = data[idx + 3]

					// 检查是否为有效像素（不是纯白或透明）
					const isValidPixel = alpha > 20 && !(r > 240 && g > 240 && b > 240)

					if (isValidPixel) {
						hasAnyContent = true
						top = Math.min(top, y)
						bottom = Math.max(bottom, y)
						left = Math.min(left, x)
						right = Math.max(right, x)
					}
				}
			}

			if (!hasAnyContent) {
				console.warn('⚠️ 未检测到有效内容，返回原Canvas')
				return canvas
			}

			// 添加小量边距，避免裁剪过紧
			const margin = 5
			top = Math.max(0, top - margin)
			bottom = Math.min(canvas.height, bottom + margin + 1)
			left = Math.max(0, left - margin)
			right = Math.min(canvas.width, right + margin + 1)

			console.log('🔍 检测到的有效边界:', { top, bottom, left, right })

			const croppedWidth = right - left
			const croppedHeight = bottom - top

			if (croppedWidth <= 0 || croppedHeight <= 0) {
				console.warn('⚠️ 裁剪尺寸无效，返回原Canvas')
				return canvas
			}

			// 创建高质量裁剪Canvas
			const croppedCanvas = document.createElement('canvas')
			croppedCanvas.width = croppedWidth
			croppedCanvas.height = croppedHeight
			const croppedCtx = croppedCanvas.getContext('2d')

			// 启用图像平滑
			croppedCtx.imageSmoothingEnabled = true
			croppedCtx.imageSmoothingQuality = 'high'

			// 复制裁剪区域
			croppedCtx.drawImage(
				canvas,
				left, top, croppedWidth, croppedHeight,
				0, 0, croppedWidth, croppedHeight
			)

			console.log('✂️ 智能裁剪完成:', croppedWidth, 'x', croppedHeight)
			return croppedCanvas
		},

		// 原始裁剪函数（备用）
		cropWhiteSpace(canvas) {
			const ctx = canvas.getContext('2d')
			const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
			const data = imageData.data

			let top = 0, bottom = canvas.height, left = 0, right = canvas.width

			// 查找顶部边界
			for (let y = 0; y < canvas.height; y++) {
				let hasContent = false
				for (let x = 0; x < canvas.width; x++) {
					const idx = (y * canvas.width + x) * 4
					const alpha = data[idx + 3]
					if (alpha > 10) { // 不是完全透明
						hasContent = true
						break
					}
				}
				if (hasContent) {
					top = y
					break
				}
			}

			// 查找底部边界
			for (let y = canvas.height - 1; y >= 0; y--) {
				let hasContent = false
				for (let x = 0; x < canvas.width; x++) {
					const idx = (y * canvas.width + x) * 4
					const alpha = data[idx + 3]
					if (alpha > 10) {
						hasContent = true
						break
					}
				}
				if (hasContent) {
					bottom = y + 1
					break
				}
			}

			// 查找左边界
			for (let x = 0; x < canvas.width; x++) {
				let hasContent = false
				for (let y = 0; y < canvas.height; y++) {
					const idx = (y * canvas.width + x) * 4
					const alpha = data[idx + 3]
					if (alpha > 10) {
						hasContent = true
						break
					}
				}
				if (hasContent) {
					left = x
					break
				}
			}

			// 查找右边界
			for (let x = canvas.width - 1; x >= 0; x--) {
				let hasContent = false
				for (let y = 0; y < canvas.height; y++) {
					const idx = (y * canvas.width + x) * 4
					const alpha = data[idx + 3]
					if (alpha > 10) {
						hasContent = true
						break
					}
				}
				if (hasContent) {
					right = x + 1
					break
				}
			}

			console.log('🔍 检测到的边界:', { top, bottom, left, right })

			// 创建裁剪后的Canvas
			const croppedWidth = right - left
			const croppedHeight = bottom - top

			if (croppedWidth <= 0 || croppedHeight <= 0) {
				console.warn('⚠️ 裁剪尺寸无效，返回原Canvas')
				return canvas
			}

			const croppedCanvas = document.createElement('canvas')
			croppedCanvas.width = croppedWidth
			croppedCanvas.height = croppedHeight
			const croppedCtx = croppedCanvas.getContext('2d')

			// 复制裁剪区域
			croppedCtx.drawImage(
				canvas,
				left, top, croppedWidth, croppedHeight,
				0, 0, croppedWidth, croppedHeight
			)

			console.log('✂️ 裁剪完成:', croppedWidth, 'x', croppedHeight)
			return croppedCanvas
		},

		// 简单的二维码绘制方法
		async drawSimpleQRCode(ctx, canvas) {
			try {
				if (!this.qrCodeUrl) {
					console.warn('⚠️ 二维码URL为空，绘制占位符')
					// 绘制二维码占位符
					const qrSize = 150
					const x = (canvas.width - qrSize) / 2
					const y = (canvas.height - qrSize) / 2 - 50

					// 白色背景
					ctx.fillStyle = '#ffffff'
					ctx.fillRect(x - 10, y - 10, qrSize + 20, qrSize + 20)

					// 灰色边框
					ctx.strokeStyle = '#cccccc'
					ctx.lineWidth = 2
					ctx.strokeRect(x - 10, y - 10, qrSize + 20, qrSize + 20)

					// 占位文字
					ctx.fillStyle = '#666666'
					ctx.font = '16px Arial'
					ctx.textAlign = 'center'
					ctx.fillText('二维码', canvas.width / 2, y + qrSize / 2)
					return
				}

				const img = new Image()

				// 设置跨域属性
				img.crossOrigin = 'anonymous'

				await new Promise((resolve, reject) => {
					img.onload = () => {
						try {
							const qrSize = 150
							const x = (canvas.width - qrSize) / 2
							const y = (canvas.height - qrSize) / 2 - 50

							// 绘制白色背景
							ctx.fillStyle = '#ffffff'
							ctx.fillRect(x - 10, y - 10, qrSize + 20, qrSize + 20)

							// 绘制二维码
							ctx.drawImage(img, x, y, qrSize, qrSize)
							console.log('✅ 二维码绘制成功')
							resolve()
						} catch (drawError) {
							console.warn('⚠️ 二维码绘制失败:', drawError)
							resolve() // 不要阻止整个流程
						}
					}

					img.onerror = (error) => {
						console.warn('⚠️ 二维码加载失败:', error)
						resolve() // 不要阻止整个流程
					}

					// 设置超时
					setTimeout(() => {
						console.warn('⚠️ 二维码加载超时')
						resolve() // 不要阻止整个流程
					}, 3000)

					img.src = this.qrCodeUrl
				})
			} catch (error) {
				console.warn('⚠️ 二维码绘制失败，跳过:', error)
			}
		},

		// 专门用于保存的背景绘制方法
		async drawSaveBackgroundH5(ctx, canvas) {
			try {
				// 使用简单的渐变背景，避免图片加载问题
				const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
				gradient.addColorStop(0, '#667eea')
				gradient.addColorStop(1, '#764ba2')

				ctx.fillStyle = gradient
				ctx.fillRect(0, 0, canvas.width, canvas.height)
				console.log('✅ 保存用渐变背景绘制成功')
			} catch (error) {
				console.warn('⚠️ 背景绘制失败，使用纯色:', error)
				ctx.fillStyle = '#667eea'
				ctx.fillRect(0, 0, canvas.width, canvas.height)
			}
		},

		// 专门用于保存的二维码绘制方法
		async drawSaveQRCodeH5(ctx, canvas) {
			try {
				if (!this.qrCodeUrl) {
					console.warn('⚠️ 二维码URL为空，跳过绘制')
					return
				}

				const img = new Image()

				await new Promise((resolve, reject) => {
					img.onload = () => {
						try {
							const qrSize = 120
							const x = (canvas.width - qrSize) / 2
							const y = canvas.height - qrSize - 80

							// 绘制白色背景
							ctx.fillStyle = '#ffffff'
							ctx.fillRect(x - 10, y - 10, qrSize + 20, qrSize + 20)

							// 绘制二维码
							ctx.drawImage(img, x, y, qrSize, qrSize)
							console.log('✅ 保存用二维码绘制成功')
							resolve()
						} catch (drawError) {
							console.warn('⚠️ 二维码绘制失败:', drawError)
							reject(drawError)
						}
					}

					img.onerror = (error) => {
						console.warn('⚠️ 二维码加载失败:', error)
						reject(error)
					}

					// 设置超时
					setTimeout(() => {
						reject(new Error('二维码加载超时'))
					}, 5000)

					img.src = this.qrCodeUrl
				})
			} catch (error) {
				console.warn('⚠️ 二维码绘制失败，跳过:', error)
			}
		},

		// 专门用于保存的文字绘制方法
		drawSaveTextH5(ctx, canvas) {
			try {
				// 设置文字样式
				ctx.fillStyle = '#ffffff'
				ctx.textAlign = 'center'
				ctx.shadowColor = 'rgba(0, 0, 0, 0.5)'
				ctx.shadowBlur = 4
				ctx.shadowOffsetX = 2
				ctx.shadowOffsetY = 2

				// 绘制标题
				ctx.font = 'bold 28px Arial, sans-serif'
				ctx.fillText('邀请好友', canvas.width / 2, 60)

				// 绘制副标题
				ctx.font = '18px Arial, sans-serif'
				ctx.fillText('扫码注册享受优惠', canvas.width / 2, 100)

				// 邀请码已隐藏，二维码包含完整邀请信息

				console.log('✅ 保存用文字绘制成功')
			} catch (error) {
				console.warn('⚠️ 文字绘制失败:', error)
			}
		},

		// 非H5环境：将Canvas转换为图片并保存到相册
		async saveCanvasAsImageUniApp() {
			try {
				// 将Canvas转换为临时文件
				const result = await new Promise((resolve, reject) => {
					uni.canvasToTempFilePath({
						canvasId: 'posterCanvas',
						success: resolve,
						fail: reject
					})
				})

				console.log('📱 Canvas转换结果:', result)

				// 保存到相册
				await new Promise((resolve, reject) => {
					uni.saveImageToPhotosAlbum({
						filePath: result.tempFilePath,
						success: resolve,
						fail: reject
					})
				})

				uni.showToast({
					title: '已保存到相册',
					icon: 'success'
				})

				console.log('✅ 非H5环境海报保存成功')
			} catch (error) {
				console.error('❌ 非H5环境保存失败:', error)
				throw error
			}
		},

		// 分享海报
		async sharePoster() {
			if (!this.qrCodeUrl) {
				uni.showToast({
					title: '海报未生成',
					icon: 'none'
				})
				return
			}

			try {
				// #ifdef H5
				// H5环境下，使用 Web Share API 或复制链接
				if (navigator.share) {
					// 支持 Web Share API
					await navigator.share({
						title: this.shareConfig.title,
						text: this.shareConfig.description,
						url: window.location.href
					})
				} else {
					// 不支持 Web Share API，复制链接到剪贴板
					await navigator.clipboard.writeText(window.location.href)
					uni.showToast({
						title: '链接已复制到剪贴板',
						icon: 'success'
					})
				}
				// #endif

				// #ifndef H5
				await uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					type: 2,
					imageUrl: this.posterImageUrl,
					title: this.shareConfig.title,
					summary: this.shareConfig.description
				})
				// #endif
			} catch (error) {
				console.error('分享失败:', error)
				uni.showToast({
					title: '分享失败',
					icon: 'none'
				})
			}
		},

		// 复制邀请链接
		copyInviteCode() {
			if (!this.inviteUrl) {
				uni.showToast({
					title: '邀请链接未生成',
					icon: 'none'
				})
				return
			}

			uni.setClipboardData({
				data: this.inviteUrl,
				success: () => {
					uni.showToast({
						title: '邀请链接已复制',
						icon: 'success'
					})
				}
			})
		},

		// 复制邀请链接
		copyInviteUrl() {
			if (!this.inviteUrl) {
				uni.showToast({
					title: '邀请链接未生成',
					icon: 'none'
				})
				return
			}

			uni.setClipboardData({
				data: this.inviteUrl,
				success: () => {
					uni.showToast({
						title: '邀请链接已复制',
						icon: 'success'
					})
				}
			})
		},

		// 检查二维码缓存是否有效
		checkQRCodeCache(userId) {
			if (!this.qrCodeCache[userId]) {
				return false
			}

			const cached = this.qrCodeCache[userId]
			const now = Date.now()
			const cacheExpiry = 24 * 60 * 60 * 1000 // 24小时缓存

			// 检查缓存是否过期
			if (now - cached.timestamp > cacheExpiry) {
				delete this.qrCodeCache[userId]
				return false
			}

			return true
		},

		// 缓存二维码信息
		cacheQRCodeInfo(userId, qrCodeUrl, inviteCode, inviteUrl) {
			this.qrCodeCache[userId] = {
				qrCodeUrl,
				inviteCode,
				inviteUrl,
				timestamp: Date.now()
			}
			console.log('💾 缓存二维码信息:', this.qrCodeCache[userId])
		},

		// 清除二维码缓存
		clearQRCodeCache(userId = null) {
			if (userId) {
				delete this.qrCodeCache[userId]
				console.log('🗑️ 清除用户二维码缓存:', userId)
			} else {
				this.qrCodeCache = {}
				console.log('🗑️ 清除所有二维码缓存')
			}
		}
	}
}
</script>

<style>
.profile-page {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
	background-size: 400% 400%;
	min-height: 100vh;
	padding: 0;
	position: relative;
	overflow: hidden;
	animation: gradientFlow 15s ease-in-out infinite;
}

.scroll-container {
	height: 100vh;
	width: 100%;
	position: relative;
	z-index: 2;
}

/* 主背景动画层 */
.profile-page::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background:
		radial-gradient(circle at 20% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
		radial-gradient(circle at 80% 80%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
		radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
		radial-gradient(circle at 60% 60%, rgba(255, 180, 120, 0.2) 0%, transparent 50%);
	animation: floatingOrbs 20s ease-in-out infinite;
	z-index: 1;
}

/* 第二层动画效果 */
.profile-page::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background:
		linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%),
		linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%);
	background-size: 200% 200%;
	animation: shimmerEffect 8s linear infinite;
	z-index: 1;
}

@keyframes gradientShift {
	0%, 100% { opacity: 0.3; }
	50% { opacity: 0.6; }
}

/* 顶部用户信息 */
.header-section {
	padding: 40px 0 0;
	position: relative;
	z-index: 1;
}

/* 用户基本信息 - 第一排 */
.user-basic-info {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	margin: 0 20px 12px 20px;
	flex: 0 0 auto;
	padding: 0;
}

/* 用户文字信息容器 */
.user-text-info {
	display: flex;
	flex-direction: column;
	gap: 5px;
	flex: 1;
	margin-left: 15px;
}

/* 用户名行容器 */
.username-row {
	margin-bottom: 2px;
}

/* UID和标签行容器 */
.uid-tags-row {
	display: flex;
	align-items: center;
	gap: 10px;
	flex-wrap: wrap;
}

/* 用户标签基础样式 */
.user-tag {
	padding: 2px 8px;
	border-radius: 10px;
	font-size: 12px;
	line-height: 1.2;
	cursor: pointer;
	transition: all 0.3s ease;
	border: 1px solid transparent;
}

/* 合伙人标签 */
.partner-tag {
	background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
	color: white;
	box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

.partner-tag:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 8px rgba(255, 107, 107, 0.4);
}

/* VIP会员标签 */
.vip-tag {
	background: linear-gradient(135deg, #ffd700, #ffed4e);
	color: #333;
	box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.vip-tag:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 8px rgba(255, 215, 0, 0.4);
}

/* 普通会员标签 */
.normal-tag {
	background: linear-gradient(135deg, #e0e0e0, #f0f0f0);
	color: #666;
	border: 1px solid #ddd;
}

/* 非合伙人标签 */
.non-partner-tag {
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	color: #6c757d;
	border: 1px solid #dee2e6;
	cursor: pointer;
}

.non-partner-tag:hover {
	transform: translateY(-1px);
	box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2);
	background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

/* 标签文字 */
.tag-text {
	font-weight: 500;
	font-size: 12px;
}

.user-avatar {
	position: relative;
	cursor: pointer;
	width: 60px;
	height: 60px;
	border-radius: 50%;
	overflow: hidden;
}

.avatar-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
	transition: all 0.3s ease;
	display: block;
	border: none;
	outline: none;
}

.avatar-border {
	position: absolute;
	top: -3px;
	left: -3px;
	right: -3px;
	bottom: -3px;
	border-radius: 50%;
	background: linear-gradient(45deg, #667eea, #764ba2, #00ff87, #60efff);
	background-size: 400% 400%;
	animation: gradientShift 3s ease infinite;
	z-index: -1;
}

.avatar-edit-hint {
	position: absolute;
	bottom: -2px;
	right: -2px;
	width: 20px;
	height: 20px;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
	transition: all 0.3s ease;
}

.edit-icon {
	font-size: 10px;
	line-height: 1;
}

.user-avatar:hover .avatar-edit-hint {
	transform: scale(1.1);
	background: rgba(255, 255, 255, 1);
}

.user-avatar:active .avatar-img {
	transform: scale(0.95);
}

.user-avatar:active .avatar-edit-hint {
	transform: scale(0.9);
}

.username {
	color: rgba(255, 255, 255, 0.95);
	font-size: 18px;
	font-weight: 700;
	text-align: left;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
	margin-bottom: 2px;
}

.user-uid {
	color: rgba(255, 255, 255, 0.7);
	font-size: 13px;
	font-weight: 500;
	text-align: left;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.login-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 20px;
	padding: 8px 16px;
	font-size: 12px;
	font-weight: 500;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
}

.login-btn:active {
	transform: translateY(2px);
	box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* 用户统计信息 - 第二排 */
.user-stats-section {
	margin: 40px 20px 24px 20px;
	padding: 0;
}

.stats-container {
	display: flex;
	align-items: center;
	justify-content: space-around;
	background: rgba(255, 255, 255, 0.12);
	backdrop-filter: blur(20px);
	border-radius: 20px;
	padding: 6px;
	border: 1px solid rgba(255, 255, 255, 0.2);
	box-shadow:
		0 8px 32px rgba(0, 0, 0, 0.1),
		inset 0 1px 0 rgba(255, 255, 255, 0.2);
	animation: pulseGlow 4s ease-in-out infinite;
	transition: all 0.3s ease;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12px;
	flex: 1;
	text-align: center;
	justify-content: center;
	padding: 8px;
	border-radius: 12px;
	transition: all 0.3s ease;
	cursor: pointer;
}

.stat-item:hover {
	background: rgba(255, 255, 255, 0.1);
	transform: translateY(-3px);
}

.stat-value {
	color: rgba(255, 255, 255, 0.98);
	font-size: 18px;
	font-weight: 800;
	text-shadow:
		0 2px 8px rgba(0, 0, 0, 0.3),
		0 0 20px rgba(255, 255, 255, 0.2);
	background: linear-gradient(135deg, #fff, #f0f8ff);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	animation: textGlow 3s ease-in-out infinite alternate;
}

.stat-label {
	color: rgba(255, 255, 255, 0.8);
	font-size: 13px;
	font-weight: 600;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	letter-spacing: 0.5px;
}

.stat-divider {
	width: 1px;
	height: 30px;
	background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.3), transparent);
}

/* 现代化背景动画效果 */
@keyframes gradientFlow {
	0% {
		background-position: 0% 50%;
		filter: hue-rotate(0deg);
	}
	25% {
		background-position: 100% 50%;
		filter: hue-rotate(90deg);
	}
	50% {
		background-position: 100% 100%;
		filter: hue-rotate(180deg);
	}
	75% {
		background-position: 0% 100%;
		filter: hue-rotate(270deg);
	}
	100% {
		background-position: 0% 50%;
		filter: hue-rotate(360deg);
	}
}

@keyframes floatingOrbs {
	0%, 100% {
		transform: translate(0, 0) rotate(0deg);
		opacity: 0.8;
	}
	25% {
		transform: translate(20px, -30px) rotate(90deg);
		opacity: 0.6;
	}
	50% {
		transform: translate(-15px, -20px) rotate(180deg);
		opacity: 0.9;
	}
	75% {
		transform: translate(-25px, 15px) rotate(270deg);
		opacity: 0.7;
	}
}

@keyframes shimmerEffect {
	0% {
		background-position: -200% -200%;
	}
	100% {
		background-position: 200% 200%;
	}
}

@keyframes pulseGlow {
	0%, 100% {
		box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
	}
	50% {
		box-shadow: 0 0 40px rgba(255, 255, 255, 0.2);
	}
}

@keyframes textGlow {
	0% {
		text-shadow:
			0 2px 8px rgba(0, 0, 0, 0.3),
			0 0 20px rgba(255, 255, 255, 0.2);
	}
	100% {
		text-shadow:
			0 2px 8px rgba(0, 0, 0, 0.3),
			0 0 30px rgba(255, 255, 255, 0.4);
	}
}

/* 未登录状态用户信息样式 */
.unauth-user-section {
	margin: 30px 20px 36px 20px;
}

.unauth-user-info {
	display: flex;
	align-items: center;
	gap: 15px;
	padding: 10px 0;
}

.unauth-avatar {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	overflow: hidden;
	background: rgba(255, 255, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.unauth-avatar:hover {
	transform: scale(1.05);
}

.unauth-avatar-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.unauth-text {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.unauth-title {
	font-size: 18px;
	font-weight: 600;
	color: rgba(255, 255, 255, 0.95);
}

.unauth-desc {
	font-size: 14px;
	color: rgba(255, 255, 255, 0.6);
}

.test-login-btn {
	margin-top: 8px;
	padding: 6px 12px;
	background: rgba(255, 255, 255, 0.2);
	color: white;
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 6px;
	font-size: 12px;
	cursor: pointer;
}

.test-login-btn:hover {
	background: rgba(255, 255, 255, 0.3);
}

/* 启元AI 会员卡片 */
.member-card {
	margin: 20px 20px 24px 20px;
	background: linear-gradient(135deg, #00ff87 0%, #60efff 100%);
	border-radius: 16px;
	padding: 3px;
	position: relative;
	z-index: 1;
	animation: cardGlow 3s ease-in-out infinite;
}

@keyframes cardGlow {
	0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 135, 0.3); }
	50% { box-shadow: 0 0 30px rgba(96, 239, 255, 0.5); }
}

.card-content {
	background: rgba(26, 26, 46, 0.9);
	border-radius: 14px;
	padding: 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.card-left {
	flex: 1;
}

.card-title {
	color: #00ff87;
	font-size: 18px;
	font-weight: bold;
	display: block;
	margin-bottom: 5px;
}

.card-desc {
	color: rgba(255, 255, 255, 0.7);
	font-size: 14px;
}

.upgrade-btn {
	background: linear-gradient(135deg, #00ff87 0%, #60efff 100%);
	color: #1a1a2e;
	border: none;
	padding: 10px 20px;
	border-radius: 20px;
	font-size: 14px;
	font-weight: bold;
	box-shadow: 0 4px 15px rgba(0, 255, 135, 0.3);
}



/* 功能模块 */
.function-modules {
	margin: 20px 20px 24px 20px;
	padding: 0;
	position: relative;
	z-index: 1;
}

.modules-container {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(15px);
	border-radius: 20px;
	padding: 24px 20px;
	border: 1px solid rgba(255, 255, 255, 0.15);
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.module-row {
	display: flex;
	justify-content: center;
	margin-bottom: 20px;
	gap: 20px;
}

.module-row:last-child {
	margin-bottom: 0;
}

.module-item {
	flex: 0 0 25%;
	text-align: center;
	padding: 12px 8px;
	margin: 0;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 12px;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
	border: 1px solid rgba(255, 255, 255, 0.1);
}



.module-item::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.module-item:active {
	transform: translateY(2px);
	background: rgba(255, 255, 255, 0.1);
}

.module-item:active::before {
	opacity: 1;
}

.item-icon {
	width: 42px;
	height: 42px;
	margin: 0 auto 10px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.icon-emoji {
	font-size: 20px;
}

.item-title {
	color: rgba(255, 255, 255, 0.9);
	font-size: 14px;
	font-weight: bold;
	display: block;
	margin-bottom: 5px;
}

.item-desc {
	color: rgba(255, 255, 255, 0.6);
	font-size: 11px;
}

/* 设置弹窗样式 */
.settings-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	backdrop-filter: blur(10px);
}

.settings-modal-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20px;
	width: 320px;
	max-width: 90%;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
	overflow: hidden;
	animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
	0% {
		opacity: 0;
		transform: scale(0.7) translateY(-30px) rotateX(10deg);
		filter: blur(10px);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.02) translateY(-5px) rotateX(0deg);
		filter: blur(2px);
	}
	100% {
		opacity: 1;
		transform: scale(1) translateY(0) rotateX(0deg);
		filter: blur(0px);
	}
}

/* 添加光泽动画效果 */
@keyframes shimmer {
	0% {
		background-position: -200% 0;
	}
	100% {
		background-position: 200% 0;
	}
}

.settings-modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px 20px 15px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-modal-title {
	color: white;
	font-size: 18px;
	font-weight: bold;
}

.settings-modal-close {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.settings-modal-close:active {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(0.95);
}

.close-icon {
	color: white;
	font-size: 16px;
	font-weight: bold;
}

.settings-modal-content {
	padding: 10px 0 20px;
}

.settings-option {
	display: flex;
	align-items: center;
	padding: 15px 20px;
	cursor: pointer;
	transition: all 0.3s ease;
	border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.settings-option:last-child {
	border-bottom: none;
}

.settings-option:active {
	background: rgba(255, 255, 255, 0.1);
	transform: scale(0.98);
}

.logout-option {
	border-top: 1px solid rgba(255, 255, 255, 0.1);
	margin-top: 10px;
}

.option-icon {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15px;
}

.option-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.option-title {
	color: white;
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 2px;
}

.option-desc {
	color: rgba(255, 255, 255, 0.7);
	font-size: 12px;
}

.option-arrow {
	width: 20px;
	height: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.arrow-icon {
	color: rgba(255, 255, 255, 0.6);
	font-size: 18px;
	font-weight: bold;
}

/* 现代钱包弹窗样式 */
.modern-wallet-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(20px);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	animation: overlayFadeIn 0.3s ease-out;
}

.modern-wallet-card {
	background: #ffffff;
	border-radius: 24px;
	width: 90%;
	max-width: 420px;
	max-height: 85vh;
	overflow: hidden;
	box-shadow:
		0 20px 60px rgba(0, 0, 0, 0.15),
		0 0 0 1px rgba(0, 0, 0, 0.05);
	animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
	position: relative;
}

/* 头部样式 */
.modern-wallet-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 24px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	overflow: hidden;
}

.modern-wallet-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
	pointer-events: none;
}

.header-content {
	display: flex;
	align-items: center;
	gap: 12px;
}

.header-icon {
	width: 40px;
	height: 40px;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10px);
}

.icon-wallet {
	font-size: 20px;
}

.header-text {
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.header-title {
	color: white;
	font-size: 18px;
	font-weight: 700;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.header-subtitle {
	color: rgba(255, 255, 255, 0.8);
	font-size: 12px;
	font-weight: 400;
}

.close-button {
	width: 32px;
	height: 32px;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	backdrop-filter: blur(10px);
}

.close-button:active {
	background: rgba(255, 255, 255, 0.25);
	transform: scale(0.95);
}

.close-icon {
	color: white;
	font-size: 16px;
	font-weight: 600;
}

/* 余额卡片样式 */
.balance-card {
	margin: 20px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20px;
	padding: 24px;
	position: relative;
	overflow: hidden;
}

.balance-card-bg {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
	pointer-events: none;
}

.balance-content {
	position: relative;
	z-index: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.balance-info {
	flex: 1;
}

.balance-label {
	color: rgba(255, 255, 255, 0.9);
	font-size: 14px;
	font-weight: 500;
	margin-bottom: 8px;
	display: block;
}

.balance-amount {
	color: white;
	font-size: 28px;
	font-weight: 800;
	margin-bottom: 4px;
	display: block;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.balance-desc {
	color: rgba(255, 255, 255, 0.8);
	font-size: 12px;
	font-weight: 400;
	display: block;
}

.balance-actions {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.action-btn {
	padding: 10px 16px;
	border-radius: 12px;
	display: flex;
	align-items: center;
	gap: 6px;
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 100px;
	justify-content: center;
}

.action-btn.primary {
	background: rgba(255, 255, 255, 0.2);
	border: 1px solid rgba(255, 255, 255, 0.3);
	backdrop-filter: blur(10px);
}

.action-btn.primary:active {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(0.96);
}

.action-icon {
	font-size: 14px;
}

.action-text {
	color: white;
	font-size: 13px;
	font-weight: 600;
}

/* 错误信息样式 */
.error-banner {
	margin: 16px 20px;
	background: #fee2e2;
	border: 1px solid #fecaca;
	border-radius: 12px;
	padding: 12px 16px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.error-icon {
	font-size: 16px;
}

.error-text {
	color: #dc2626;
	font-size: 14px;
	font-weight: 500;
	flex: 1;
}

/* 现代选项卡样式 */
.modern-tabs {
	padding: 0 20px 16px;
}

.tab-nav {
	background: #f8fafc;
	border-radius: 16px;
	padding: 4px;
	display: flex;
	gap: 4px;
}

.tab-item {
	flex: 1;
	padding: 12px 16px;
	border-radius: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 6px;
	cursor: pointer;
	transition: all 0.3s ease;
	background: transparent;
}

.tab-item.active {
	background: white;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-icon {
	font-size: 14px;
}

.tab-text {
	color: #64748b;
	font-size: 14px;
	font-weight: 500;
}

.tab-item.active .tab-text {
	color: #1e293b;
	font-weight: 600;
}

/* 内容区域样式 */
.tab-content {
	flex: 1;
	overflow-y: auto;
	max-height: 50vh;
}

.content-section {
	padding: 0 20px 20px;
}

.section-header {
	margin-bottom: 16px;
	text-align: center;
}

.section-title {
	color: #1e293b;
	font-size: 16px;
	font-weight: 600;
	display: block;
	margin-bottom: 2px;
}

.section-subtitle {
	color: #64748b;
	font-size: 12px;
	font-weight: 400;
	display: block;
}

.records-container {
	background: #f8fafc;
	border-radius: 16px;
	padding: 16px;
	min-height: 200px;
}

/* 加载状态样式 */
.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 0;
	gap: 12px;
}

.loading-spinner {
	width: 24px;
	height: 24px;
	border: 2px solid #e2e8f0;
	border-top: 2px solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	color: #64748b;
	font-size: 14px;
	font-weight: 500;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 20px;
	gap: 8px;
}

.empty-icon {
	font-size: 32px;
	margin-bottom: 8px;
}

.empty-title {
	color: #1e293b;
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 4px;
	display: block;
}

.empty-desc {
	color: #64748b;
	font-size: 12px;
	font-weight: 400;
	text-align: center;
	display: block;
}

/* 记录列表样式 */
.records-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
	max-height: 300px;
	overflow-y: auto;
	padding-right: 4px;
}

.records-list::-webkit-scrollbar {
	width: 4px;
}

.records-list::-webkit-scrollbar-track {
	background: #e2e8f0;
	border-radius: 2px;
}

.records-list::-webkit-scrollbar-thumb {
	background: #cbd5e1;
	border-radius: 2px;
}

.records-list::-webkit-scrollbar-thumb:hover {
	background: #94a3b8;
}

/* 余额记录项样式 */
.record-item {
	background: white;
	border-radius: 12px;
	padding: 16px;
	display: flex;
	align-items: center;
	gap: 12px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.record-item:active {
	transform: scale(0.98);
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.record-icon {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
}

.record-icon.income {
	background: #dcfce7;
}

.record-icon.expense {
	background: #fee2e2;
}

.record-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.record-title {
	color: #1e293b;
	font-size: 13px;
	font-weight: 600;
}

.record-time {
	color: #64748b;
	font-size: 11px;
	font-weight: 400;
}

.record-amount {
	display: flex;
	align-items: center;
}

.amount-text {
	font-size: 14px;
	font-weight: 700;
}

.record-amount.income .amount-text {
	color: #16a34a;
}

.record-amount.expense .amount-text {
	color: #dc2626;
}

/* 提现记录样式 */
.withdraw-record {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 10px;
	padding: 10px 12px;
	margin-bottom: 6px;
	backdrop-filter: blur(10px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.withdraw-record:active {
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	border-color: rgba(255, 255, 255, 0.2);
}

.withdraw-header {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	padding-bottom: 8px;
}

.withdraw-info-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 6px;
}

.info-row {
	display: flex;
	align-items: center;
	gap: 8px;
}

.info-icon {
	font-size: 14px;
	opacity: 0.9;
	width: 20px;
	flex-shrink: 0;
}

.info-text {
	color: white;
	font-size: 13px;
	font-weight: 600;
}

.withdraw-right-section {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8px;
}

.withdraw-amount-section {
	display: flex;
	align-items: center;
}

.amount-value {
	color: #fbbf24;
	font-size: 16px;
	font-weight: 700;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.withdraw-status-badge {
	padding: 3px 8px;
	border-radius: 12px;
	font-size: 10px;
	font-weight: 600;
	text-align: center;
	min-width: 50px;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	flex-shrink: 0;
}

.withdraw-status-badge.status-pending {
	background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(245, 158, 11, 0.2));
	color: #fbbf24;
	border-color: rgba(251, 191, 36, 0.3);
}

.withdraw-status-badge.status-processing {
	background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.2));
	color: #60a5fa;
	border-color: rgba(59, 130, 246, 0.3);
}

.withdraw-status-badge.status-completed {
	background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.2));
	color: #4ade80;
	border-color: rgba(34, 197, 94, 0.3);
}

.withdraw-status-badge.status-rejected {
	background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.2));
	color: #f87171;
	border-color: rgba(239, 68, 68, 0.3);
}

.status-text {
	font-size: 10px;
	font-weight: 600;
}

.reject-reason {
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 8px 12px;
	margin-top: 8px;
	background: rgba(239, 68, 68, 0.1);
	border-radius: 8px;
	border: 1px solid rgba(239, 68, 68, 0.2);
}

.reject-icon {
	font-size: 14px;
	flex-shrink: 0;
}

.reject-label {
	color: #f87171;
	font-size: 12px;
	font-weight: 600;
	flex-shrink: 0;
}

.reject-text {
	color: #f87171;
	font-size: 12px;
	font-weight: 500;
	line-height: 1.4;
	word-wrap: break-word;
	flex: 1;
}

/* 提现表单样式 */
.withdraw-form-container {
	background: #f8fafc;
	border-radius: 16px;
	padding: 16px;
}

.form-card {
	background: white;
	border-radius: 12px;
	padding: 20px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-group {
	margin-bottom: 20px;
}

.form-group.highlight {
	background: #f0f9ff;
	border: 1px solid #e0f2fe;
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 24px;
}

.form-label-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.form-label {
	color: #374151;
	font-size: 14px;
	font-weight: 600;
	margin-bottom: 8px;
	display: block;
}

.form-note {
	color: #6b7280;
	font-size: 12px;
	font-weight: 400;
}

.amount-display-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 12px;
	padding: 16px;
	display: flex;
	align-items: center;
	gap: 4px;
	justify-content: center;
}

.amount-symbol {
	color: rgba(255, 255, 255, 0.9);
	font-size: 20px;
	font-weight: 600;
}

.amount-value {
	color: white;
	font-size: 28px;
	font-weight: 800;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.fee-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: #fef3c7;
	border: 1px solid #fde68a;
	border-radius: 8px;
	padding: 12px;
}

.fee-rate {
	color: #d97706;
	font-size: 14px;
	font-weight: 600;
}

.fee-amount {
	color: #d97706;
	font-size: 14px;
	font-weight: 700;
}

.actual-amount-card {
	background: #dcfce7;
	border: 1px solid #bbf7d0;
	border-radius: 12px;
	padding: 16px;
	display: flex;
	align-items: center;
	gap: 4px;
	justify-content: center;
}

.actual-symbol {
	color: #16a34a;
	font-size: 18px;
	font-weight: 600;
}

.actual-value {
	color: #16a34a;
	font-size: 24px;
	font-weight: 800;
}

.input-wrapper {
	position: relative;
	z-index: 5;
	pointer-events: auto;
}

.modern-input {
	width: 100%;
	padding: 12px 16px;
	border: 1px solid #d1d5db;
	border-radius: 8px;
	font-size: 14px;
	background: white;
	transition: all 0.3s ease;
	box-sizing: border-box;
	position: relative;
	z-index: 10;
	pointer-events: auto;
	user-select: text;
	-webkit-user-select: text;
	min-height: 44px;
	max-height: 44px;
	line-height: 20px;
	resize: none;
	overflow: hidden;
	font-family: inherit;
}

.modern-input:focus {
	outline: none;
	border-color: #667eea;
	box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-error {
	margin-top: 4px;
}

.input-error .error-text {
	color: #dc2626;
	font-size: 12px;
	font-weight: 500;
}

.fee-breakdown {
	background: #f8fafc;
	border-radius: 12px;
	padding: 16px;
	margin: 16px 0;
	border: 1px solid #e2e8f0;
}

.breakdown-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 0;
}

.breakdown-label {
	font-size: 14px;
	color: #64748b;
	font-weight: 500;
}

.breakdown-value {
	font-size: 14px;
	color: #1e293b;
	font-weight: 600;
}

.breakdown-item.fee .breakdown-value {
	color: #dc2626;
}

.breakdown-item.total .breakdown-label {
	color: #1e293b;
	font-weight: 600;
}

.breakdown-item.total .breakdown-value {
	color: #059669;
	font-size: 16px;
	font-weight: 700;
}

.breakdown-divider {
	height: 1px;
	background: #e2e8f0;
	margin: 8px 0;
}

.info-card {
	background: #f0fdf4;
	border: 1px solid #bbf7d0;
	border-radius: 8px;
	padding: 12px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.info-icon {
	font-size: 16px;
}

.info-text {
	color: #16a34a;
	font-size: 13px;
	font-weight: 500;
	flex: 1;
}

.form-actions {
	display: flex;
	gap: 8px;
	margin-top: 24px;
	padding: 0 4px;
}

.action-btn.secondary {
	flex: 0 0 auto;
	min-width: 70px;
	max-width: 80px;
	padding: 10px 12px;
	background: #f8fafc;
	border: 1px solid #e2e8f0;
	color: #64748b;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.secondary:active {
	background: #f1f5f9;
	transform: scale(0.98);
}

.action-btn.primary {
	flex: 1;
	max-width: 120px;
	padding: 10px 12px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border: none;
	color: white;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.action-btn.primary:active {
	background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
	transform: scale(0.98);
}

.action-btn.disabled {
	opacity: 0.5;
	pointer-events: none;
}

.btn-text {
	font-size: 13px;
	font-weight: 600;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.btn-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;
}

.btn-loading .loading-spinner {
	width: 16px;
	height: 16px;
	border: 2px solid rgba(255, 255, 255, 0.3);
	border-top: 2px solid white;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.empty-text {
	color: rgba(255, 255, 255, 0.6);
	font-size: 15px;
	font-weight: 400;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.history-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 0;
	border-bottom: 1px solid rgba(255, 255, 255, 0.08);
	transition: all 0.3s ease;
	border-radius: 8px;
	margin-bottom: 4px;
}

.history-item:hover {
	background: rgba(255, 255, 255, 0.05);
	padding-left: 8px;
	padding-right: 8px;
}

.history-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.history-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.history-type {
	color: white;
	font-size: 15px;
	font-weight: 600;
	margin-bottom: 2px;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.history-time {
	color: rgba(255, 255, 255, 0.7);
	font-size: 13px;
	font-weight: 400;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.history-amount {
	font-size: 17px;
	font-weight: 700;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	letter-spacing: 0.5px;
}

.history-amount.income {
	color: #10b981;
	text-shadow: 0 0 8px rgba(16, 185, 129, 0.3);
}

.history-amount.expense {
	color: #ef4444;
	text-shadow: 0 0 8px rgba(239, 68, 68, 0.3);
}

/* 提现记录项样式 */
.withdraw-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 0;
	border-bottom: 1px solid rgba(255, 255, 255, 0.08);
	transition: all 0.3s ease;
	border-radius: 8px;
	margin-bottom: 4px;
}

.withdraw-item:hover {
	background: rgba(255, 255, 255, 0.05);
	padding-left: 8px;
	padding-right: 8px;
}

.withdraw-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.withdraw-info {
	display: flex;
	flex-direction: column;
	gap: 6px;
	flex: 1;
}

.withdraw-type {
	color: white;
	font-size: 14px;
	font-weight: 500;
}

.withdraw-time {
	color: rgba(255, 255, 255, 0.6);
	font-size: 11px;
}

/* 原有的withdraw-details样式保留，以防其他地方使用 */
.withdraw-details {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 4px;
}

.withdraw-amount {
	color: white;
	font-size: 13px;
	font-weight: bold;
}

.withdraw-status {
	font-size: 12px;
	padding: 2px 8px;
	border-radius: 10px;
	font-weight: 500;
}

.status-pending {
	background: rgba(255, 193, 7, 0.2);
	color: #ffc107;
}

.status-processing {
	background: rgba(0, 123, 255, 0.2);
	color: #007bff;
}

.status-completed {
	background: rgba(40, 167, 69, 0.2);
	color: #28a745;
}

.status-rejected {
	background: rgba(220, 53, 69, 0.2);
	color: #dc3545;
}

.status-default {
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.8);
}

/* 提现表单样式 */
.withdraw-form-section {
	padding: 20px;
}

/* 错误信息样式 */
.error-message {
	background: rgba(255, 59, 48, 0.1);
	border: 1px solid rgba(255, 59, 48, 0.3);
	border-radius: 8px;
	padding: 12px 16px;
	margin-bottom: 16px;
}

.error-text {
	color: #ff3b30;
	font-size: 14px;
	line-height: 1.4;
}



.withdraw-form {
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.form-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.form-label {
	color: rgba(255, 255, 255, 0.9);
	font-size: 14px;
	font-weight: 500;
}

.amount-display {
	display: flex;
	align-items: baseline;
	gap: 8px;
}

.amount-text {
	color: white;
	font-size: 24px;
	font-weight: bold;
}

.amount-note {
	color: rgba(255, 255, 255, 0.6);
	font-size: 12px;
}

.fee-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 14px;
}

.actual-amount {
	color: #4ade80;
	font-size: 18px;
	font-weight: bold;
}

.form-input {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 8px;
	padding: 12px;
	color: white;
	font-size: 14px;
}

.form-input::placeholder {
	color: rgba(255, 255, 255, 0.5);
}

.wechat-note {
	background: rgba(76, 175, 80, 0.1);
	border: 1px solid rgba(76, 175, 80, 0.3);
	border-radius: 8px;
	padding: 12px;
}

.note-text {
	color: #4ade80;
	font-size: 14px;
}

.form-actions {
	display: flex;
	gap: 10px;
	margin-top: 10px;
}

.action-button {
	flex: 1;
	padding: 12px;
	border-radius: 8px;
	text-align: center;
	font-size: 16px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.cancel-btn {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	color: rgba(255, 255, 255, 0.8);
}

.cancel-btn:active {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(0.98);
}

.submit-btn {
	background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
	border: none;
	color: white;
}

.submit-btn:active {
	transform: scale(0.98);
}

.submit-btn.disabled {
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.4);
	cursor: not-allowed;
}

/* 三列布局样式 */
.withdraw-row {
	display: flex;
	align-items: center;
	margin-top: 8px;
	width: 100%;
}

.withdraw-left {
	flex: 1;
	min-width: 20px;
}

.withdraw-middle {
	flex: 2;
	display: flex;
	justify-content: center;
	padding: 0 5px;
}

.withdraw-right {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 4px;
}

/* 驳回原因样式 */
.reject-reason {
	padding: 4px 8px;
	background: rgba(255, 59, 48, 0.1);
	border: 1px solid rgba(255, 59, 48, 0.2);
	border-radius: 6px;
	max-width: 100%;
	text-align: center;
}

.reject-reason-text {
	color: #ff8a8a;
	font-size: 12px;
	line-height: 1.4;
}





/* 点数弹窗样式 */
.points-summary {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	text-align: center;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.points-label {
	font-size: 14px;
	color: #cccccc;
	display: block;
	margin-bottom: 8px;
}

.points-value {
	font-size: 24px;
	font-weight: 600;
	color: #4CAF50;
}

/* ==================== 现代化大气点数弹窗样式 ==================== */

/* 弹窗遮罩层 */
.premium-points-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(30, 41, 59, 0.9) 100%);
	backdrop-filter: blur(20px);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	animation: fadeIn 0.3s ease-out;
}

/* 弹窗主体 */
.premium-points-modal {
	background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
	border-radius: 32px;
	width: 92%;
	max-width: 480px;
	max-height: 90vh;
	overflow: hidden;
	position: relative;
	box-shadow:
		0 32px 80px rgba(0, 0, 0, 0.25),
		0 16px 40px rgba(0, 0, 0, 0.15),
		0 8px 20px rgba(0, 0, 0, 0.1),
		inset 0 1px 0 rgba(255, 255, 255, 0.9);
	border: 1px solid rgba(255, 255, 255, 0.8);
	animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 动态背景装饰 */
.modal-bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	overflow: hidden;
}

.floating-orb {
	position: absolute;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.1));
	animation: float 6s ease-in-out infinite;
}

.orb-1 {
	width: 120px;
	height: 120px;
	top: -60px;
	right: -60px;
	animation-delay: 0s;
}

.orb-2 {
	width: 80px;
	height: 80px;
	bottom: -40px;
	left: -40px;
	animation-delay: 2s;
}

.orb-3 {
	width: 60px;
	height: 60px;
	top: 50%;
	right: 20px;
	animation-delay: 4s;
}

/* 弹窗头部 */
.premium-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #6366f1 100%);
	padding: 16px 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: relative;
	overflow: hidden;
}

.premium-header::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
	animation: shimmer 3s ease-in-out infinite;
}

.header-content {
	display: flex;
	align-items: center;
	gap: 12px;
	position: relative;
	z-index: 1;
}

.header-icon-wrapper {
	position: relative;
	width: 40px;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-glow {
	position: absolute;
	width: 100%;
	height: 100%;
	background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
	border-radius: 50%;
	animation: pulse 2s ease-in-out infinite;
}

.header-icon {
	font-size: 20px;
	position: relative;
	z-index: 1;
	filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.header-text-group {
	display: flex;
	flex-direction: column;
	gap: 1px;
}

.header-title {
	font-size: 18px;
	font-weight: 800;
	color: white;
	text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
	letter-spacing: -0.5px;
}

.header-subtitle {
	font-size: 11px;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 500;
	text-transform: uppercase;
	letter-spacing: 1px;
}

.close-btn {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	position: relative;
	z-index: 2;
	backdrop-filter: blur(10px);
}

.close-btn:active {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(1.1);
}

.close-icon {
	font-size: 14px;
	color: white;
	font-weight: 300;
}

/* 点数仪表板 */
.points-dashboard {
	padding: 16px;
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.dashboard-card {
	background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
	border-radius: 16px;
	padding: 16px;
	position: relative;
	overflow: hidden;
	box-shadow:
		0 8px 32px rgba(0, 0, 0, 0.08),
		0 4px 16px rgba(0, 0, 0, 0.04),
		inset 0 1px 0 rgba(255, 255, 255, 0.9);
	border: 1px solid rgba(0, 0, 0, 0.05);
}

.main-balance::before {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: conic-gradient(from 0deg, transparent, rgba(99, 102, 241, 0.08), transparent);
	animation: rotate 8s linear infinite;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
	position: relative;
	z-index: 1;
}

.card-title {
	font-size: 16px;
	font-weight: 600;
	color: #64748b;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.balance-trend {
	width: 32px;
	height: 32px;
	background: linear-gradient(135deg, #10b981, #059669);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.trend-icon {
	font-size: 16px;
}

.balance-display {
	display: flex;
	align-items: baseline;
	gap: 4px;
	margin-bottom: 6px;
	position: relative;
	z-index: 1;
}

.balance-number {
	font-size: 28px;
	font-weight: 900;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #6366f1 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	line-height: 1;
	letter-spacing: -1px;
}

.balance-unit {
	font-size: 12px;
	font-weight: 700;
	color: #64748b;
	margin-top: 4px;
}

.balance-subtitle {
	font-size: 12px;
	color: #94a3b8;
	font-weight: 500;
	position: relative;
	z-index: 1;
}

/* 交易记录区域 */
.transaction-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	background: #f8fafc;
	border-radius: 24px 24px 0 0;
	overflow: hidden;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24px 28px 20px;
	background: white;
	border-bottom: 1px solid #f1f5f9;
}

.section-title {
	font-size: 20px;
	font-weight: 800;
	color: #1e293b;
	letter-spacing: -0.5px;
}

.record-filter {
	background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
	padding: 8px 16px;
	border-radius: 12px;
	border: 1px solid #e2e8f0;
}

.filter-text {
	font-size: 12px;
	color: #64748b;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.transaction-container {
	flex: 1;
	padding: 8px 28px 28px;
	overflow-y: auto;
	max-height: 400px;
}

/* 加载状态 */
.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	gap: 20px;
}

.loading-animation {
	display: flex;
	gap: 8px;
}

.loading-dot {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea, #764ba2);
	animation: loadingBounce 1.4s ease-in-out infinite both;
}

.dot1 { animation-delay: -0.32s; }
.dot2 { animation-delay: -0.16s; }
.dot3 { animation-delay: 0s; }

.loading-text {
	font-size: 16px;
	color: #64748b;
	font-weight: 500;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	gap: 16px;
}

.empty-illustration {
	width: 80px;
	height: 80px;
	background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8px;
}

.empty-icon {
	font-size: 36px;
	opacity: 0.6;
}

.empty-title {
	font-size: 15px;
	font-weight: 600;
	color: #1e293b;
	margin-bottom: 4px;
}

.empty-desc {
	font-size: 12px;
	color: #64748b;
	text-align: center;
	line-height: 1.5;
}

/* 交易列表 */
.transaction-list {
	display: flex;
	flex-direction: column;
	gap: 16px;
	padding: 8px 0;
}

.transaction-item {
	background: white;
	border-radius: 20px;
	padding: 20px;
	display: flex;
	align-items: flex-start;
	gap: 16px;
	box-shadow:
		0 4px 20px rgba(0, 0, 0, 0.06),
		0 2px 8px rgba(0, 0, 0, 0.04),
		inset 0 1px 0 rgba(255, 255, 255, 0.9);
	border: 1px solid #f1f5f9;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.transaction-item::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
	transition: left 0.6s ease;
}

.transaction-item:active {
	transform: translateY(-4px) scale(1.02);
	box-shadow:
		0 12px 40px rgba(0, 0, 0, 0.15),
		0 8px 20px rgba(0, 0, 0, 0.08),
		inset 0 1px 0 rgba(255, 255, 255, 0.9);
	border-color: #e2e8f0;
}

.transaction-item:active::before {
	left: 100%;
}

.transaction-icon {
	width: 52px;
	height: 52px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20px;
	flex-shrink: 0;
	position: relative;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.transaction-icon.income {
	background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
	border: 3px solid #86efac;
}

.transaction-icon.expense {
	background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
	border: 3px solid #fca5a5;
}

.icon-symbol {
	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.transaction-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.transaction-main {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	gap: 16px;
}

.transaction-title {
	font-size: 14px;
	font-weight: 600;
	color: #1e293b;
	line-height: 1.4;
	flex: 1;
}

.transaction-amount {
	font-size: 16px;
	font-weight: 700;
	flex-shrink: 0;
	line-height: 1;
}

.transaction-amount.income {
	color: #16a34a;
}

.transaction-amount.expense {
	color: #dc2626;
}

.transaction-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	gap: 12px;
}

.transaction-time {
	font-size: 12px;
	color: #64748b;
	font-weight: 500;
}

.transaction-balance {
	font-size: 12px;
	color: #64748b;
	font-weight: 500;
	background: #f8fafc;
	padding: 4px 8px;
	border-radius: 8px;
}

.transaction-app {
	font-size: 12px;
	color: #16a34a;
	font-weight: 600;
	background: linear-gradient(135deg, #dcfce7, #bbf7d0);
	padding: 4px 10px;
	border-radius: 8px;
	border: 1px solid #86efac;
	align-self: flex-start;
}

/* 动画关键帧 */
@keyframes fadeIn {
	from { opacity: 0; }
	to { opacity: 1; }
}

@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: scale(0.8) translateY(40px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

@keyframes float {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes shimmer {
	0%, 100% { transform: translateX(-100%) translateY(-100%); }
	50% { transform: translateX(-50%) translateY(-50%); }
}

@keyframes pulse {
	0%, 100% { transform: scale(1); opacity: 0.8; }
	50% { transform: scale(1.1); opacity: 1; }
}

@keyframes rotate {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes loadingBounce {
	0%, 80%, 100% { transform: scale(0); }
	40% { transform: scale(1); }
}

/* 海报弹窗样式 */
.poster-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.7);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.poster-modal {
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	border-radius: 20px;
	width: 90%;
	max-width: 400px;
	max-height: 90vh; /* 增加最大高度 */
	overflow: hidden;
	position: relative;
	display: flex;
	flex-direction: column; /* 使用flex布局 */
	box-shadow:
		0 20px 60px rgba(0, 0, 0, 0.15),
		0 8px 32px rgba(0, 0, 0, 0.1),
		inset 0 1px 0 rgba(255, 255, 255, 0.8);
	border: 1px solid rgba(255, 255, 255, 0.8);
}

.poster-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	border-bottom: 1px solid #f0f0f0;
}

.poster-modal-title {
	font-size: 18px;
	font-weight: bold;
	color: #333333;
}

.poster-modal-close {
	font-size: 24px;
	color: #999999;
	cursor: pointer;
	width: 30px;
	height: 30px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.poster-modal-content {
	padding: 20px;
	flex: 1; /* 占用剩余空间 */
	overflow-y: auto; /* 允许滚动 */
	min-height: 0; /* 确保flex子项可以收缩 */
}

.poster-loading {
	text-align: center;
	padding: 40px 20px;
}

.loading-text {
	color: #666666;
	font-size: 16px;
}

.poster-preview {
	text-align: center;
}

.poster-container {
	position: relative;
	border-radius: 16px;
	overflow: hidden; /* 确保背景图片正确显示 */
	margin-bottom: 20px;
	width: 100%;
	height: 0;
	padding-bottom: 150%; /* 2:3 宽高比，更接近原图比例 */
	max-width: 300px; /* 限制最大宽度，保持合理尺寸 */
	margin: 0 auto 20px auto; /* 居中显示 */
	box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
	display: block;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border: 2px solid rgba(255, 255, 255, 0.8);
}

.poster-background {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
	display: block;
	z-index: 1; /* 背景层 */
	border-radius: 14px !important; /* 与容器圆角保持一致，稍小一点 */
	overflow: hidden; /* 确保圆角效果 */
	/* UniApp特殊处理 */
	-webkit-border-radius: 14px;
	-moz-border-radius: 14px;
	/* 背景图片样式通过内联style设置 */
}





.qrcode-overlay {
	position: absolute;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10px);
	border-radius: 8px;
	padding: 3px; /* 减少内边距，减少白边 */
	box-shadow:
		0 4px 16px rgba(0, 0, 0, 0.1),
		0 1px 4px rgba(0, 0, 0, 0.05);
	z-index: 10; /* 在背景图片之上 */
	border: 1px solid rgba(255, 255, 255, 0.4);
	/* 其他样式通过 :style 动态设置 */
	/* 确保二维码保持正方形 */
	aspect-ratio: 1/1;
}

.qrcode-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
	border-radius: 8px;
}

.qrcode-placeholder {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background: #f8f9fa;
	border: 2px dashed #dee2e6;
	border-radius: 4px;
}

.placeholder-text {
	font-size: 12px;
	color: #6c757d;
	margin-bottom: 4px;
}

.placeholder-code {
	font-size: 10px;
	color: #495057;
	font-weight: bold;
}

.poster-error {
	text-align: center;
	padding: 40px 20px;
}

.error-icon {
	font-size: 48px;
	margin-bottom: 16px;
}

.error-text {
	font-size: 16px;
	color: #333333;
	font-weight: bold;
	margin-bottom: 8px;
	display: block;
}

.error-desc {
	font-size: 14px;
	color: #666666;
	margin-bottom: 20px;
	display: block;
}

.retry-btn {
	background: #007AFF;
	color: white;
	border: none;
	border-radius: 8px;
	padding: 12px 24px;
	font-size: 16px;
}

.invite-info {
	margin-bottom: 20px;
}

.invite-item {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
	padding: 16px;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12px;
	border: 1px solid rgba(0, 0, 0, 0.05);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.invite-label {
	font-size: 14px;
	color: #666666;
	min-width: 80px;
}

.invite-value {
	flex: 1;
	font-size: 14px;
	color: #333333;
	margin: 0 10px;
	word-break: break-all;
}

.copy-btn {
	background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
	color: #ffffff;
	padding: 8px 16px;
	border-radius: 8px;
	font-size: 12px;
	cursor: pointer;
	border: none;
	box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
	transition: all 0.3s ease;
}

.copy-btn:hover {
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

.poster-actions {
	display: flex;
	gap: 12px;
}

.action-btn {
	flex: 1;
	padding: 14px 20px;
	border-radius: 12px;
	font-size: 16px;
	font-weight: 500;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.save-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	color: white;
	margin-right: 8px;
}

.save-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
}

.share-btn {
	background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
	color: white;
	margin-left: 8px;
}

.share-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 16px rgba(111, 66, 193, 0.3);
}

.save-btn {
	background: #34C759;
	color: #ffffff;
}

.share-btn {
	background: #007AFF;
	color: #ffffff;
}

/* 现代化团队弹窗样式 */
.modern-team-modal-overlay {
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	background: rgba(0, 0, 0, 0.6) !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	z-index: 9999 !important;
	backdrop-filter: blur(20rpx) !important;
	animation: overlayFadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.modern-team-modal-card {
	background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
	border-radius: 20rpx;
	width: 92%;
	max-width: 680rpx;
	max-height: 85vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	box-shadow: 0 25rpx 50rpx rgba(0, 0, 0, 0.15), 0 0 0 1rpx rgba(255, 255, 255, 0.05);
	animation: modalSlideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes modalSlideUp {
	from {
		opacity: 0;
		transform: translateY(60rpx) scale(0.95);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

/* 头部样式 */
.modern-team-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 32rpx 28rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
}

.header-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.header-icon-wrapper {
	width: 56rpx;
	height: 56rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.header-icon {
	font-size: 28rpx;
}

.header-text {
	display: flex;
	flex-direction: column;
}

.header-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #ffffff;
	line-height: 1.2;
}

.header-subtitle {
	font-size: 20rpx;
	color: rgba(255, 255, 255, 0.7);
	margin-top: 4rpx;
	font-weight: 400;
}

.header-close {
	width: 48rpx;
	height: 48rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.header-close:active {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(0.95);
}

.close-text {
	font-size: 30rpx;
	color: #ffffff;
	font-weight: 300;
}

/* 内容区域 */
.modern-team-content {
	padding: 32rpx 28rpx;
	flex: 1;
	overflow-y: auto;
}

/* 统计区域样式 */
.stats-overview {
	margin-bottom: 40rpx;
}

.overview-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.overview-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #1a202c;
}

.total-badge {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	min-width: 60rpx;
	text-align: center;
}

.total-count {
	font-size: 22rpx;
	font-weight: 600;
	color: #ffffff;
}

.stats-cards-row {
	display: flex;
	gap: 20rpx;
}

.modern-stat-card {
	flex: 1;
	background: #ffffff;
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid #e2e8f0;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.modern-stat-card:active {
	transform: translateY(-4rpx);
	box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
}

.modern-stat-card.primary-card {
	border-left: 4rpx solid #667eea;
}

.modern-stat-card.secondary-card {
	border-left: 4rpx solid #764ba2;
}

.card-top {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.card-icon-bg {
	width: 48rpx;
	height: 48rpx;
	background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.card-icon {
	font-size: 22rpx;
}

.trend-indicator {
	width: 32rpx;
	height: 32rpx;
	background: #10b981;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.trend-arrow {
	font-size: 18rpx;
	color: #ffffff;
	font-weight: 600;
}

.card-bottom {
	display: flex;
	flex-direction: column;
}

.card-number {
	font-size: 34rpx;
	font-weight: 700;
	color: #1a202c;
	line-height: 1;
	margin-bottom: 8rpx;
}

.card-label {
	font-size: 22rpx;
	color: #64748b;
	font-weight: 500;
}

/* 成员区域样式 */
.members-overview {
	margin-top: 8rpx;
}

.members-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.members-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #1a202c;
}

.view-all {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 8rpx 16rpx;
	background: #f8fafc;
	border-radius: 12rpx;
	transition: all 0.2s ease;
}

.view-all:active {
	background: #e2e8f0;
	transform: scale(0.98);
}

.view-all-text {
	font-size: 22rpx;
	color: #667eea;
	font-weight: 500;
}

.view-all-arrow {
	font-size: 18rpx;
	color: #667eea;
	font-weight: 600;
}

/* 加载和空状态 */
.modern-loading {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 32rpx;
}

.loading-spinner {
	width: 40rpx;
	height: 40rpx;
	border: 3rpx solid #e2e8f0;
	border-top: 3rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 16rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 22rpx;
	color: #64748b;
}

.modern-empty {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 32rpx;
	text-align: center;
}

.empty-icon-bg {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.empty-icon {
	font-size: 40rpx;
}

.empty-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #1a202c;
	margin-bottom: 8rpx;
}

.empty-desc {
	font-size: 22rpx;
	color: #64748b;
}

/* 成员卡片样式 */
.modern-members-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.modern-member-item {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 20rpx;
	border: 1rpx solid #e2e8f0;
	display: flex;
	align-items: center;
	transition: all 0.2s ease;
}

.modern-member-item:active {
	background: #f8fafc;
	transform: scale(0.98);
}

.member-avatar-section {
	position: relative;
	margin-right: 20rpx;
}

.member-avatar-circle {
	width: 64rpx;
	height: 64rpx;
	border-radius: 16rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-letter {
	font-size: 22rpx;
	font-weight: 600;
	color: #ffffff;
}

.level-badge {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 32rpx;
	height: 20rpx;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2rpx solid #ffffff;
}

.level-badge.level-one {
	background: #10b981;
}

.level-badge.level-two {
	background: #f59e0b;
}

.level-text {
	font-size: 16rpx;
	font-weight: 600;
	color: #ffffff;
}

.member-info-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.member-name-text {
	font-size: 24rpx;
	font-weight: 600;
	color: #1a202c;
	line-height: 1.2;
}

.member-phone-text {
	font-size: 20rpx;
	color: #64748b;
}

.member-time-section {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.join-time-text {
	font-size: 18rpx;
	color: #94a3b8;
	font-weight: 500;
}

/* 团队详情弹窗样式 */
.team-detail-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 10000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.team-detail-modal {
	background: white;
	border-radius: 24rpx;
	width: 100%;
	max-width: 640rpx;
	max-height: 80vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 40rpx;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.modal-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
}

.modal-close {
	font-size: 48rpx;
	color: #999;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.modal-content {
	flex: 1;
	overflow-y: auto;
	padding: 32rpx 40rpx;
}

.promoter-info {
	margin-bottom: 32rpx;
	padding: 24rpx;
	background: rgba(102, 126, 234, 0.05);
	border-radius: 16rpx;
}

.info-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	display: block;
	margin-bottom: 16rpx;
}

.info-item {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.info-label {
	font-size: 24rpx;
	color: #666;
	margin-right: 16rpx;
	min-width: 80rpx;
}

.info-value {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}

.stats-detail {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.detail-card {
	border-radius: 16rpx;
	padding: 24rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.detail-card.secondary {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.detail-header {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.detail-icon {
	font-size: 32rpx;
	margin-right: 12rpx;
}

.detail-title {
	font-size: 32rpx;
	font-weight: 600;
	color: white;
}

.detail-count {
	font-size: 36rpx;
	font-weight: 700;
	color: white;
	display: block;
	margin-bottom: 16rpx;
}

.detail-list {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 12rpx;
	padding: 16rpx;
	max-height: 300rpx;
	overflow-y: auto;
}

.empty-tip {
	text-align: center;
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
	padding: 20rpx;
}

.user-item {
	display: flex;
	align-items: center;
	padding: 12rpx 16rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 8rpx;
	margin-bottom: 8rpx;
}

.user-item:last-child {
	margin-bottom: 0;
}

.user-id {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	background: rgba(255, 255, 255, 0.2);
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	margin-right: 12rpx;
	min-width: 60rpx;
	text-align: center;
}

.user-info {
	font-size: 26rpx;
	color: white;
	flex: 1;
}

.modal-footer {
	padding: 24rpx 40rpx;
	border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.modal-button {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	text-align: center;
	cursor: pointer;
}

.button-text {
	font-size: 32rpx;
	font-weight: 600;
	color: white;
}

/* 加载和空状态 */
.loading-state, .empty-state {
	text-align: center !important;
	padding: 80rpx 40rpx !important;
}

.loading-text, .empty-text {
	font-size: 28rpx !important;
	color: #999 !important;
}

/* 🌟 现代化VIP套餐弹窗样式 */
.modern-package-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(10rpx);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10000;
	animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

.modern-package-modal-container {
	background: #ffffff;
	border-radius: 48rpx;
	width: 100%;
	max-width: 960rpx;
	max-height: 85vh;
	box-shadow: 0 64rpx 128rpx rgba(0, 0, 0, 0.2);
	overflow: hidden;
	display: flex;
	flex-direction: column;
	animation: modalShow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	margin: 40rpx;
}

@keyframes modalShow {
	from {
		opacity: 0;
		transform: scale(0.9) translateY(40rpx);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.modern-package-modal-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 48rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: white;
	position: relative;
	overflow: hidden;
}

.modern-package-modal-header::before {
	content: '';
	position: absolute;
	top: -50%;
	right: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
	animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
	0%, 100% { transform: translateX(-100%) translateY(-100%); }
	50% { transform: translateX(-50%) translateY(-50%); }
}

.header-content {
	flex: 1;
	position: relative;
	z-index: 1;
}

.modal-title {
	font-size: 48rpx;
	font-weight: 700;
	color: white;
	display: block;
	margin-bottom: 8rpx;
}

.modal-subtitle {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 300;
}

.close-button {
	width: 72rpx;
	height: 72rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	border: none;
	color: white;
	position: relative;
	z-index: 2;
}

.close-button:active {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(1.1);
}

.close-icon {
	font-size: 40rpx;
	font-weight: 300;
	line-height: 1;
}

.modern-package-modal-content {
	flex: 1;
	overflow-y: auto;
	padding: 0;
}

.modern-package-modal-content::-webkit-scrollbar {
	width: 12rpx;
}

.modern-package-modal-content::-webkit-scrollbar-track {
	background: #f1f1f1;
}

.modern-package-modal-content::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 6rpx;
}

/* 加载和空状态 */
.loading-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 6rpx solid #f3f3f3;
	border-top: 6rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin: 0 auto 24rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 32rpx;
	color: #666;
	font-weight: 500;
}

.empty-state {
	text-align: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 24rpx;
	display: block;
}

.empty-text {
	font-size: 36rpx;
	color: #333;
	font-weight: 600;
	margin-bottom: 12rpx;
	display: block;
}

.empty-desc {
	font-size: 24rpx;
	color: #999;
}

/* 现代化套餐列表 */
.modern-package-list {
	padding: 48rpx;
}

/* VIP套餐卡片 */
.vip-package-card {
	background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
	border-radius: 40rpx;
	padding: 48rpx;
	margin-bottom: 48rpx;
	position: relative;
	overflow: hidden;
	box-shadow: 0 16rpx 64rpx rgba(255, 234, 167, 0.3);
}

.vip-package-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
	opacity: 0.3;
}

.vip-card-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 40rpx;
	position: relative;
	z-index: 1;
}

.package-title-section {
	flex: 1;
	margin-right: 32rpx;
}

.package-name {
	font-size: 28rpx;
	font-weight: 700;
	color: #2d3436;
	margin-bottom: 16rpx;
	display: block;
}

.package-price {
	font-size: 48rpx;
	font-weight: 800;
	color: #e17055;
	display: flex;
	align-items: baseline;
	gap: 8rpx;
}

.price-symbol {
	font-size: 32rpx;
}

.price-value {
	font-size: 48rpx;
}

.package-card:hover {
	box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.12);
	transform: translateY(-4rpx);
}

.package-card-header {
	background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
	padding: 32rpx;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	border-bottom: 1rpx solid #e2e8f0;
}

.package-info {
	flex: 1;
}

.package-title {
	font-size: 24rpx;
	font-weight: 700;
	color: #1a202c;
	margin-bottom: 16rpx;
}

.package-meta {
	display: flex;
	align-items: center;
	gap: 16rpx;
	flex-wrap: wrap;
}

.package-order {
	font-size: 24rpx;
	color: #718096;
	font-family: 'SF Mono', 'Monaco', monospace;
}

.package-status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
	font-weight: 600;
}

/* 状态标识美化 */
.status-badge {
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	font-size: 20rpx;
	font-weight: 600;
	position: relative;
	overflow: hidden;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	margin-top: 8rpx;
	display: inline-block;
	width: auto;
	min-width: auto;
}

.status-active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	animation: pulse 2s ease-in-out infinite;
}

.status-active::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
	animation: shine 3s ease-in-out infinite;
}

@keyframes pulse {
	0%, 100% {
		box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
	}
	50% {
		box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.5);
	}
}

@keyframes shine {
	0% {
		left: -100%;
	}
	100% {
		left: 100%;
	}
}

.status-expired {
	background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
	color: white;
}

.package-amount {
	text-align: right;
}

.amount-symbol {
	font-size: 24rpx;
	color: #667eea;
	font-weight: 500;
}

.amount-value {
	font-size: 40rpx;
	font-weight: 700;
	color: #667eea;
	margin-left: 4rpx;
}

.package-card-body {
	padding: 32rpx;
}

.detail-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 32rpx;
}

.detail-section {
	background: #f8fafc;
	border-radius: 16rpx;
	padding: 24rpx;
}

.section-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #4a5568;
	margin-bottom: 20rpx;
	display: block;
}

.detail-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.detail-row:last-child {
	margin-bottom: 0;
}

.detail-label {
	font-size: 22rpx;
	color: #718096;
}

.detail-value {
	font-size: 22rpx;
	color: #2d3748;
	font-weight: 600;
}

.detail-row.highlight .detail-value.remaining-days {
	color: #48bb78;
	font-weight: 700;
}

.detail-value.points {
	color: #667eea;
	font-weight: 700;
}

/* VIP卡片主体 */
.vip-card-body {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 24rpx;
	padding: 32rpx;
	position: relative;
	z-index: 1;
}

.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 24rpx;
	margin-bottom: 32rpx;
}

.info-item {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.info-label {
	font-size: 22rpx;
	color: #636e72;
	font-weight: 500;
}

.info-value {
	font-size: 24rpx;
	color: #2d3436;
	font-weight: 600;
}

.info-value.highlight {
	color: #667eea;
	font-weight: 700;
}

.info-value.remaining {
	background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
	color: white;
	padding: 8rpx 16rpx;
	border-radius: 24rpx;
	font-size: 22rpx;
	font-weight: 600;
	text-align: center;
}

.daily-limit-section {
	background: #f8f9fa;
	border-radius: 16rpx;
	padding: 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.limit-label {
	font-size: 28rpx;
	color: #636e72;
	font-weight: 500;
}

.limit-value {
	font-size: 28rpx;
	color: #2d3436;
	font-weight: 600;
}

/* 历史套餐区域 */
.history-section {
	margin-top: 48rpx;
}

.history-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #2d3436;
	margin-bottom: 32rpx;
	display: block;
}

.package-card.history {
	background: #f8f9fa;
	border-radius: 32rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.package-card.history:hover {
	border-color: #667eea;
	transform: translateY(-4rpx);
	box-shadow: 0 16rpx 64rpx rgba(102, 126, 234, 0.1);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.package-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.card-details {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.detail-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.detail-label {
	font-size: 24rpx;
	color: #636e72;
	font-weight: 500;
}

.detail-value {
	font-size: 24rpx;
	color: #2d3436;
	font-weight: 600;
}

/* 弹窗底部 */
.modern-package-modal-footer {
	background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
	padding: 32rpx 48rpx;
	border-top: 1rpx solid #e2e8f0;
	text-align: center;
}

.footer-content {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.footer-text {
	font-size: 32rpx;
	color: #667eea;
	font-weight: 600;
}

.footer-desc {
	font-size: 24rpx;
	color: #718096;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.modern-package-modal-container {
		margin: 20rpx;
		max-width: calc(100vw - 40rpx);
	}

	.modern-package-list {
		padding: 32rpx;
	}

	.info-grid {
		grid-template-columns: 1fr;
	}

	.vip-card-header {
		flex-direction: column;
		align-items: flex-start;
		gap: 24rpx;
	}

	.package-price {
		font-size: 36rpx;
	}

	.price-value {
		font-size: 36rpx;
	}
}

/* 合伙人权益弹窗样式 */
.partner-benefits-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	backdrop-filter: blur(4px);
}

.partner-benefits-modal-card {
	background: white;
	border-radius: 20px;
	width: 90%;
	max-width: 500px;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
	animation: modalSlideIn 0.3s ease-out;
}

/* VIP权益弹窗样式 */
.vip-benefits-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	backdrop-filter: blur(4px);
}

.vip-benefits-modal-card {
	background: white;
	border-radius: 20px;
	width: 90%;
	max-width: 500px;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
	animation: modalSlideIn 0.3s ease-out;
}

/* 弹窗头部 */
.benefits-modal-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20px;
	color: white;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.benefits-modal-header.vip-header {
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	color: #333;
}

.benefits-modal-title {
	font-size: 18px;
	font-weight: 600;
}

.benefits-modal-close {
	width: 30px;
	height: 30px;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.benefits-modal-close:hover {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(1.1);
}

.close-icon {
	font-size: 16px;
	font-weight: bold;
}

/* 弹窗内容 */
.benefits-modal-content {
	padding: 20px;
	max-height: 60vh;
	overflow-y: auto;
}

/* 权益区块 */
.benefits-section {
	margin-bottom: 20px;
}

.benefits-section:last-child {
	margin-bottom: 0;
}

.section-header {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 8px;
	margin-bottom: 12px;
}

.section-icon {
	font-size: 18px;
}

.section-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

/* 升级条件 */
.condition-item {
	background: #f8f9fa;
	border-radius: 12px;
	padding: 15px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-left: 4px solid #667eea;
}

.condition-text {
	font-size: 14px;
	color: #555;
}

.condition-status {
	padding: 4px 12px;
	border-radius: 20px;
	font-size: 12px;
	font-weight: 500;
	background: #e9ecef;
	color: #6c757d;
}

.condition-status.achieved {
	background: #d4edda;
	color: #155724;
}

.status-text {
	font-size: 12px;
}

/* 权益列表 */
.benefits-list {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.benefit-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 12px;
	background: #f8f9fa;
	border-radius: 10px;
	border-left: 3px solid #28a745;
}

.benefit-icon {
	font-size: 16px;
	flex-shrink: 0;
}

.benefit-text {
	font-size: 14px;
	color: #555;
	line-height: 1.4;
}

/* VIP套餐信息 */
.vip-package-info {
	background: linear-gradient(135deg, #ffd700, #ffed4e);
	border-radius: 12px;
	padding: 15px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.package-title {
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

.package-price {
	font-size: 18px;
	font-weight: bold;
	color: #d63384;
}

/* 弹窗动画 */
@keyframes modalSlideIn {
	from {
		opacity: 0;
		transform: translateY(-50px) scale(0.9);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

</style>

