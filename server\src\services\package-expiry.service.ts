import { AppDataSource } from '../data-source';
import { Member } from '../entity/Member';
import { Package } from '../entity/Package';

/**
 * 套餐到期监控服务
 * 负责检查用户套餐是否到期，清理过期套餐的未使用点数，并记录到points_records表
 */
export class PackageExpiryService {
  
  /**
   * 检查并处理所有过期套餐
   */
  static async checkAndProcessExpiredPackages(): Promise<void> {
    console.log('开始检查过期套餐...');
    
    try {
      // 查找所有有套餐且套餐已过期的用户
      const expiredMembers = await AppDataSource.query(`
        SELECT m.id, m.packageId, m.packageExpiredAt, m.points, p.title as packageTitle, p.totalQuota
        FROM members m
        LEFT JOIN package p ON m.packageId = p.id
        WHERE m.packageId IS NOT NULL 
        AND m.packageExpiredAt IS NOT NULL 
        AND m.packageExpiredAt < NOW()
        AND m.points > 0
      `);

      console.log(`找到 ${expiredMembers.length} 个过期套餐用户需要处理`);

      for (const member of expiredMembers) {
        await this.processExpiredMember(member);
      }

      console.log('过期套餐处理完成');
    } catch (error) {
      console.error('检查过期套餐失败:', error);
      throw error;
    }
  }

  /**
   * 处理单个过期用户
   */
  private static async processExpiredMember(memberData: any): Promise<void> {
    const { id: memberId, packageId, packageTitle, points, totalQuota } = memberData;

    console.log(`处理过期用户 ${memberId}, 套餐: ${packageTitle}, 当前总点数: ${points}, 套餐总点数: ${totalQuota}`);

    try {
      // 开始事务
      await AppDataSource.transaction(async (transactionalEntityManager) => {

        // 1. 计算该套餐已使用的点数
        const usedPointsResult = await transactionalEntityManager.query(`
          SELECT COALESCE(SUM(ABS(amount)), 0) as usedPoints
          FROM points_records
          WHERE memberId = ?
          AND relatedType = 'package'
          AND relatedId = ?
          AND type = 'app_usage'
          AND amount < 0
        `, [memberId, packageId]);

        const usedPoints = usedPointsResult[0]?.usedPoints || 0;

        // 2. 计算该套餐剩余的点数
        const remainingPackagePoints = Math.max(0, totalQuota - usedPoints);

        console.log(`套餐 ${packageTitle} 使用情况: 总点数=${totalQuota}, 已使用=${usedPoints}, 剩余=${remainingPackagePoints}`);

        // 3. 只扣除该套餐剩余的点数，而不是清零所有点数
        const newTotalPoints = Math.max(0, points - remainingPackagePoints);

        // 4. 更新用户点数和套餐信息
        await transactionalEntityManager.query(`
          UPDATE members
          SET points = ?, packageId = NULL, packageExpiredAt = NULL
          WHERE id = ?
        `, [newTotalPoints, memberId]);

        // 5. 记录点数扣除到points_records表（只记录实际扣除的点数）
        if (remainingPackagePoints > 0) {
          await transactionalEntityManager.query(`
            INSERT INTO points_records (
              memberId,
              type,
              amount,
              balanceBefore,
              balanceAfter,
              description,
              relatedType,
              relatedId,
              metadata,
              createdAt
            ) VALUES (?, 'package_expired', ?, ?, ?, ?, 'package', ?, ?, NOW())
          `, [
            memberId,
            -remainingPackagePoints, // 负数表示扣除，只扣除该套餐剩余的点数
            points,  // 扣除前总余额
            newTotalPoints, // 扣除后总余额
            `套餐过期清理点数：${packageTitle}（剩余${remainingPackagePoints}点）`,
            packageId,
            JSON.stringify({
              packageTitle: packageTitle,
              totalQuota: totalQuota,
              usedPoints: usedPoints,
              expiredPoints: remainingPackagePoints,
              reason: 'package_expired'
            })
          ]);
        }

        console.log(`用户 ${memberId} 过期套餐处理完成，清理点数: ${remainingPackagePoints}，保留其他来源点数: ${newTotalPoints}`);
      });

    } catch (error) {
      console.error(`处理用户 ${memberId} 过期套餐失败:`, error);
      throw error;
    }
  }

  /**
   * 获取即将过期的套餐（提前3天提醒）
   */
  static async getExpiringPackages(daysBefore: number = 3): Promise<any[]> {
    try {
      const expiringMembers = await AppDataSource.query(`
        SELECT m.id, m.packageId, m.packageExpiredAt, m.points, p.title as packageTitle
        FROM members m
        LEFT JOIN package p ON m.packageId = p.id
        WHERE m.packageId IS NOT NULL 
        AND m.packageExpiredAt IS NOT NULL 
        AND m.packageExpiredAt > NOW()
        AND m.packageExpiredAt <= DATE_ADD(NOW(), INTERVAL ? DAY)
      `, [daysBefore]);

      return expiringMembers;
    } catch (error) {
      console.error('获取即将过期套餐失败:', error);
      throw error;
    }
  }

  /**
   * 启动定时任务
   */
  static startScheduledTask(): void {
    console.log('启动套餐到期监控定时任务...');
    
    // 每小时检查一次过期套餐
    setInterval(async () => {
      try {
        await this.checkAndProcessExpiredPackages();
      } catch (error) {
        console.error('定时检查过期套餐失败:', error);
      }
    }, 60 * 60 * 1000); // 1小时 = 60 * 60 * 1000 毫秒

    // 立即执行一次
    setTimeout(async () => {
      try {
        await this.checkAndProcessExpiredPackages();
      } catch (error) {
        console.error('初始检查过期套餐失败:', error);
      }
    }, 5000); // 5秒后执行
  }
}
