<template>
	<view class="modern-packages-page">
		<!-- 动态背景系统 -->
		<view class="dynamic-background">
			<!-- 渐变背景层 -->
			<view class="gradient-layer"></view>

			<!-- 几何图形层 -->
			<view class="geometric-shapes">
				<view v-for="i in 12" :key="i" class="shape" :class="`shape-${i % 4 + 1}`" :style="getShapeStyle(i)"></view>
			</view>

			<!-- 光线效果 -->
			<view class="light-rays">
				<view v-for="i in 6" :key="i" class="ray" :style="getRayStyle(i)"></view>
			</view>

			<!-- 粒子系统 -->
			<view class="particle-system">
				<view v-for="i in 30" :key="i" class="particle" :style="getAdvancedParticleStyle(i)"></view>
			</view>

			<!-- 波浪效果 -->
			<view class="wave-effects">
				<view class="wave wave-1"></view>
				<view class="wave wave-2"></view>
				<view class="wave wave-3"></view>
			</view>
		</view>

		<!-- 主内容区域 -->
		<scroll-view
			class="main-content"
			scroll-y="true"
			refresher-enabled="true"
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
		>


			<!-- 套餐展示区域 -->
			<view class="packages-showcase">
				<!-- 加载状态 -->
				<view v-if="loading" class="loading-section">
					<view class="loading-animation">
						<view class="loading-orb">
							<view class="orb-core"></view>
							<view class="orb-ring ring-1"></view>
							<view class="orb-ring ring-2"></view>
							<view class="orb-ring ring-3"></view>
						</view>
						<text class="loading-text">正在加载精彩套餐...</text>
					</view>
				</view>

				<!-- 套餐网格 -->
				<view v-else-if="packages && packages.length > 0" class="packages-grid">
					<!-- 区域标题 -->
					<view class="section-header">
						<view class="header-content">
							<text class="section-title">精选套餐</text>
							<view class="title-decoration">
								<view class="deco-dot"></view>
								<view class="deco-line"></view>
								<view class="deco-dot"></view>
							</view>
						</view>
						<text class="section-subtitle">为您精心挑选的AI智能套餐方案</text>
					</view>

					<!-- 套餐卡片网格 -->
					<view class="cards-grid">
						<view
							v-for="(pkg, index) in packages"
							:key="pkg.id"
							class="premium-package-card"
							:class="{
								'card-featured': pkg.is_featured,
								'card-popular': pkg.is_popular,
								'card-premium': index === 0
							}"
							@tap="purchasePackage(pkg)"
						>
							<!-- 卡片光环效果 -->
							<view class="card-aura" :class="`aura-${index % 3 + 1}`"></view>



							<!-- 顶部装饰条 -->
							<view class="top-decoration">
								<view class="deco-line"></view>
								<view class="deco-gems">
									<view v-for="i in 3" :key="i" class="gem" :style="getGemStyle(i)"></view>
								</view>
								<view class="deco-line"></view>
							</view>

							<!-- 特色标签组 -->
							<view class="badge-group">
								<view v-if="pkg.is_featured" class="premium-badge">
									<view class="badge-icon">👑</view>
									<text class="badge-text">推荐</text>
									<view class="badge-sparkle">
										<view v-for="i in 4" :key="i" class="sparkle" :style="getSparkleStyle(i)"></view>
									</view>
								</view>

								<view v-if="pkg.is_popular" class="hot-badge">
									<view class="badge-flame">🔥</view>
									<text class="badge-text">热门</text>
									<view class="flame-effect"></view>
								</view>
							</view>

							<!-- 卡片主体内容 -->
							<view class="card-body">
								<!-- 顶部标题区域 -->
								<view class="header-section">
									<view class="title-container">
										<view class="title-info">
											<text class="package-title">{{ pkg.title || '未知套餐' }}</text>
											<text class="package-subtitle">{{ getPackageSubtitle(index) }}</text>
										</view>
									</view>
								</view>

								<!-- 价格展示区域 -->
								<view class="price-section">
									<view class="price-container">
										<view class="main-price">
											<text class="currency-symbol">¥</text>
											<text class="price-number">{{ pkg.price || 0 }}元</text>
										</view>
										<view class="price-info">
											<text class="daily-cost">日均 ¥{{ getDailyCost(pkg) }}元</text>
										</view>
									</view>
								</view>

								<!-- 特性展示区域 -->
								<view class="features-section">
									<view class="features-container">
										<view class="feature-item">
											<view class="feature-icon">📊</view>
											<view class="feature-content">
												<text class="feature-label">总配额</text>
												<text class="feature-value">{{ formatNumber(pkg.totalQuota) }}点</text>
											</view>
										</view>

										<view class="feature-item">
											<view class="feature-icon">⚡</view>
											<view class="feature-content">
												<text class="feature-label">日限额</text>
												<text class="feature-value">{{ pkg.dailyMaxConsumption === 0 ? '不限制' : formatNumber(pkg.dailyMaxConsumption) + '点' }}</text>
											</view>
										</view>

										<view class="feature-item">
											<view class="feature-icon">🎯</view>
											<view class="feature-content">
												<text class="feature-label">有效期</text>
												<text class="feature-value">{{ pkg.duration || 0 }}天</text>
											</view>
										</view>
									</view>
								</view>

								<!-- 购买按钮区域 -->
								<view class="purchase-section">
									<view class="purchase-button" :class="{ 'premium': pkg.is_featured }">
										<view class="button-background"></view>
										<text class="button-text">立即开通</text>
										<view class="button-icon">→</view>
									</view>
								</view>
							</view>

							<!-- 底部装饰 -->
							<view class="bottom-decoration">
								<view class="deco-wave"></view>
								<view class="deco-particles">
									<view v-for="i in 5" :key="i" class="particle" :style="getParticleStyle(i)"></view>
								</view>
							</view>

							<!-- 交互效果层 -->
							<view class="interaction-layer">
								<view class="hover-overlay"></view>
								<view class="click-ripple"></view>
								<view class="focus-ring"></view>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view v-else class="empty-state">
					<view class="empty-animation">
						<view class="empty-icon">
							<text class="icon-text">🎁</text>
							<view class="icon-rings">
								<view class="ring ring-1"></view>
								<view class="ring ring-2"></view>
								<view class="ring ring-3"></view>
							</view>
						</view>
						<view class="empty-content">
							<text class="empty-title">精彩套餐即将上线</text>
							<text class="empty-desc">我们正在为您准备更多优质的AI套餐方案</text>
						</view>
						<view class="retry-button" @tap="loadPackages">
							<text class="retry-text">刷新重试</text>
							<view class="button-ripple"></view>
						</view>
					</view>
				</view>
			</view>


		</scroll-view>

		<!-- 购买确认弹窗 -->
		<view v-if="showPurchaseModal" class="modal-overlay" @click="closePurchaseModal">
			<view class="purchase-modal" @click.stop>
				<view class="modal-header">
					<text class="modal-title">确认购买</text>
					<view class="close-button" @click="closePurchaseModal">
						<text class="close-icon">×</text>
					</view>
				</view>

				<view class="modal-content">
					<view v-if="selectedPackage" class="package-summary">
						<view class="summary-header">
							<text class="summary-title">{{ selectedPackage.title }}</text>
							<text class="summary-price">¥{{ selectedPackage.price }}</text>
						</view>
						<view class="summary-details">
							<view class="detail-row">
								<text class="detail-label">总配额：</text>
								<text class="detail-value">{{ selectedPackage.totalQuota }} 次</text>
							</view>
							<view class="detail-row">
								<text class="detail-label">日限额：</text>
								<text class="detail-value">{{ selectedPackage.dailyMaxConsumption === 0 ? '不限制' : selectedPackage.dailyMaxConsumption + ' 次' }}</text>
							</view>
							<view class="detail-row">
								<text class="detail-label">有效期：</text>
								<text class="detail-value">{{ selectedPackage.duration }} 天</text>
							</view>
						</view>
					</view>

					<view class="payment-section">
						<text class="payment-title">支付方式</text>
						<!-- 调试信息 - 需要时取消注释 -->
						<!-- <view class="debug-info" style="font-size: 24rpx; color: #999; margin-bottom: 20rpx;">
							平台: {{ platform }} | 微信环境: {{ isWechatEnv }} | 显示支付宝: {{ showAlipayOption }}
						</view> -->

						<!-- 余额支付 -->
						<view class="payment-option" @click="selectPayment('balance')" :class="{ active: selectedPayment === 'balance' }">
							<view class="option-icon balance-icon">💰</view>
							<view class="option-info">
								<text class="option-name">余额支付</text>
								<text class="option-balance">可用余额：¥{{ userInfo && userInfo.balance !== undefined ? userInfo.balance.toFixed(2) : '0.00' }}</text>
							</view>
							<view class="option-check" :class="{ active: selectedPayment === 'balance' }">
								<text class="check-icon">✓</text>
							</view>
						</view>

						<!-- 微信支付 -->
						<view class="payment-option" @click="selectPayment('wechat')" :class="{ active: selectedPayment === 'wechat' }">
							<view class="option-icon wechat-icon">
								<text class="wechat-pay-icon">微</text>
							</view>
							<view class="option-info">
								<text class="option-name">微信支付</text>
								<text class="option-desc">安全便捷的移动支付</text>
							</view>
							<view class="option-check" :class="{ active: selectedPayment === 'wechat' }">
								<text class="check-icon">✓</text>
							</view>
						</view>

						<!-- 支付宝支付 - 仅在H5浏览器环境显示 -->
						<view v-if="showAlipayOption" class="payment-option" @click="selectPayment('alipay')" :class="{ active: selectedPayment === 'alipay' }">
							<view class="option-icon alipay-icon">
								<text class="alipay-pay-icon">支</text>
							</view>
							<view class="option-info">
								<text class="option-name">支付宝</text>
								<text class="option-desc">快速安全的在线支付</text>
							</view>
							<view class="option-check" :class="{ active: selectedPayment === 'alipay' }">
								<text class="check-icon">✓</text>
							</view>
						</view>
					</view>
				</view>

				<view class="modal-footer">
					<view class="redeem-button" @click="openRedeemModal">
						<text class="button-text">兑换</text>
					</view>
					<view class="confirm-button" @click="confirmPurchase" :class="{ disabled: purchasing }">
						<text class="button-text">{{ purchasing ? '处理中...' : '确认购买' }}</text>
						<view v-if="!purchasing" class="button-shine"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 兑换码弹窗 -->
		<view v-if="showRedeemCodeModal" class="modal-overlay" @click="closeRedeemModal">
			<view class="redeem-modal" @click.stop>
				<view class="modal-header">
					<text class="modal-title">兑换码兑换</text>
					<view class="close-btn" @click="closeRedeemModal">×</view>
				</view>

				<view class="redeem-content">
					<view class="input-section">
						<text class="input-label">请输入兑换码</text>
						<input
							class="redeem-input"
							v-model="redeemCode"
							placeholder="请输入兑换码"
							:disabled="redeeming"
						/>
					</view>

					<view class="redeem-tips">
						<text class="tips-text">• 兑换码不区分大小写</text>
						<text class="tips-text">• 每个兑换码只能使用一次</text>
						<text class="tips-text">• 兑换成功后会自动激活对应套餐</text>
					</view>
				</view>

				<view class="redeem-footer">
					<view class="cancel-button" @click="closeRedeemModal">
						<text class="button-text">取消</text>
					</view>
					<view class="confirm-button" @click="handleRedeem" :class="{ disabled: redeeming || !redeemCode.trim() }">
						<text class="button-text">{{ redeeming ? '兑换中...' : '确认兑换' }}</text>
						<view v-if="!redeeming" class="button-shine"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 套餐详情弹窗 -->
		<view v-if="showPackageDetailModal" class="modal-overlay" @click="closePackageDetailModal">
			<view class="package-detail-modal" @click.stop>
				<view class="modal-header">
					<text class="modal-title">套餐激活成功</text>
					<view class="close-btn" @click="closePackageDetailModal">×</view>
				</view>

				<view class="detail-content" v-if="packageDetail">
					<view class="success-icon">
						<text class="icon">✓</text>
					</view>

					<view class="package-info">
						<text class="package-name">{{ packageDetail.title }}</text>
						<text class="package-desc">{{ packageDetail.description }}</text>
					</view>

					<view class="detail-list">
						<view class="detail-item">
							<text class="detail-label">开通时间</text>
							<text class="detail-value">{{ formatDateTime(packageDetail.activatedAt) }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">到期时间</text>
							<text class="detail-value">{{ formatDateTime(packageDetail.expiredAt) }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">有效期</text>
							<text class="detail-value">{{ packageDetail.duration }} 天</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">获得点数</text>
							<text class="detail-value">+{{ packageDetail.totalQuota }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">当前余额</text>
							<text class="detail-value">¥{{ newBalance }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">当前点数</text>
							<text class="detail-value">{{ newPoints }}</text>
						</view>
					</view>
				</view>

				<view class="detail-footer">
					<view class="confirm-button" @click="closePackageDetailModal">
						<text class="button-text">确定</text>
						<view class="button-shine"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部导航 -->
		<magic-navigation
			:current="1"
			:items="navItems"
			@change="onNavChange"
		/>

		<!-- 智能登录弹窗 -->
		<smart-login-modal
			:visible="showLoginModalFlag"
			@close="hideLoginModal"
			@login-success="handleLoginSuccess"
		></smart-login-modal>
	</view>
</template>

<script>
import MagicNavigation from '../../components/MagicNavigation.vue'
import SmartLoginModal from '../../components/smart-login-modal.vue'

export default {
	components: {
		MagicNavigation,
		SmartLoginModal
	},
	data() {
		return {
			loading: true,
			refreshing: false,
			packages: [],
			userInfo: null,
			showPurchaseModal: false,
			selectedPackage: null,
			selectedPayment: 'balance',
			purchasing: false,
			showRedeemCodeModal: false,
			redeemCode: '',
			redeeming: false,
			showPackageDetailModal: false,
			packageDetail: null,
			newBalance: 0,
			newPoints: 0,
			currentCardIndex: 0, // 当前选中的卡片索引
			platform: '', // 当前平台
			isWechatEnv: false, // 是否在微信环境
			showLoginModalFlag: false, // 登录弹窗显示状态
			navItems: [
				{ text: '初页', icon: 'icon-home', path: '/pages/index/index' },
				{ text: '会享', icon: 'icon-message', path: '/pages/chat/index' },
				{ text: '往档', icon: 'icon-history', path: '/pages/history/index' },
				{ text: '吾界', icon: 'icon-user', path: '/pages/profile/index' }
			]
		}
	},
	onLoad() {
		this.detectPlatform()
		this.initPage()
	},
	onShow() {
		this.loadUserInfo()
	},
	computed: {
		// 是否显示支付宝支付选项
		showAlipayOption() {
			// 在以下情况显示支付宝：
			// 1. H5浏览器环境且不是微信浏览器
			// 2. 不是微信小程序环境
			// #ifdef H5
			return !this.isWechatEnv
			// #endif

			// #ifdef MP-WEIXIN
			return false
			// #endif

			// #ifdef APP-PLUS
			return true
			// #endif

			// 默认显示（其他平台）
			return true
		}
	},
	methods: {
		// 检测当前平台
		detectPlatform() {
			// 获取系统信息
			const systemInfo = uni.getSystemInfoSync()
			this.platform = systemInfo.platform

			// 检测是否在微信环境
			// #ifdef H5
			const ua = navigator.userAgent.toLowerCase()
			this.isWechatEnv = ua.includes('micromessenger')
			console.log('H5环境检测:', { ua, isWechatEnv: this.isWechatEnv })
			// #endif

			// #ifdef MP-WEIXIN
			this.isWechatEnv = true
			console.log('微信小程序环境')
			// #endif

			// #ifdef APP-PLUS
			this.isWechatEnv = false
			console.log('APP环境')
			// #endif

			console.log('平台检测结果:', {
				platform: this.platform,
				isWechatEnv: this.isWechatEnv,
				showAlipayOption: this.showAlipayOption
			})
		},
		async initPage() {
			try {
				await Promise.all([
					this.loadPackages(),
					this.loadUserInfo()
				])
			} catch (error) {
				console.error('页面初始化失败:', error)
				uni.showToast({
					title: '加载失败，请重试',
					icon: 'none'
				})
			}
		},

		async loadPackages() {
			try {
				this.loading = true

				const apiUrl = `${getApp().globalData.API_BASE_URL}/api/packages`;
				console.log('正在请求套餐API:', apiUrl);

				const response = await uni.request({
					url: apiUrl,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token') || ''}`,
						'Content-Type': 'application/json'
					}
				})

				console.log('套餐API响应:', response);

				if (response.statusCode === 200 && response.data && response.data.code === 200) {
					// 后端返回的数据结构是 { code: 200, data: { list: [...] } }
					const packageList = response.data.data.list || []
					console.log('解析到的套餐列表:', packageList);

					// 转换后端数据格式到前端需要的格式
					this.packages = packageList.map(pkg => {
						// 处理标签和特性
						const tags = pkg.tags ? pkg.tags.split(',').map(tag => tag.trim()) : []
						const features = []

						// 根据套餐信息生成特性列表
						if (pkg.dailyMaxConsumption) {
							features.push(`每日${pkg.dailyMaxConsumption}次调用`)
						}
						if (pkg.totalQuota) {
							features.push(`总配额${pkg.totalQuota.toLocaleString()}次`)
						}
						if (pkg.duration) {
							features.push(`有效期${pkg.duration}天`)
						}

						// 添加标签作为特性
						tags.forEach(tag => {
							if (!features.includes(tag)) {
								features.push(tag)
							}
						})

						// 检查是否为推荐套餐 - 优先使用recommended字段，其次检查tags
						const isRecommended = pkg.recommended === true || tags.includes('推荐') || tags.includes('热门')
						const isPopular = tags.includes('热门')

						return {
							id: pkg.id,
							name: pkg.title, // 后端字段是title，前端使用name
							title: pkg.title,
							price: parseFloat(pkg.price),
							duration: pkg.duration,
							total_quota: pkg.totalQuota,
							daily_quota: pkg.dailyMaxConsumption,
							totalQuota: pkg.totalQuota,
							dailyMaxConsumption: pkg.dailyMaxConsumption,
							description: pkg.description,
							features: features,
							is_featured: isRecommended,
							is_popular: isPopular,
							status: pkg.enabled ? 'active' : 'inactive',
							enabled: pkg.enabled,
							frontendDisplay: pkg.frontendDisplay,
							tags: tags,
							order: pkg.order || 0
						}
					})

					// 排序：推荐的在前面，然后按order排序
					this.packages.sort((a, b) => {
						// 推荐的排在前面
						if (a.is_featured && !b.is_featured) return -1
						if (!a.is_featured && b.is_featured) return 1
						// 都是推荐或都不是推荐时，按order排序
						return a.order - b.order
					})

					console.log('成功加载套餐数据:', this.packages)

				} else {
					console.error('API响应异常:', {
						statusCode: response.statusCode,
						data: response.data
					});
					throw new Error(response.data?.message || `API响应异常: ${response.statusCode}`)
				}

			} catch (error) {
				console.error('加载套餐失败:', error)
				console.error('错误详情:', {
					message: error.message,
					stack: error.stack,
					response: error.response
				});

				// API失败时使用模拟数据作为后备
				this.packages = [
					{
						id: 1,
						name: '基础套餐',
						price: 29.9,
						duration: 30,
						total_quota: 1000,
						daily_quota: 50,
						description: '适合轻度使用的用户',
						features: ['每日50次调用', '基础AI模型', '7x24小时支持'],
						is_featured: false,
						status: 'active'
					},
					{
						id: 2,
						name: '专业套餐',
						price: 99.9,
						duration: 30,
						total_quota: 5000,
						daily_quota: 200,
						description: '适合专业用户和小团队',
						features: ['每日200次调用', '高级AI模型', '优先技术支持', '数据分析报告'],
						is_featured: true,
						status: 'active'
					}
				]

				uni.showToast({
					title: '网络异常，已加载演示数据',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		async loadUserInfo() {
			try {
				const token = uni.getStorageSync('token')
				if (!token) {
					// 如果没有token，使用模拟用户数据
					this.userInfo = {
						id: 1,
						username: '演示用户',
						email: '<EMAIL>',
						balance: 128.50,
						total_quota: 5000,
						used_quota: 1250,
						remaining_quota: 3750,
						membership_level: '专业版',
						expire_time: '2024-08-15'
					}
					return
				}

				try {
					const response = await uni.request({
						url: `${getApp().globalData.API_BASE_URL}/api/members/me`,
						method: 'GET',
						header: {
							'Authorization': `Bearer ${token}`
						}
					})

					console.log('API响应:', response.data)
					if (response.data && response.data.success) {
						this.userInfo = response.data.data
						console.log('获取到的用户信息:', this.userInfo)
						console.log('用户余额:', this.userInfo.balance)
					} else if (response.data && response.data.code === 200) {
						this.userInfo = response.data.data
						console.log('获取到的用户信息:', this.userInfo)
						console.log('用户余额:', this.userInfo.balance)
					}
				} catch (apiError) {
					console.log('API请求失败，使用模拟用户数据:', apiError)
					// API失败时也使用模拟数据
					this.userInfo = {
						id: 1,
						username: '演示用户',
						email: '<EMAIL>',
						balance: 128.50,
						total_quota: 5000,
						used_quota: 1250,
						remaining_quota: 3750,
						membership_level: '专业版',
						expire_time: '2024-08-15'
					}
				}
			} catch (error) {
				console.error('加载用户信息失败:', error)
			}
		},

		async onRefresh() {
			this.refreshing = true
			try {
				await this.loadPackages()
				await this.loadUserInfo()
				uni.showToast({
					title: '刷新成功',
					icon: 'success'
				})
			} catch (error) {
				uni.showToast({
					title: '刷新失败',
					icon: 'none'
				})
			} finally {
				this.refreshing = false
			}
		},

		selectPackage(pkg) {
			console.log('选择套餐:', pkg)
		},

		async purchasePackage(pkg) {
			const token = uni.getStorageSync('token')
			if (!token) {
				// 显示登录弹窗
				this.showLoginModalFlag = true
				return
			}

			this.selectedPackage = pkg
			this.showPurchaseModal = true
			console.log('打开购买弹窗，用户信息:', this.userInfo)
			console.log('用户余额:', this.userInfo?.balance)

			// 如果用户信息为空，尝试重新加载
			if (!this.userInfo) {
				await this.loadUserInfo()
			}
		},

		closePurchaseModal() {
			this.showPurchaseModal = false
			this.selectedPackage = null
			// 重置为默认支付方式，如果支付宝不可用且当前选择的是支付宝，则重置为余额支付
			if (this.selectedPayment === 'alipay' && !this.showAlipayOption) {
				this.selectedPayment = 'balance'
			} else {
				this.selectedPayment = 'balance'
			}
		},

		selectPayment(type) {
			// 检查支付宝是否可用
			if (type === 'alipay' && !this.showAlipayOption) {
				uni.showToast({
					title: '当前环境不支持支付宝支付',
					icon: 'none',
					duration: 2000
				})
				return
			}
			this.selectedPayment = type
		},

		// 打开兑换码弹窗
		openRedeemModal() {
			this.showRedeemCodeModal = true
			this.redeemCode = ''
		},

		// 关闭兑换码弹窗
		closeRedeemModal() {
			this.showRedeemCodeModal = false
			this.redeemCode = ''
			this.redeeming = false
		},

		// 打开兑换码弹窗
		openRedeemModal() {
			this.showRedeemCodeModal = true
			this.redeemCode = ''
		},

		// 关闭兑换码弹窗
		closeRedeemModal() {
			this.showRedeemCodeModal = false
			this.redeemCode = ''
			this.redeeming = false
		},

		closePackageDetailModal() {
			this.showPackageDetailModal = false
			this.packageDetail = null
			this.newBalance = 0
			this.newPoints = 0
		},

		showPackageDetail(packageInfo, balance, points) {
			this.packageDetail = packageInfo
			this.newBalance = balance
			this.newPoints = points
			this.showPackageDetailModal = true
		},

		formatDateTime(dateTime) {
			if (!dateTime) return ''
			const date = new Date(dateTime)
			return date.toLocaleString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit'
			})
		},

		async confirmPurchase() {
			if (!this.selectedPackage || this.purchasing) return

			try {
				this.purchasing = true

				// 检查支付宝是否可用
				if (this.selectedPayment === 'alipay' && !this.showAlipayOption) {
					uni.showToast({
						title: '当前环境不支持支付宝支付，请选择其他支付方式',
						icon: 'none',
						duration: 2000
					})
					this.purchasing = false
					return
				}

				// 检查余额支付
				if (this.selectedPayment === 'balance') {
					const userBalance = this.userInfo?.balance || 0
					if (userBalance < this.selectedPackage.price) {
						uni.showToast({
							title: '余额不足，请选择其他支付方式',
							icon: 'none',
							duration: 2000
						})
						this.purchasing = false
						return
					}
				}

				// 处理不同支付方式
				if (this.selectedPayment === 'wechat') {
					await this.handleWechatPay()
				} else if (this.selectedPayment === 'alipay') {
					await this.handleAlipayPay()
				} else if (this.selectedPayment === 'balance') {
					await this.handleBalancePay()
				}

			} catch (error) {
				console.error('购买失败:', error)
				uni.showToast({
					title: error.message || '购买失败',
					icon: 'none'
				})
			} finally {
				this.purchasing = false
			}
		},

		// 余额支付
		async handleBalancePay() {
			const response = await uni.request({
				url: `${getApp().globalData.API_BASE_URL}/api/packages/purchase`,
				method: 'POST',
				header: {
					'Authorization': `Bearer ${uni.getStorageSync('token')}`,
					'Content-Type': 'application/json'
				},
				data: {
					packageId: this.selectedPackage.id,
					paymentMethod: 'balance'
				}
			})

			if (response.data && response.data.code === 200) {
				uni.showToast({
					title: '购买成功',
					icon: 'success'
				})
				this.closePurchaseModal()

				// 显示套餐详情弹窗
				if (response.data.data && response.data.data.packageInfo) {
					this.showPackageDetail(
						response.data.data.packageInfo,
						response.data.data.newBalance,
						response.data.data.newPoints
					)
				}

				await this.loadUserInfo() // 重新加载用户信息
			} else {
				throw new Error(response.data?.message || '余额支付失败')
			}
		},

		// 处理兑换码兑换
		async handleRedeem() {
			if (!this.redeemCode.trim()) {
				uni.showToast({
					title: '请输入兑换码',
					icon: 'none'
				})
				return
			}

			try {
				this.redeeming = true

				const response = await uni.request({
					url: `${getApp().globalData.API_BASE_URL}/api/redemption-codes/use`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`,
						'Content-Type': 'application/json'
					},
					data: {
						code: this.redeemCode.trim()
					}
				})

				if (response.data && response.data.code === 200) {
					const packageInfo = response.data.data.package
					uni.showToast({
						title: `兑换成功！获得${packageInfo.title}`,
						icon: 'success',
						duration: 3000
					})

					// 关闭弹窗
					this.closeRedeemModal()
					this.closePurchaseModal()

					// 显示套餐详情弹窗
					if (response.data.data && response.data.data.packageInfo) {
						this.showPackageDetail(
							response.data.data.packageInfo,
							response.data.data.newBalance,
							response.data.data.newPoints
						)
					}

					// 重新加载用户信息和套餐列表
					await this.loadUserInfo()
					await this.loadPackages()
				} else {
					throw new Error(response.data?.message || '兑换失败')
				}
			} catch (error) {
				console.error('兑换失败:', error)
				uni.showToast({
					title: error.message || '兑换失败，请检查兑换码是否正确',
					icon: 'none',
					duration: 2000
				})
			} finally {
				this.redeeming = false
			}
		},

		// 微信支付
		async handleWechatPay() {
			try {
				// 获取微信支付配置
				const configResponse = await uni.request({
					url: `${getApp().globalData.API_BASE_URL}/api/payment/config/wechat-pay`,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`
					}
				})

				if (!configResponse.data || configResponse.data.code !== 200) {
					throw new Error('获取微信支付配置失败')
				}

				const config = configResponse.data.data
				if (!config.appId || !config.mchId || !config.apiKey) {
					throw new Error('微信支付配置不完整，请联系管理员')
				}

				// 创建支付订单
				const orderResponse = await uni.request({
					url: `${getApp().globalData.API_BASE_URL}/api/payment/create-order`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`,
						'Content-Type': 'application/json'
					},
					data: {
						packageId: this.selectedPackage.id,
						paymentMethod: 'wechat',
						amount: this.selectedPackage.price
					}
				})

				if (orderResponse.data && orderResponse.data.code === 200) {
					const paymentData = orderResponse.data.data

					// 调用微信支付
					uni.requestPayment({
						provider: 'wxpay',
						timeStamp: paymentData.timeStamp,
						nonceStr: paymentData.nonceStr,
						package: paymentData.package,
						signType: paymentData.signType,
						paySign: paymentData.paySign,
						success: () => {
							// 支付成功后通知后端
							this.handlePaymentSuccess(paymentData.orderId)
						},
						fail: (err) => {
							console.error('微信支付失败:', err)
							uni.showToast({
								title: '支付取消或失败',
								icon: 'none'
							})
						}
					})
				} else {
					throw new Error(orderResponse.data?.message || '创建微信支付订单失败')
				}
			} catch (error) {
				console.error('微信支付处理失败:', error)
				uni.showToast({
					title: error.message || '微信支付失败',
					icon: 'none'
				})
			}
		},

		// 支付宝支付
		async handleAlipayPay() {
			try {
				// 获取支付宝配置
				const configResponse = await uni.request({
					url: `${getApp().globalData.API_BASE_URL}/api/payment/config/alipay`,
					method: 'GET',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`
					}
				})

				if (!configResponse.data || configResponse.data.code !== 200) {
					throw new Error('获取支付宝配置失败')
				}

				const config = configResponse.data.data
				if (!config.appId || !config.privateKey) {
					throw new Error('支付宝配置不完整，请联系管理员')
				}

				// 创建支付订单
				const orderResponse = await uni.request({
					url: `${getApp().globalData.API_BASE_URL}/api/payment/create-order`,
					method: 'POST',
					header: {
						'Authorization': `Bearer ${uni.getStorageSync('token')}`,
						'Content-Type': 'application/json'
					},
					data: {
						packageId: this.selectedPackage.id,
						paymentMethod: 'alipay',
						amount: this.selectedPackage.price
					}
				})

				if (orderResponse.data && orderResponse.data.code === 200) {
					const paymentData = orderResponse.data.data

					// 调用支付宝支付
					uni.requestPayment({
						provider: 'alipay',
						orderInfo: paymentData.orderInfo,
						success: () => {
							// 支付成功后通知后端
							this.handlePaymentSuccess(paymentData.orderId)
						},
						fail: (err) => {
							console.error('支付宝支付失败:', err)
							uni.showToast({
								title: '支付取消或失败',
								icon: 'none'
							})
						}
					})
				} else {
					throw new Error(orderResponse.data?.message || '创建支付宝支付订单失败')
				}
			} catch (error) {
				console.error('支付宝支付处理失败:', error)
				uni.showToast({
					title: error.message || '支付宝支付失败',
					icon: 'none'
				})
			}
		},

		// 处理支付成功
		async handlePaymentSuccess(orderId) {
			try {
				// 通知后端支付成功
				const response = await uni.request({
					url: `${getApp().globalData.API_BASE_URL}/api/payment/callback`,
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					},
					data: {
						orderId: orderId,
						paymentResult: 'success'
					}
				})

				if (response.data && response.data.code === 200) {
					uni.showToast({
						title: '支付成功',
						icon: 'success'
					})
					this.closePurchaseModal()
					await this.loadUserInfo() // 重新加载用户信息
				} else {
					throw new Error(response.data?.message || '支付确认失败')
				}
			} catch (error) {
				console.error('支付成功处理失败:', error)
				uni.showToast({
					title: '支付成功，但处理异常，请联系客服',
					icon: 'none',
					duration: 3000
				})
			}
		},

		onNavChange(index) {
			const item = this.navItems[index]
			if (item && item.path) {
				uni.navigateTo({
					url: item.path
				})
			}
		},

		// 工具方法
		getUserInitial() {
			const username = this.userInfo?.username || '用户'
			return username.charAt(0).toUpperCase()
		},

		getUserLevel() {
			if (!this.userInfo?.membershipInfo) return '普通用户'
			const quota = this.userInfo.membershipInfo.remainingQuota || 0
			if (quota > 1000) return 'VIP用户'
			if (quota > 500) return '高级用户'
			return '标准用户'
		},

		// 3D卡片相关方法
		onCardChange(e) {
			this.currentCardIndex = e.detail.current
		},

		getPackageCharacter(index) {
			const characters = ['🎮', '🎯', '🚀', '⭐', '💎', '🔥']
			return characters[index % characters.length]
		},

		getCharacterStyle(index) {
			const colors = [
				'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
				'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
				'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
				'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
				'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
			]
			return {
				background: colors[index % colors.length]
			}
		},

		formatPeriod(pkg) {
			const start = new Date().toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
			const end = new Date(Date.now() + (pkg.duration || 30) * 24 * 60 * 60 * 1000)
				.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
			return `${start}-${end}`
		},

		getFloatStyle(index) {
			const positions = [
				{ top: '10%', left: '15%', animationDelay: '0s' },
				{ top: '20%', right: '20%', animationDelay: '0.5s' },
				{ top: '40%', left: '10%', animationDelay: '1s' },
				{ top: '60%', right: '15%', animationDelay: '1.5s' },
				{ top: '80%', left: '20%', animationDelay: '2s' },
				{ top: '30%', right: '10%', animationDelay: '2.5s' },
				{ top: '70%', left: '25%', animationDelay: '3s' },
				{ top: '50%', right: '25%', animationDelay: '3.5s' }
			]
			return positions[index % positions.length]
		},

		purchaseCurrentPackage() {
			if (this.packages && this.packages[this.currentCardIndex]) {
				this.purchasePackage(this.packages[this.currentCardIndex])
			}
		},

		// 粒子样式
		getParticleStyle(index) {
			const size = Math.random() * 4 + 2
			const left = Math.random() * 100
			const top = Math.random() * 100
			const duration = Math.random() * 10 + 5
			const delay = Math.random() * 5

			return {
				width: `${size}px`,
				height: `${size}px`,
				left: `${left}%`,
				top: `${top}%`,
				animationDuration: `${duration}s`,
				animationDelay: `${delay}s`
			}
		},

		formatExpiry(date) {
			if (!date) return '未知'
			try {
				const expiry = new Date(date)
				const now = new Date()
				const diffTime = expiry - now
				const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

				if (diffDays < 0) return '已过期'
				if (diffDays === 0) return '今天到期'
				if (diffDays === 1) return '明天到期'
				return `${diffDays}天后到期`
			} catch (error) {
				return '未知'
			}
		},

		isFeaturePackage(pkg) {
			return pkg.featured || pkg.recommended
		},

		isPopularPackage(pkg) {
			return pkg.popular || pkg.hot
		},

		getPackageIcon(index) {
			const icons = ['🚀', '⭐', '💎', '🔥', '✨']
			return icons[index % icons.length]
		},

		getDailyCost(pkg) {
			if (!pkg.price || !pkg.duration) return '0'
			return (pkg.price / pkg.duration).toFixed(1)
		},

		formatNumber(num) {
			if (!num) return 0;
			// 如果数字大于等于10000，显示为万
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + '万';
			}
			return num;
		},

		// 格式化数字显示
		formatNumber(num) {
			if (!num) return '0'
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + '万'
			} else if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k'
			}
			return num.toString()
		},

		getParticleStyle(index) {
			const size = Math.random() * 4 + 2
			const duration = Math.random() * 20 + 10
			const delay = Math.random() * 5
			const x = Math.random() * 100
			const y = Math.random() * 100

			return {
				width: `${size}px`,
				height: `${size}px`,
				left: `${x}%`,
				top: `${y}%`,
				animationDuration: `${duration}s`,
				animationDelay: `${delay}s`
			}
		},

		// 新设计相关方法
		getShapeStyle(index) {
			const size = Math.random() * 60 + 20
			const x = Math.random() * 100
			const y = Math.random() * 100
			const duration = Math.random() * 20 + 15
			const delay = Math.random() * 10

			return {
				width: `${size}px`,
				height: `${size}px`,
				left: `${x}%`,
				top: `${y}%`,
				animationDuration: `${duration}s`,
				animationDelay: `${delay}s`
			}
		},

		getRayStyle(index) {
			const angle = (index * 60) + Math.random() * 30
			const length = Math.random() * 200 + 100
			const delay = Math.random() * 5

			return {
				transform: `rotate(${angle}deg)`,
				width: `${length}px`,
				animationDelay: `${delay}s`
			}
		},

		getAdvancedParticleStyle(index) {
			const size = Math.random() * 6 + 2
			const x = Math.random() * 100
			const y = Math.random() * 100
			const duration = Math.random() * 25 + 10
			const delay = Math.random() * 8
			const opacity = Math.random() * 0.8 + 0.2

			return {
				width: `${size}px`,
				height: `${size}px`,
				left: `${x}%`,
				top: `${y}%`,
				opacity: opacity,
				animationDuration: `${duration}s`,
				animationDelay: `${delay}s`
			}
		},



		getCardGradient(index) {
			const gradients = [
				'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
				'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
				'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
				'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
				'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
				'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
				'linear-gradient(135deg, #ff8a80 0%, #ea6100 100%)'
			]
			return {
				background: gradients[index % gradients.length]
			}
		},

		getPackageIconStyle(index) {
			const colors = [
				'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
				'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
				'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
				'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
				'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
				'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
			]
			return {
				background: colors[index % colors.length]
			}
		},

		getPackageIcon(index) {
			const icons = ['🚀', '⭐', '💎', '🔥', '✨', '🎯', '💫', '🌟']
			return icons[index % icons.length]
		},

		getDailyCost(pkg) {
			if (!pkg.price || !pkg.duration) return '0'
			return (pkg.price / pkg.duration).toFixed(1)
		},

		// 格式化数字显示
		formatNumber(num) {
			if (!num) return '0'
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + '万'
			} else if (num >= 1000) {
				return (num / 1000).toFixed(1) + 'k'
			}
			return num.toString()
		},

		getDotStyle(index) {
			const x = Math.random() * 100
			const y = Math.random() * 100
			const delay = Math.random() * 3

			return {
				left: `${x}%`,
				top: `${y}%`,
				animationDelay: `${delay}s`
			}
		},

		// 高级卡片效果方法
		getAdvancedGradient(index) {
			const gradients = [
				'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%)',
				'linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #f093fb 100%)',
				'linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #4facfe 100%)',
				'linear-gradient(135deg, #43e97b 0%, #38f9d7 50%, #43e97b 100%)',
				'linear-gradient(135deg, #fa709a 0%, #fee140 50%, #fa709a 100%)',
				'linear-gradient(135deg, #a8edea 0%, #fed6e3 50%, #a8edea 100%)'
			]
			return {
				background: gradients[index % gradients.length],
				backgroundSize: '200% 200%'
			}
		},

		getGemStyle(index) {
			const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1']
			const delay = index * 0.5

			return {
				backgroundColor: colors[index % colors.length],
				animationDelay: `${delay}s`
			}
		},

		getSparkleStyle(index) {
			const angle = index * 90
			const distance = 20 + Math.random() * 10
			const x = Math.cos(angle * Math.PI / 180) * distance
			const y = Math.sin(angle * Math.PI / 180) * distance
			const delay = index * 0.3

			return {
				transform: `translate(${x}px, ${y}px)`,
				animationDelay: `${delay}s`
			}
		},

		// ========== 登录弹窗相关方法 ==========

		// 显示登录弹窗
		showLoginModal() {
			this.showLoginModalFlag = true
		},

		// 隐藏登录弹窗
		hideLoginModal() {
			this.showLoginModalFlag = false
		},

		// 登录成功处理
		async handleLoginSuccess() {
			this.hideLoginModal()
			// 重新加载用户信息
			await this.loadUserInfo()
			uni.showToast({
				title: '登录成功',
				icon: 'success'
			})
		},

		getIconBackdrop(index) {
			const colors = [
				'radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%)',
				'radial-gradient(circle, rgba(240, 147, 251, 0.3) 0%, transparent 70%)',
				'radial-gradient(circle, rgba(79, 172, 254, 0.3) 0%, transparent 70%)',
				'radial-gradient(circle, rgba(67, 233, 123, 0.3) 0%, transparent 70%)',
				'radial-gradient(circle, rgba(250, 112, 154, 0.3) 0%, transparent 70%)',
				'radial-gradient(circle, rgba(168, 237, 234, 0.3) 0%, transparent 70%)'
			]
			return {
				background: colors[index % colors.length]
			}
		},

		getPackageSubtitle(index) {
			const subtitles = [
				'专业级AI体验',
				'热门推荐选择',
				'经济实惠方案',
				'企业级服务',
				'高级功能套餐',
				'标准版服务'
			]
			return subtitles[index % subtitles.length]
		},

		getParticleStyle(index) {
			const x = Math.random() * 100
			const y = Math.random() * 100
			const size = 2 + Math.random() * 4
			const delay = Math.random() * 2

			return {
				left: `${x}%`,
				top: `${y}%`,
				width: `${size}px`,
				height: `${size}px`,
				animationDelay: `${delay}s`
			}
		}
	}
}
</script>

<style scoped>
.modern-packages-page {
	position: relative;
	min-height: 100vh;
	overflow: hidden;
}

/* 动态背景系统 */
.dynamic-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 0;
}

/* 渐变背景层 */
.gradient-layer {
	position: absolute;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg,
		#0a0a0a 0%,
		#1a1a2e 15%,
		#16213e 30%,
		#0f3460 45%,
		#533483 60%,
		#2d1b69 75%,
		#11998e 90%,
		#38ef7d 100%);
	background-size: 400% 400%;
	animation: gradientFlow 20s ease infinite;
}

@keyframes gradientFlow {
	0%, 100% { background-position: 0% 50%; }
	25% { background-position: 100% 50%; }
	50% { background-position: 100% 100%; }
	75% { background-position: 0% 100%; }
}

/* 几何图形层 */
.geometric-shapes {
	position: absolute;
	width: 100%;
	height: 100%;
}

.shape {
	position: absolute;
	border-radius: 50%;
	opacity: 0.1;
	animation: shapeFloat infinite ease-in-out;
}

.shape-1 {
	background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
	border-radius: 30%;
}

.shape-2 {
	background: linear-gradient(45deg, #667eea, #764ba2);
	border-radius: 20%;
}

.shape-3 {
	background: linear-gradient(45deg, #f093fb, #f5576c);
	clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}

.shape-4 {
	background: linear-gradient(45deg, #4facfe, #00f2fe);
	clip-path: polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%);
}

@keyframes shapeFloat {
	0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
	33% { transform: translateY(-30px) rotate(120deg) scale(1.1); }
	66% { transform: translateY(15px) rotate(240deg) scale(0.9); }
}

/* 光线效果 */
.light-rays {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.ray {
	position: absolute;
	top: 50%;
	left: 50%;
	height: 2px;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
	transform-origin: left center;
	animation: rayRotate 15s linear infinite;
}

@keyframes rayRotate {
	0% { transform: rotate(0deg) scaleX(0); opacity: 0; }
	10% { opacity: 1; }
	50% { transform: rotate(180deg) scaleX(1); opacity: 0.8; }
	90% { opacity: 1; }
	100% { transform: rotate(360deg) scaleX(0); opacity: 0; }
}

/* 粒子系统 */
.particle-system {
	position: absolute;
	width: 100%;
	height: 100%;
}

.particle {
	position: absolute;
	background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
	border-radius: 50%;
	animation: particleFloat infinite ease-in-out;
}

@keyframes particleFloat {
	0%, 100% { transform: translateY(0px) scale(1); opacity: 0.3; }
	25% { transform: translateY(-40px) scale(1.2); opacity: 0.8; }
	50% { transform: translateY(-20px) scale(0.8); opacity: 1; }
	75% { transform: translateY(-60px) scale(1.1); opacity: 0.6; }
}

/* 波浪效果 */
.wave-effects {
	position: absolute;
	bottom: 0;
	width: 100%;
	height: 200px;
	overflow: hidden;
}

.wave {
	position: absolute;
	bottom: 0;
	width: 200%;
	height: 100px;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
	border-radius: 50%;
	animation: waveMove infinite linear;
}

.wave-1 {
	animation-duration: 8s;
	animation-delay: 0s;
}

.wave-2 {
	animation-duration: 12s;
	animation-delay: -2s;
	opacity: 0.7;
}

.wave-3 {
	animation-duration: 16s;
	animation-delay: -4s;
	opacity: 0.5;
}

@keyframes waveMove {
	0% { transform: translateX(-50%) rotate(0deg); }
	100% { transform: translateX(-50%) rotate(360deg); }
}

/* 主内容区域 */
.main-content {
	position: relative;
	z-index: 1;
	height: 100vh;
	padding-bottom: 120rpx;
}









/* 套餐展示区域 */
.packages-showcase {
	padding: 60rpx 20rpx;
	position: relative;
}

/* 加载状态 */
.loading-section {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 120rpx 0;
}

.loading-animation {
	text-align: center;
}

.loading-orb {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	margin: 0 auto 40rpx;
}

.orb-core {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 40rpx;
	height: 40rpx;
	background: linear-gradient(135deg, #4ecdc4, #44a08d);
	border-radius: 50%;
	animation: orbPulse 2s ease-in-out infinite;
}

.orb-ring {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border: 2px solid rgba(78, 205, 196, 0.3);
	border-radius: 50%;
	animation: orbRing 3s ease-in-out infinite;
}

.ring-1 {
	width: 60rpx;
	height: 60rpx;
	animation-delay: 0s;
}

.ring-2 {
	width: 80rpx;
	height: 80rpx;
	animation-delay: 0.5s;
}

.ring-3 {
	width: 100rpx;
	height: 100rpx;
	animation-delay: 1s;
}

@keyframes orbPulse {
	0%, 100% { transform: translate(-50%, -50%) scale(1); }
	50% { transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes orbRing {
	0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
	50% { transform: translate(-50%, -50%) scale(1.3); opacity: 1; }
}

.loading-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 32rpx;
	font-weight: 500;
}

/* 套餐网格 */
.packages-grid {
	position: relative;
}

/* 区域标题 */
.section-header {
	text-align: center;
	margin-bottom: 80rpx;
}

.header-content {
	position: relative;
	display: inline-block;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 48rpx;
	font-weight: 800;
	color: white;
	text-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.title-decoration {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 16rpx;
	gap: 16rpx;
}

.deco-dot {
	width: 8rpx;
	height: 8rpx;
	background: #4ecdc4;
	border-radius: 50%;
	animation: dotPulse 2s ease-in-out infinite;
}

.deco-line {
	width: 60rpx;
	height: 2rpx;
	background: linear-gradient(90deg, transparent, #4ecdc4, transparent);
}

@keyframes dotPulse {
	0%, 100% { transform: scale(1); opacity: 0.5; }
	50% { transform: scale(1.5); opacity: 1; }
}

.section-subtitle {
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
	font-weight: 400;
	line-height: 1.6;
}

/* 紧凑卡片网格 */
.cards-grid {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	padding: 0 20rpx;
	margin-bottom: 100rpx;
}

/* 紧凑套餐卡片 */
.premium-package-card {
	position: relative;
	background: linear-gradient(135deg,
		rgba(30, 41, 59, 0.95) 0%,
		rgba(51, 65, 85, 0.95) 50%,
		rgba(71, 85, 105, 0.95) 100%);
	border-radius: 40rpx;
	padding: 0;
	border: 2rpx solid rgba(148, 163, 184, 0.3);
	overflow: visible; /* 改为visible以显示所有内容 */
	transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
	cursor: pointer;
	min-height: 400rpx; /* 增加最小高度 */
	/* 移除max-height限制，让内容自然展开 */
	box-shadow:
		0 20rpx 60rpx rgba(0, 0, 0, 0.25),
		0 8rpx 25rpx rgba(0, 0, 0, 0.15),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

.premium-package-card:hover {
	transform: translateY(-15rpx) scale(1.03);
	box-shadow:
		0 50rpx 120rpx rgba(0, 0, 0, 0.15),
		0 20rpx 50rpx rgba(0, 0, 0, 0.1),
		inset 0 2rpx 0 rgba(255, 255, 255, 0.8);
	border-color: rgba(255, 255, 255, 0.6);
}

/* 卡片光环效果 */
.card-aura {
	position: absolute;
	top: -20rpx;
	left: -20rpx;
	right: -20rpx;
	bottom: -20rpx;
	border-radius: 40rpx;
	opacity: 0;
	transition: opacity 0.5s ease;
	z-index: -1;
}

.aura-1 {
	background: radial-gradient(ellipse at center, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
}

.aura-2 {
	background: radial-gradient(ellipse at center, rgba(240, 147, 251, 0.3) 0%, transparent 70%);
}

.aura-3 {
	background: radial-gradient(ellipse at center, rgba(79, 172, 254, 0.3) 0%, transparent 70%);
}

.premium-package-card:hover .card-aura {
	opacity: 1;
}



/* 紧凑顶部装饰条 */
.top-decoration {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 10rpx 0;
	z-index: 2;
}

.deco-line {
	flex: 1;
	height: 2rpx;
	background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
}

.deco-gems {
	display: flex;
	gap: 10rpx;
	margin: 0 20rpx;
}

.gem {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	animation: gemPulse 2s ease-in-out infinite;
}

/* 紧凑标签组 */
.badge-group {
	position: absolute;
	top: 15rpx;
	right: 20rpx;
	display: flex;
	gap: 8rpx;
	z-index: 3;
}

.premium-badge {
	position: relative;
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	color: #333;
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 22rpx;
	font-weight: 700;
	display: flex;
	align-items: center;
	gap: 6rpx;
	box-shadow:
		0 4rpx 16rpx rgba(255, 215, 0, 0.4),
		0 2rpx 8rpx rgba(255, 215, 0, 0.2);
	animation: premiumPulse 2s ease-in-out infinite;
}

.hot-badge {
	position: relative;
	background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
	color: white;
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	font-size: 22rpx;
	font-weight: 700;
	display: flex;
	align-items: center;
	gap: 6rpx;
	box-shadow:
		0 4rpx 16rpx rgba(255, 107, 107, 0.4),
		0 2rpx 8rpx rgba(255, 107, 107, 0.2);
	animation: hotPulse 1.5s ease-in-out infinite;
}

/* 推荐标签脉冲动画 */
@keyframes premiumPulse {
	0%, 100% {
		transform: scale(1);
		box-shadow:
			0 4rpx 16rpx rgba(255, 215, 0, 0.4),
			0 2rpx 8rpx rgba(255, 215, 0, 0.2);
	}
	50% {
		transform: scale(1.05);
		box-shadow:
			0 6rpx 24rpx rgba(255, 215, 0, 0.6),
			0 3rpx 12rpx rgba(255, 215, 0, 0.3);
	}
}

/* 热门标签脉冲动画 */
@keyframes hotPulse {
	0%, 100% {
		transform: scale(1);
		box-shadow:
			0 4rpx 16rpx rgba(255, 107, 107, 0.4),
			0 2rpx 8rpx rgba(255, 107, 107, 0.2);
	}
	50% {
		transform: scale(1.05);
		box-shadow:
			0 6rpx 24rpx rgba(255, 107, 107, 0.6),
			0 3rpx 12rpx rgba(255, 107, 107, 0.3);
	}
}

/* 紧凑卡片主体 */
.card-body {
	position: relative;
	padding: 25rpx;
	z-index: 2;
}

/* 左右分栏布局 */
.card-layout {
	display: flex;
	gap: 20rpx;
	align-items: flex-start;
}

/* 左侧区域 - 重新设计 */
.left-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 100%;
	gap: 15rpx;
}

/* 顶部内容区域 */
.top-content {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

/* 图标标题组合 */
.icon-title-group {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

/* 标题组 */
.title-group {
	display: flex;
	flex-direction: column;
	gap: 2rpx;
	flex: 1;
}

/* 底部内容区域 */
.bottom-content {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

/* 价格区域 */
.price-section {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
	width: 100%;
}

/* 右侧区域 */
.right-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: flex-end;
}

/* 紧凑套餐头部 - 垂直排列 */
.package-header {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 5rpx;
	width: 100%;
}

.icon-container {
	position: relative;
	width: 45rpx;
	height: 45rpx;
	flex-shrink: 0;
}

.icon-backdrop {
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.package-icon {
	position: relative;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
	z-index: 2;
}

.icon-emoji {
	font-size: 24rpx;
	line-height: 1;
}

.icon-ring {
	position: absolute;
	width: 100%;
	height: 100%;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	animation: iconRotate 8s linear infinite;
}

.package-info {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 5rpx;
}

.package-name {
	font-size: 26rpx;
	font-weight: 700;
	color: #2c3e50;
	line-height: 1.3;
	margin-bottom: 2rpx;
}

.package-subtitle {
	font-size: 20rpx;
	color: #7f8c8d;
	line-height: 1.2;
	opacity: 0.8;
}

/* 紧凑价格展示区 - 垂直居左对齐 */
.price-showcase {
	display: flex;
	flex-direction: column;
	gap: 5rpx;
	align-items: flex-start;
	width: 100%;
}

.price-display {
	display: flex;
	align-items: baseline;
	gap: 4rpx;
	align-self: flex-start;
}

.currency-symbol {
	font-size: 20rpx;
	color: #4ecdc4;
	font-weight: 600;
}

.price-amount {
	font-size: 32rpx;
	color: rgba(255, 255, 255, 0.95);
	font-weight: 800;
	line-height: 1;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.price-meta {
	display: flex;
	flex-direction: column;
	gap: 3rpx;
	align-items: flex-start;
	width: 100%;
}

.validity-text {
	font-size: 16rpx;
	color: rgba(255, 255, 255, 0.6);
	text-align: left;
}

.daily-average {
	background: rgba(78, 205, 196, 0.1);
	padding: 2rpx 6rpx;
	border-radius: 6rpx;
	border: 1rpx solid rgba(78, 205, 196, 0.3);
	align-self: flex-start;
}

.average-text {
	font-size: 14rpx;
	color: #4ecdc4;
	font-weight: 600;
	text-align: left;
}

/* 新的价格样式 */
.main-price {
	display: flex;
	align-items: baseline;
	gap: 2rpx;
	margin-bottom: 6rpx;
}

.currency {
	font-size: 18rpx;
	font-weight: 600;
	color: #e74c3c;
	line-height: 1;
}

.price-value {
	font-size: 32rpx;
	font-weight: 800;
	color: #e74c3c;
	line-height: 1;
}

.price-details {
	display: flex;
	flex-direction: column;
	gap: 3rpx;
}

.validity {
	font-size: 18rpx;
	color: #7f8c8d;
	line-height: 1.2;
}

.daily-cost {
	font-size: 16rpx;
	color: #4ecdc4;
	background: rgba(78, 205, 196, 0.15);
	padding: 3rpx 8rpx;
	border-radius: 10rpx;
	align-self: flex-start;
	font-weight: 500;
	border: 1px solid rgba(78, 205, 196, 0.3);
}

/* 右侧功能特性区 */
.features-showcase {
	width: 100%;
}

.features-list {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	align-items: flex-end;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 6rpx 12rpx;
	background: rgba(78, 205, 196, 0.05);
	border-radius: 12rpx;
	border: 1rpx solid rgba(78, 205, 196, 0.2);
	min-width: 120rpx;
}

.feature-icon {
	font-size: 20rpx;
	flex-shrink: 0;
}

.feature-info {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 2rpx;
}

.feature-label {
	font-size: 18rpx;
	color: rgba(255, 255, 255, 0.7);
	font-weight: 500;
}

.feature-value {
	font-size: 20rpx;
	color: #4ecdc4;
	font-weight: 600;
}

/* 紧凑购买操作区 */
.action-section {
	margin-top: 20rpx;
	width: 100%;
}

.purchase-btn {
	position: relative;
	width: 100%;
	height: 60rpx;
	border-radius: 16rpx;
	overflow: hidden;
	cursor: pointer;
	transition: all 0.3s ease;
}

.btn-background {
	position: absolute;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.btn-premium .btn-background {
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
}

.btn-shine {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
	transition: left 0.6s ease;
}

.purchase-btn:hover .btn-shine {
	left: 100%;
}

.btn-content {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	height: 100%;
	z-index: 2;
}

.btn-text {
	font-size: 24rpx;
	color: white;
	font-weight: 600;
}

.btn-premium .btn-text {
	color: #333;
}

.btn-icon {
	transition: transform 0.3s ease;
}

.purchase-btn:hover .btn-icon {
	transform: translateX(5rpx);
}

.icon-arrow {
	font-size: 20rpx;
	color: white;
	font-weight: bold;
}

.btn-premium .icon-arrow {
	color: #333;
}

/* 动画关键帧 */
@keyframes gradientShift {
	0%, 100% { background-position: 0% 50%; }
	50% { background-position: 100% 50%; }
}

@keyframes gemPulse {
	0%, 100% { opacity: 0.6; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.2); }
}

@keyframes iconRotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

@keyframes indicatorPulse {
	0%, 100% { opacity: 0.3; transform: scale(1); }
	50% { opacity: 0.8; transform: scale(1.5); }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.premium-package-card {
		min-height: 380rpx; /* 增加最小高度 */
		/* 移除max-height限制 */
	}

	.card-body {
		padding: 25rpx; /* 保持足够的内边距 */
		gap: 18rpx; /* 调整间距 */
	}

	.package-header {
		gap: 12rpx;
		margin-bottom: 15rpx;
	}

	.icon-container {
		width: 50rpx;
		height: 50rpx;
	}

	.icon-emoji {
		font-size: 24rpx;
	}

	.package-name {
		font-size: 24rpx;
	}

	.subtitle-text {
		font-size: 18rpx;
	}

	.price-amount {
		font-size: 32rpx;
	}

	.currency-symbol {
		font-size: 20rpx;
	}

	.feature-label, .feature-value {
		font-size: 20rpx;
	}

	.btn-text {
		font-size: 22rpx;
	}

	.purchase-btn {
		height: 55rpx;
	}
}

.card-featured {
	border: 2px solid #4ecdc4;
	box-shadow: 0 0 40px rgba(78, 205, 196, 0.3);
}

.card-popular {
	border: 2px solid #ff6b6b;
	box-shadow: 0 0 40px rgba(255, 107, 107, 0.3);
}

/* 卡片背景效果 */
.card-background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.bg-mesh {
	position: absolute;
	width: 100%;
	height: 100%;
	background-image:
		linear-gradient(90deg, rgba(255,255,255,0.03) 1px, transparent 1px),
		linear-gradient(rgba(255,255,255,0.03) 1px, transparent 1px);
	background-size: 20rpx 20rpx;
}

.bg-glow {
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(78, 205, 196, 0.1) 0%, transparent 70%);
	animation: glowRotate 20s linear infinite;
}

@keyframes glowRotate {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 特色标签 */
.featured-badge {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	z-index: 10;
}

.badge-content {
	position: relative;
	background: linear-gradient(135deg, #4ecdc4, #44a08d);
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.badge-text {
	color: white;
	font-size: 22rpx;
	font-weight: 600;
	position: relative;
	z-index: 2;
}

.badge-shine {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
	animation: badgeShine 3s infinite;
}

@keyframes badgeShine {
	0% { left: -100%; }
	100% { left: 100%; }
}

/* 热门标签 */
.popular-badge {
	position: absolute;
	top: 20rpx;
	left: 20rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: linear-gradient(135deg, #ff6b6b, #ee5a24);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	z-index: 10;
}

.badge-flame {
	font-size: 20rpx;
	animation: flameFlicker 1.5s ease-in-out infinite;
}

@keyframes flameFlicker {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.2); }
}

/* 卡片内容 */
.card-content {
	position: relative;
	z-index: 2;
	padding: 40rpx;
}

/* 卡片头部 */
.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.package-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.icon-emoji {
	font-size: 32rpx;
}

.package-title {
	flex: 1;
}

.title-text {
	color: white;
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
	text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.title-underline {
	width: 40rpx;
	height: 3rpx;
	background: linear-gradient(90deg, #4ecdc4, #44a08d);
	border-radius: 2rpx;
}

/* 价格区域 */
.price-section {
	margin-bottom: 30rpx;
}

.price-main {
	display: flex;
	align-items: baseline;
	margin-bottom: 12rpx;
}

.currency {
	color: #4ecdc4;
	font-size: 28rpx;
	font-weight: 600;
	margin-right: 8rpx;
}

.price-number {
	color: white;
	font-size: 56rpx;
	font-weight: 800;
	line-height: 1;
	text-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.price-details {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.duration {
	color: rgba(255, 255, 255, 0.7);
	font-size: 24rpx;
	font-weight: 500;
}

.daily-cost {
	background: rgba(78, 205, 196, 0.15);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	border: 1px solid rgba(78, 205, 196, 0.3);
}

.cost-text {
	color: #4ecdc4;
	font-size: 22rpx;
	font-weight: 600;
}

/* 卡片主体内容 */
.card-body {
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	gap: 20rpx; /* 减少间距以容纳更多内容 */
	min-height: 350rpx; /* 确保有足够高度 */
	position: relative;
	z-index: 10; /* 确保内容在最上层 */
}

/* 顶部标题区域 */
.header-section {
	margin-bottom: 15rpx;
	width: 100%;
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
}

.title-container {
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;
	width: 100%;
}

.title-info {
	display: flex;
	flex-direction: column;
	gap: 3rpx;
	align-items: flex-start;
	width: 100%;
}

.package-title {
	color: white;
	font-size: 32rpx;
	font-weight: 600;
	line-height: 1.2;
	text-align: left;
	margin-bottom: 4rpx;
}

.package-subtitle {
	color: rgba(255,255,255,0.7);
	font-size: 24rpx;
	font-weight: 400;
	text-align: left;
}

/* 价格展示区域 */
.price-section {
	margin-bottom: 20rpx;
	padding: 20rpx 0; /* 去除背景，只保留内边距 */
	color: white;
	position: relative;
	margin-top: -10rpx; /* 价格往上一点 */
}



.price-container {
	display: flex;
	flex-direction: column; /* 改为垂直布局 */
	align-items: flex-start; /* 左对齐 */
	position: relative;
	z-index: 1;
}

.main-price {
	display: flex;
	align-items: baseline;
	gap: 4rpx;
	margin-bottom: 8rpx; /* 价格和日均成本之间的间距 */
}

.currency-symbol {
	color: rgba(255, 255, 255, 0.9);
	font-size: 32rpx;
	font-weight: 700;
}

.price-number {
	color: white;
	font-size: 56rpx;
	font-weight: 800;
	line-height: 1;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.price-info {
	display: flex;
	flex-direction: column;
	align-items: flex-start; /* 左对齐 */
	gap: 6rpx;
}

.duration-text {
	color: rgba(255, 255, 255, 0.9);
	font-size: 24rpx;
	font-weight: 600;
	background: rgba(255, 255, 255, 0.2);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	backdrop-filter: blur(10rpx);
}

.daily-cost {
	color: #4ecdc4;
	font-size: 22rpx;
	font-weight: 600;
	background: rgba(78, 205, 196, 0.15);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	border: 1px solid rgba(78, 205, 196, 0.3);
}

/* 特性展示区域 */
.features-section {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	background: rgba(248, 250, 252, 0.8);
	border-radius: 16rpx;
	padding: 20rpx;
	border: 1rpx solid rgba(226, 232, 240, 0.6);
	width: 50%;
	z-index: 10;
}

.features-container {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	gap: 15rpx;
	padding: 12rpx 16rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 12rpx;
	border: 1rpx solid rgba(226, 232, 240, 0.4);
	transition: all 0.3s ease;
}

.feature-item:hover {
	background: rgba(255, 255, 255, 1);
	transform: translateY(-2rpx);
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.feature-icon {
	font-size: 28rpx;
	width: 36rpx;
	height: 36rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	color: white;
	text-align: center;
	line-height: 1;
}

.feature-content {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.feature-label {
	color: rgba(92, 50, 132, 0.992);
	font-size: 26rpx;
	font-weight: 500;
}

.feature-value {
	color: rgba(255, 255, 255, 0.95);
	font-size: 26rpx;
	font-weight: 700;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

/* 购买按钮区域 */
.purchase-section {
	margin-top: 20rpx;
}

.purchase-button {
	position: relative;
	background: linear-gradient(135deg, #4ecdc4, #44a08d);
	border-radius: 24rpx;
	padding: 24rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	overflow: hidden;
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow:
		0 8rpx 32rpx rgba(78, 205, 196, 0.4),
		0 4rpx 16rpx rgba(78, 205, 196, 0.2),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.purchase-button.premium {
	background: linear-gradient(135deg, #667eea, #764ba2);
	box-shadow:
		0 8rpx 32rpx rgba(102, 126, 234, 0.5),
		0 4rpx 16rpx rgba(102, 126, 234, 0.3),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.purchase-button:hover {
	transform: translateY(-4rpx) scale(1.02);
	box-shadow:
		0 12rpx 48rpx rgba(78, 205, 196, 0.5),
		0 6rpx 24rpx rgba(78, 205, 196, 0.3),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
}

.purchase-button.premium:hover {
	box-shadow:
		0 12rpx 48rpx rgba(102, 126, 234, 0.6),
		0 6rpx 24rpx rgba(102, 126, 234, 0.4),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
}

.purchase-button:active {
	transform: translateY(-2rpx) scale(0.98);
}

.button-background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
	animation: buttonShine 3s infinite;
}

@keyframes buttonShine {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

.button-text {
	color: white;
	font-size: 32rpx;
	font-weight: 700;
	z-index: 2;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
	letter-spacing: 1rpx;
}

.button-icon {
	color: white;
	font-size: 28rpx;
	font-weight: bold;
	z-index: 2;
	transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.purchase-button:hover .button-icon {
	transform: translateX(6rpx) scale(1.1);
}

/* 卡片进入动画 */
@keyframes cardSlideIn {
	0% {
		opacity: 0;
		transform: translateY(50rpx) scale(0.9);
	}
	100% {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

.premium-package-card {
	animation: cardSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.premium-package-card:nth-child(1) { animation-delay: 0.1s; }
.premium-package-card:nth-child(2) { animation-delay: 0.2s; }
.premium-package-card:nth-child(3) { animation-delay: 0.3s; }

/* 特性项目动画 */
@keyframes featureSlideIn {
	0% {
		opacity: 0;
		transform: translateX(-20rpx);
	}
	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

.feature-item {
	animation: featureSlideIn 0.4s ease-out forwards;
}

.feature-item:nth-child(1) { animation-delay: 0.1s; }
.feature-item:nth-child(2) { animation-delay: 0.2s; }
.feature-item:nth-child(3) { animation-delay: 0.3s; }
.feature-item:nth-child(4) { animation-delay: 0.4s; }
.feature-item:nth-child(5) { animation-delay: 0.5s; }
.feature-item:nth-child(6) { animation-delay: 0.6s; }

.button-effects {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.button-glow {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
	animation: buttonGlow 3s infinite;
}

@keyframes buttonGlow {
	0% { left: -100%; }
	100% { left: 100%; }
}

.button-ripple {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 0;
	height: 0;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	animation: buttonRipple 2s ease-out infinite;
}

@keyframes buttonRipple {
	0% { width: 0; height: 0; opacity: 1; }
	100% { width: 200rpx; height: 200rpx; opacity: 0; }
}

/* 卡片装饰效果 */
.card-decorations {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1;
}

.decoration-dots {
	position: absolute;
	width: 100%;
	height: 100%;
}

.dot {
	position: absolute;
	width: 4rpx;
	height: 4rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	animation: dotFloat 4s ease-in-out infinite;
}

@keyframes dotFloat {
	0%, 100% { opacity: 0.3; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.5); }
}

.decoration-lines {
	position: absolute;
	width: 100%;
	height: 100%;
}

.line {
	position: absolute;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
	height: 1px;
}

.line-1 {
	top: 30%;
	left: 0;
	width: 60%;
	animation: lineMove1 6s infinite;
}

.line-2 {
	bottom: 40%;
	right: 0;
	width: 40%;
	animation: lineMove2 8s infinite reverse;
}

@keyframes lineMove1 {
	0%, 100% { opacity: 0; transform: translateX(-20rpx); }
	50% { opacity: 1; transform: translateX(0); }
}

@keyframes lineMove2 {
	0%, 100% { opacity: 0; transform: translateX(20rpx); }
	50% { opacity: 1; transform: translateX(0); }
}

/* 悬浮效果 */
.hover-effects {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 0;
}

.hover-shadow {
	position: absolute;
	bottom: -20rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 80%;
	height: 40rpx;
	background: radial-gradient(ellipse, rgba(0,0,0,0.3) 0%, transparent 70%);
	filter: blur(10rpx);
	transition: all 0.3s ease;
}

.package-card:hover .hover-shadow {
	width: 90%;
	opacity: 0.6;
}

.hover-glow {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: radial-gradient(circle at center, rgba(78, 205, 196, 0.1) 0%, transparent 70%);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.package-card:hover .hover-glow {
	opacity: 1;
}

/* 空状态 */
.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 120rpx 40rpx;
}

.empty-animation {
	text-align: center;
}

.empty-icon {
	position: relative;
	margin-bottom: 40rpx;
	display: inline-block;
}

.icon-text {
	font-size: 120rpx;
	opacity: 0.8;
	animation: iconBounce 3s ease-in-out infinite;
}

@keyframes iconBounce {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-20rpx); }
}

.icon-rings {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.ring {
	position: absolute;
	border: 2px solid rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	animation: ringPulse 3s ease-in-out infinite;
}

.ring-1 {
	width: 140rpx;
	height: 140rpx;
	top: -70rpx;
	left: -70rpx;
}

.ring-2 {
	width: 160rpx;
	height: 160rpx;
	top: -80rpx;
	left: -80rpx;
	animation-delay: 1s;
}

.ring-3 {
	width: 180rpx;
	height: 180rpx;
	top: -90rpx;
	left: -90rpx;
	animation-delay: 2s;
}

@keyframes ringPulse {
	0%, 100% { transform: scale(1); opacity: 0.3; }
	50% { transform: scale(1.1); opacity: 0.6; }
}

.empty-content {
	margin-bottom: 40rpx;
}

.empty-title {
	display: block;
	color: white;
	font-size: 36rpx;
	font-weight: 700;
	margin-bottom: 16rpx;
	text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.empty-desc {
	display: block;
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
	line-height: 1.6;
	margin-bottom: 40rpx;
}

.retry-button {
	position: relative;
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 24rpx;
	padding: 20rpx 40rpx;
	display: inline-block;
	transition: all 0.3s ease;
	overflow: hidden;
}

.retry-button:active {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(0.98);
}

.retry-text {
	color: white;
	font-size: 28rpx;
	font-weight: 600;
	position: relative;
	z-index: 2;
}

.button-ripple {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 0;
	height: 0;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	animation: retryRipple 2s ease-out infinite;
}

@keyframes retryRipple {
	0% { width: 0; height: 0; opacity: 1; }
	100% { width: 160rpx; height: 160rpx; opacity: 0; }
}



/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.cards-grid {
		grid-template-columns: 1fr;
		gap: 30rpx;
	}




}

/* 特色标签 */
.featured-badge {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	z-index: 10;
}

.badge-content {
	position: relative;
	background: linear-gradient(135deg, #4ecdc4, #44a08d);
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.badge-text {
	color: white;
	font-size: 22rpx;
	font-weight: 600;
	position: relative;
	z-index: 2;
}

.badge-shine {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
	animation: badgeShine 3s infinite;
}

@keyframes badgeShine {
	0% { left: -100%; }
	100% { left: 100%; }
}

/* 热门标签 */
.popular-badge {
	position: absolute;
	top: 20rpx;
	left: 20rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: linear-gradient(135deg, #ff6b6b, #ee5a24);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	z-index: 10;
}

.badge-flame {
	font-size: 20rpx;
	animation: flameFlicker 1.5s ease-in-out infinite;
}

@keyframes flameFlicker {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.2); }
}

/* 卡片内容 */
.card-content {
	position: relative;
	z-index: 2;
	padding: 40rpx;
}

/* 卡片头部 */
.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.package-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.icon-emoji {
	font-size: 32rpx;
}

.package-title {
	flex: 1;
}

.title-text {
	color: white;
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
	text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.title-underline {
	width: 40rpx;
	height: 3rpx;
	background: linear-gradient(90deg, #4ecdc4, #44a08d);
	border-radius: 2rpx;
}

/* 价格区域 */
.price-section {
	margin-bottom: 30rpx;
}

.price-main {
	display: flex;
	align-items: baseline;
	margin-bottom: 12rpx;
}

.currency {
	color: #4ecdc4;
	font-size: 28rpx;
	font-weight: 600;
	margin-right: 8rpx;
}

.price-number {
	color: white;
	font-size: 56rpx;
	font-weight: 800;
	line-height: 1;
	text-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.price-details {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.duration {
	color: rgba(255, 255, 255, 0.7);
	font-size: 24rpx;
	font-weight: 500;
}

.daily-cost {
	background: rgba(78, 205, 196, 0.15);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	border: 1px solid rgba(78, 205, 196, 0.3);
}

.cost-text {
	color: #4ecdc4;
	font-size: 22rpx;
	font-weight: 600;
}

/* 特色标签 */
.featured-badge {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	z-index: 10;
}

.badge-content {
	position: relative;
	background: linear-gradient(135deg, #4ecdc4, #44a08d);
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.badge-text {
	color: white;
	font-size: 22rpx;
	font-weight: 600;
	position: relative;
	z-index: 2;
}

.badge-shine {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
	animation: badgeShine 3s infinite;
}

@keyframes badgeShine {
	0% { left: -100%; }
	100% { left: 100%; }
}

/* 热门标签 */
.popular-badge {
	position: absolute;
	top: 20rpx;
	left: 20rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: linear-gradient(135deg, #ff6b6b, #ee5a24);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	z-index: 10;
}

.badge-flame {
	font-size: 20rpx;
	animation: flameFlicker 1.5s ease-in-out infinite;
}

@keyframes flameFlicker {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.2); }
}

/* 卡片内容 */
.card-content {
	position: relative;
	z-index: 2;
	padding: 40rpx;
}

/* 卡片头部 */
.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.package-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.icon-emoji {
	font-size: 32rpx;
}

.package-title {
	flex: 1;
}

.title-text {
	color: white;
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 8rpx;
	text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.title-underline {
	width: 40rpx;
	height: 3rpx;
	background: linear-gradient(90deg, #4ecdc4, #44a08d);
	border-radius: 2rpx;
}

.packages-title {
	text-align: center;
	margin-bottom: 60rpx;
}

.title-main {
	display: block;
	color: #fff;
	font-size: 48rpx;
	font-weight: 700;
	margin-bottom: 10rpx;
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.title-sub {
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
	font-weight: 400;
}

/* 3D卡片轮播 */
.cards-carousel {
	position: relative;
	height: 600rpx;
	margin-bottom: 60rpx;
}

.cards-swiper {
	width: 100%;
	height: 100%;
}

.swiper-item {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0 40rpx;
}

/* 3D卡片 */
.card-3d {
	position: relative;
	width: 320rpx;
	height: 480rpx;
	border-radius: 40rpx;
	background: linear-gradient(135deg,
		rgba(255, 255, 255, 0.15) 0%,
		rgba(255, 255, 255, 0.05) 100%);
	backdrop-filter: blur(20px);
	border: 2px solid rgba(255, 255, 255, 0.2);
	overflow: hidden;
	transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
	transform-style: preserve-3d;
	perspective: 1000px;
}

.card-3d.card-active {
	transform: scale(1.05) rotateY(0deg);
	box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4);
	z-index: 10;
}

.card-3d.card-left {
	transform: scale(0.9) rotateY(15deg) translateX(-50rpx);
	opacity: 0.7;
}

.card-3d.card-right {
	transform: scale(0.9) rotateY(-15deg) translateX(50rpx);
	opacity: 0.7;
}

/* 卡片背景装饰 */
.card-bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}

.bg-gradient {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(135deg,
		rgba(116, 75, 162, 0.3) 0%,
		rgba(102, 126, 234, 0.3) 50%,
		rgba(78, 205, 196, 0.3) 100%);
	animation: gradientShift 8s ease-in-out infinite;
}

@keyframes gradientShift {
	0%, 100% { opacity: 0.3; }
	50% { opacity: 0.6; }
}

.bg-pattern {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image:
		radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 2px, transparent 2px),
		radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 1px, transparent 1px);
	background-size: 60rpx 60rpx, 40rpx 40rpx;
}

/* 浮动元素 */
.floating-elements {
	position: absolute;
	width: 100%;
	height: 100%;
}

.float-element {
	position: absolute;
	width: 8rpx;
	height: 8rpx;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 50%;
	animation: floatMove 6s ease-in-out infinite;
}

@keyframes floatMove {
	0%, 100% { transform: translateY(0px) scale(1); opacity: 0.6; }
	50% { transform: translateY(-20rpx) scale(1.2); opacity: 1; }
}

/* 特色标签 */
.feature-tag {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	z-index: 20;
}

.tag-text {
	color: white;
	font-size: 20rpx;
	font-weight: 600;
}

/* 卡片主要内容 */
.card-main-content {
	position: relative;
	z-index: 10;
	padding: 40rpx 30rpx;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
}

/* 套餐角色 */
.package-character {
	position: relative;
	margin-bottom: 30rpx;
}

.character-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 2;
}

.character-emoji {
	font-size: 60rpx;
}

.character-effects {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.effect-ring {
	position: absolute;
	border: 2px solid rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	animation: ringPulse 3s ease-in-out infinite;
}

.ring-1 {
	width: 140rpx;
	height: 140rpx;
	top: -70rpx;
	left: -70rpx;
}

.ring-2 {
	width: 160rpx;
	height: 160rpx;
	top: -80rpx;
	left: -80rpx;
	animation-delay: 1s;
}

@keyframes ringPulse {
	0%, 100% { transform: scale(1); opacity: 0.3; }
	50% { transform: scale(1.1); opacity: 0.6; }
}

/* 套餐信息 */
.package-info {
	text-align: center;
	margin-bottom: 30rpx;
}

.package-name {
	display: block;
	color: white;
	font-size: 32rpx;
	font-weight: 700;
	margin-bottom: 10rpx;
}

.package-period {
	background: rgba(255, 255, 255, 0.1);
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.period-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 22rpx;
}

/* 价格标签 */
.price-tag {
	position: absolute;
	top: 30rpx;
	left: 30rpx;
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: baseline;
	z-index: 20;
}

.price-symbol {
	color: white;
	font-size: 20rpx;
	font-weight: 600;
	margin-right: 4rpx;
}

.price-value {
	color: white;
	font-size: 28rpx;
	font-weight: 700;
}

/* 卡片底部信息 */
.card-bottom-info {
	display: flex;
	justify-content: space-between;
	width: 100%;
	padding: 0 20rpx;
}

.quota-info, .validity-info {
	background: rgba(255, 255, 255, 0.1);
	padding: 8rpx 12rpx;
	border-radius: 12rpx;
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.quota-text, .validity-text {
	color: rgba(255, 255, 255, 0.9);
	font-size: 20rpx;
	font-weight: 500;
}

/* 3D效果层 */
.card-3d-effects {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
}

.shine-effect {
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
	transform: rotate(45deg);
	animation: cardShine 4s ease-in-out infinite;
}

@keyframes cardShine {
	0%, 100% { transform: rotate(45deg) translateX(-100%); }
	50% { transform: rotate(45deg) translateX(100%); }
}

.shadow-layer {
	position: absolute;
	bottom: -20rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 80%;
	height: 40rpx;
	background: radial-gradient(ellipse, rgba(0,0,0,0.3) 0%, transparent 70%);
	filter: blur(10rpx);
}

/* 选择按钮 */
.select-action {
	text-align: center;
	padding: 0 40rpx;
}

.action-button {
	position: relative;
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	padding: 24rpx 60rpx;
	border-radius: 50rpx;
	overflow: hidden;
	transition: all 0.3s ease;
}

.action-button:active {
	transform: scale(0.95);
}

.action-text {
	color: white;
	font-size: 32rpx;
	font-weight: 600;
	position: relative;
	z-index: 2;
}

.button-shine {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
	animation: buttonShine 3s infinite;
}

@keyframes buttonShine {
	0% { left: -100%; }
	100% { left: 100%; }
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.loading-spinner {
	position: relative;
	width: 80rpx;
	height: 80rpx;
	margin-bottom: 30rpx;
}

.spinner-ring {
	position: absolute;
	width: 100%;
	height: 100%;
	border: 4rpx solid rgba(255, 255, 255, 0.1);
	border-top: 4rpx solid #4ecdc4;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

.spinner-core {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 40rpx;
	height: 40rpx;
	background: radial-gradient(circle, #4ecdc4, transparent);
	border-radius: 50%;
	animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes pulse {
	0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(0.8); }
	50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

.loading-text {
	color: rgba(255, 255, 255, 0.7);
	font-size: 28rpx;
}

/* 套餐网格 */
.packages-grid {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 套餐卡片 */
.package-card {
	position: relative;
	background: rgba(255, 255, 255, 1);
	border-radius: 32rpx;
	padding: 40rpx;
	border: 1px solid rgba(200, 200, 200, 1);
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
}

.package-card:hover {
	transform: translateY(-8rpx);
	box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
	border-color: rgba(255, 255, 255, 0.3);
}

.package-card.featured {
	border: 2px solid #4ecdc4;
	box-shadow: 0 0 30px rgba(78, 205, 196, 0.3);
}

.package-card.premium {
	background: linear-gradient(135deg,
		rgba(255, 107, 107, 0.1) 0%,
		rgba(78, 205, 196, 0.1) 100%);
}

/* 特色标签 */
.feature-badge {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	overflow: hidden;
}

.badge-text {
	color: white;
	font-size: 22rpx;
	font-weight: 600;
	position: relative;
	z-index: 2;
}

.badge-shine {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
	animation: shine 2s infinite;
}

@keyframes shine {
	0% { left: -100%; }
	100% { left: 100%; }
}

/* 卡片内容 */
.card-content {
	position: relative;
	z-index: 2;
}

.package-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.package-title {
	color: white;
	font-size: 36rpx;
	font-weight: 700;
}

.package-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea, #764ba2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-text {
	font-size: 28rpx;
}

/* 价格区域 */
.price-section {
	margin-bottom: 30rpx;
}

.price-main {
	display: flex;
	align-items: baseline;
	margin-bottom: 10rpx;
}

.currency {
	color: #4ecdc4;
	font-size: 28rpx;
	font-weight: 600;
	margin-right: 8rpx;
}

.price-number {
	color: white;
	font-size: 56rpx;
	font-weight: 700;
	line-height: 1;
}

.price-details {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.duration {
	color: rgba(255, 255, 255, 0.7);
	font-size: 24rpx;
}

.daily-cost {
	color: #4ecdc4;
	font-size: 22rpx;
	background: rgba(78, 205, 196, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

/* 特性列表 */
.features-list {
	margin-bottom: 40rpx;
}

.feature-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.feature-icon {
	margin-right: 16rpx;
	font-size: 24rpx;
}

.feature-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 26rpx;
	line-height: 1.4;
}

/* 选择按钮 */
.select-button {
	position: relative;
	background: linear-gradient(135deg, #4ecdc4, #44a08d);
	border-radius: 24rpx;
	padding: 24rpx;
	text-align: center;
	overflow: hidden;
	transition: all 0.3s ease;
}

.select-button:active {
	transform: scale(0.98);
}

.button-text {
	color: white;
	font-size: 30rpx;
	font-weight: 600;
	position: relative;
	z-index: 2;
}

.button-glow {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
	animation: buttonShine 3s infinite;
}

@keyframes buttonShine {
	0% { left: -100%; }
	100% { left: 100%; }
}

/* 卡片装饰 */
.card-decoration {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1;
}

.deco-line {
	position: absolute;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
	height: 1px;
}

.line-1 {
	top: 30%;
	left: 0;
	width: 60%;
	animation: lineMove1 4s infinite;
}

.line-2 {
	bottom: 40%;
	right: 0;
	width: 40%;
	animation: lineMove2 5s infinite reverse;
}

@keyframes lineMove1 {
	0%, 100% { opacity: 0; transform: translateX(-20rpx); }
	50% { opacity: 1; transform: translateX(0); }
}

@keyframes lineMove2 {
	0%, 100% { opacity: 0; transform: translateX(20rpx); }
	50% { opacity: 1; transform: translateX(0); }
}

.deco-circle {
	position: absolute;
	top: 20rpx;
	left: 20rpx;
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: rgba(78, 205, 196, 0.5);
	animation: circleGlow 3s infinite;
}

@keyframes circleGlow {
	0%, 100% { opacity: 0.3; transform: scale(1); }
	50% { opacity: 1; transform: scale(1.5); }
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 100rpx 40rpx;
}

.empty-icon {
	position: relative;
	margin-bottom: 30rpx;
}

.empty-icon .icon-text {
	font-size: 120rpx;
	opacity: 0.6;
}

.icon-pulse {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 200rpx;
	height: 200rpx;
	border: 2px solid rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	animation: pulse 2s infinite;
}

.empty-title {
	display: block;
	color: white;
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 16rpx;
}

.empty-desc {
	display: block;
	color: rgba(255, 255, 255, 0.6);
	font-size: 26rpx;
	line-height: 1.5;
	margin-bottom: 40rpx;
}

.retry-button {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 24rpx;
	padding: 20rpx 40rpx;
	display: inline-block;
	transition: all 0.3s ease;
}

.retry-button:active {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(0.98);
}

.retry-text {
	color: white;
	font-size: 28rpx;
}

/* 服务保障区域 */
.guarantee-section {
	padding: 60rpx 40rpx;
	background: rgba(0, 0, 0, 0.2);
	margin: 40rpx 20rpx;
	border-radius: 32rpx;
	backdrop-filter: blur(10px);
}

.guarantee-title {
	text-align: center;
	margin-bottom: 40rpx;
}

.guarantee-title .title-text {
	color: white;
	font-size: 36rpx;
	font-weight: 600;
}

.guarantee-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 30rpx;
}

.guarantee-item {
	text-align: center;
	padding: 30rpx 20rpx;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 20rpx;
	border: 1px solid rgba(255, 255, 255, 0.1);
	transition: all 0.3s ease;
}

.guarantee-item:hover {
	background: rgba(255, 255, 255, 0.1);
	transform: translateY(-4rpx);
}

.guarantee-icon {
	display: block;
	font-size: 48rpx;
	margin-bottom: 16rpx;
}

.guarantee-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 26rpx;
}

/* 购买弹窗 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.7);
	backdrop-filter: blur(10px);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	box-sizing: border-box;
}

.purchase-modal {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 32rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	animation: modalSlideIn 0.3s ease-out;
	margin: auto;
	position: relative;
}

@keyframes modalSlideIn {
	0% {
		opacity: 0;
		transform: scale(0.9) translateY(50rpx);
	}
	100% {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-title {
	color: #333;
	font-size: 36rpx;
	font-weight: 600;
}

.close-button {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.close-button:active {
	background: rgba(0, 0, 0, 0.2);
	transform: scale(0.9);
}

.close-icon {
	color: #666;
	font-size: 40rpx;
	line-height: 1;
}

.modal-content {
	padding: 40rpx;
}

/* 套餐摘要 */
.package-summary {
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	border-radius: 24rpx;
	padding: 30rpx;
	margin-bottom: 40rpx;
}

.summary-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.summary-title {
	color: #333;
	font-size: 32rpx;
	font-weight: 600;
}

.summary-price {
	color: #4ecdc4;
	font-size: 36rpx;
	font-weight: 700;
}

.summary-details {
	border-top: 1px solid rgba(0, 0, 0, 0.1);
	padding-top: 20rpx;
}

.detail-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.detail-label {
	color: #666;
	font-size: 26rpx;
}

.detail-value {
	color: #333;
	font-size: 26rpx;
	font-weight: 500;
}

/* 支付方式 */
.payment-section {
	margin-bottom: 40rpx;
}

.payment-title {
	color: #333;
	font-size: 30rpx;
	font-weight: 600;
	margin-bottom: 20rpx;
}

.payment-option {
	display: flex;
	align-items: center;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
	border: 2px solid transparent;
	transition: all 0.3s ease;
	margin-bottom: 16rpx;
}

.payment-option.active {
	border-color: #4ecdc4;
	background: rgba(78, 205, 196, 0.1);
}

.payment-option:active {
	transform: scale(0.98);
}

.option-icon {
	font-size: 40rpx;
	margin-right: 20rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 12rpx;
}

.balance-icon {
	background: linear-gradient(135deg, #FFD700, #FFA500);
}

.wechat-icon {
	background: linear-gradient(135deg, #07C160, #00D100);
}

.wechat-pay-icon {
	color: white;
	font-size: 28rpx;
	font-weight: bold;
}

.alipay-icon {
	background: linear-gradient(135deg, #1677FF, #00A0E9);
}

.alipay-pay-icon {
	color: white;
	font-size: 28rpx;
	font-weight: bold;
}

.option-info {
	flex: 1;
}

.option-name {
	display: block;
	color: #333;
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.option-balance {
	color: #666;
	font-size: 24rpx;
}

.option-desc {
	color: #999;
	font-size: 24rpx;
}

.option-check {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	border: 2px solid #ddd;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.option-check.active {
	background: #4ecdc4;
	border-color: #4ecdc4;
}

.check-icon {
	color: white;
	font-size: 24rpx;
	font-weight: bold;
}

/* 弹窗底部 */
.modal-footer {
	display: flex;
	gap: 20rpx;
	padding: 20rpx 40rpx 40rpx;
}

.cancel-button,
.confirm-button,
.redeem-button {
	flex: 1;
	padding: 24rpx;
	border-radius: 24rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.cancel-button {
	background: #f8f9fa;
	border: 1px solid #ddd;
}

.cancel-button:active {
	background: #e9ecef;
	transform: scale(0.98);
}

.cancel-button .button-text {
	color: #666;
	font-size: 28rpx;
}

.redeem-button {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border: none;
}

.redeem-button:active {
	background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
	transform: scale(0.98);
}

.redeem-button .button-text {
	color: white;
	font-size: 28rpx;
	font-weight: 500;
}

.confirm-button {
	background: linear-gradient(135deg, #4ecdc4, #44a08d);
	position: relative;
	overflow: hidden;
}

.confirm-button:active {
	transform: scale(0.98);
}

.confirm-button.disabled {
	opacity: 0.6;
	pointer-events: none;
}

.confirm-button .button-text {
	color: white;
	font-size: 28rpx;
	font-weight: 600;
	position: relative;
	z-index: 2;
}

.button-shine {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
	animation: shine 2s infinite;
}

/* 兑换码弹窗样式 */
.redeem-modal {
	width: 680rpx;
	max-width: 90vw;
	background: white;
	border-radius: 32rpx;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
	margin: auto;
	position: relative;
}

.redeem-content {
	padding: 40rpx;
}

.input-section {
	margin-bottom: 40rpx;
}

.input-label {
	display: block;
	color: #333;
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 16rpx;
}

.redeem-input {
	width: 100%;
	height: 88rpx;
	padding: 0 24rpx;
	border: 2rpx solid #e1e5e9;
	border-radius: 16rpx;
	font-size: 28rpx;
	color: #333;
	background: #f8f9fa;
	transition: all 0.3s ease;
	box-sizing: border-box;
}

.redeem-input:focus {
	border-color: #667eea;
	background: white;
	box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.redeem-tips {
	background: #f8f9ff;
	border: 1rpx solid #e1e8ff;
	border-radius: 16rpx;
	padding: 24rpx;
}

.tips-text {
	display: block;
	color: #666;
	font-size: 24rpx;
	line-height: 1.6;
	margin-bottom: 8rpx;
}

.tips-text:last-child {
	margin-bottom: 0;
}

.redeem-footer {
	display: flex;
	gap: 20rpx;
	padding: 20rpx 40rpx 40rpx;
}

/* 套餐详情弹窗样式 */
.package-detail-modal {
	width: 680rpx;
	max-width: 90vw;
	background: white;
	border-radius: 32rpx;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
	margin: auto;
	position: relative;
}

.detail-content {
	padding: 40rpx;
	text-align: center;
}

.success-icon {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #4CAF50, #45a049);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto 30rpx;
}

.success-icon .icon {
	color: white;
	font-size: 60rpx;
	font-weight: bold;
}

.package-info {
	margin-bottom: 40rpx;
}

.package-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.package-desc {
	font-size: 28rpx;
	color: #666;
	display: block;
}

.detail-list {
	background: #f8f9fa;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.detail-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.detail-item:last-child {
	border-bottom: none;
}

.detail-label {
	font-size: 28rpx;
	color: #666;
}

.detail-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.detail-footer {
	padding: 20rpx 40rpx 40rpx;
	display: flex;
	justify-content: center;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.packages-grid {
		gap: 20rpx;
	}

	.package-card {
		padding: 30rpx;
	}

	.guarantee-grid {
		grid-template-columns: 1fr;
		gap: 20rpx;
	}

	.modal-overlay {
		padding: 20rpx;
		min-height: 100vh;
		min-height: 100dvh; /* 动态视口高度，更准确 */
	}

	.purchase-modal,
	.redeem-modal,
	.package-detail-modal {
		max-height: 85vh;
		max-height: 85dvh;
		width: 90vw;
		max-width: 600rpx;
	}
}
</style>