<template>
	<view class="floating-navigation">
		<view class="nav-bubble">
			<view
				v-for="(item, index) in navItems"
				:key="index"
				class="nav-item"
				:class="{
					active: activeIndex === index,
					clicked: clickedIndex === index
				}"
				@click="handleNavClick(index, item)"
				:ref="`navItem${index}`"
			>
				<text class="nav-icon">{{ getIconEmoji(item.icon) }}</text>
				<text class="nav-text">{{ item.text }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'MagicNavigation',
	props: {
		// 导航项配置
		items: {
			type: Array,
			default: () => [
				{ text: '初页', icon: 'icon-home', path: '/pages/index/index' },
				{ text: '会享', icon: 'icon-message', path: '/pages/chat/index' },
				{ text: '往档', icon: 'icon-history', path: '/pages/history/index' },
				{ text: '吾界', icon: 'icon-user', path: '/pages/profile/index' }
			]
		},
		// 当前激活的索引
		current: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			activeIndex: this.current,
			clickedIndex: -1, // 当前点击的索引
			screenWidth: 375 // 默认屏幕宽度
		}
	},
	computed: {
		navItems() {
			return this.items
		}
	},
	watch: {
		current(newVal) {
			this.activeIndex = newVal
		}
	},
	methods: {
		handleNavClick(index, item) {
			if (this.activeIndex === index) return

			// 创建点击旋转效果
			this.createClickEffect(index);

			// 移除所有激活状态
			setTimeout(() => {
				this.activeIndex = index;
			}, 300);

			// 触发父组件事件
			this.$emit('change', { index, item })

			// 触觉反馈
			this.hapticFeedback();

			// 如果有路径，进行页面跳转
			if (item.path) {
				uni.switchTab({
					url: item.path
				}).catch(() => {
					// 如果不是 tabBar 页面，使用 navigateTo
					uni.navigateTo({
						url: item.path
					})
				})
			}
		},

		// 点击旋转效果
		createClickEffect(index) {
			this.clickedIndex = index;
			setTimeout(() => {
				this.clickedIndex = -1;
			}, 600);
		},

		// 触觉反馈
		hapticFeedback() {
			// uniapp 中的震动反馈
			uni.vibrateShort({
				type: 'light'
			});
		},

		// 获取图标 emoji
		getIconEmoji(iconClass) {
			const iconMap = {
				'icon-home': '🏠',
				'icon-message': '💎',
				'icon-history': '📚',
				'icon-user': '👤'
			}
			return iconMap[iconClass] || '📱'
		}
	}
}
</script>

<style scoped>
/* 悬浮气泡导航 */
.floating-navigation {
	position: fixed;
	bottom: 30rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 1000;
	animation: floatUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes floatUp {
	0% {
		transform: translateX(-50%) translateY(200rpx);
		opacity: 0;
	}
	100% {
		transform: translateX(-50%) translateY(0);
		opacity: 1;
	}
}

.nav-bubble {
	background: linear-gradient(135deg, #ff6b6b, #ff8e53, #ff6348);
	backdrop-filter: blur(60rpx);
	border-radius: 60rpx;
	padding: 16rpx 24rpx;
	display: flex;
	gap: 16rpx;
	box-shadow:
		0 20rpx 60rpx rgba(0, 0, 0, 0.2),
		0 10rpx 30rpx rgba(0, 0, 0, 0.2),
		0 5rpx 15rpx rgba(0, 0, 0, 0.1),
		inset 0 2rpx 0 rgba(255, 255, 255, 0.3);
	animation: gentleFloat 6s ease-in-out infinite, navColorShift 600s ease-in-out infinite;
	position: relative;
	overflow: hidden;
	max-width: 500rpx;
	margin: 0 auto;
}

@keyframes gentleFloat {
	0%, 100% {
		transform: translateY(0rpx) scale(1);
	}
	50% {
		transform: translateY(-10rpx) scale(1.01);
	}
}

/* 导航栏缓慢颜色渐变动画 - 每5秒一个新的颜色组合，总共600秒循环 */
@keyframes navColorShift {
	0% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 50%, #fab1a0 100%); }
	0.83% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 30%, #fab1a0 60%, #ff7675 100%); }
	1.67% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 30%, #ff7675 70%, #fd79a8 100%); }
	2.5% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 40%, #fd79a8 80%, #e84393 100%); }
	3.33% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 50%, #e84393 100%); }
	4.17% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 50%, #ff6b6b 100%); }

	5% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 50%, #ff8e53 100%); }
	5.83% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 30%, #ff8e53 60%, #ff6348 100%); }
	6.67% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 30%, #ff6348 70%, #ff9ff3 100%); }
	7.5% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 40%, #ff9ff3 80%, #f368e0 100%); }
	8.33% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 50%, #f368e0 100%); }
	9.17% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 50%, #ff3838 100%); }

	10% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 50%, #ff4757 100%); }
	10.83% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 30%, #ff4757 60%, #fdcb6e 100%); }
	11.67% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 30%, #fdcb6e 70%, #e17055 100%); }
	12.5% { background: linear-gradient(135deg, #ff4757 0%, #fdcb6e 40%, #e17055 80%, #00b894 100%); }
	13.33% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 50%, #00b894 100%); }
	14.17% { background: linear-gradient(135deg, #e17055 0%, #00b894 50%, #00cec9 100%); }

	15% { background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #55a3ff 100%); }
	15.83% { background: linear-gradient(135deg, #00b894 0%, #00cec9 30%, #55a3ff 60%, #74b9ff 100%); }
	16.67% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 30%, #74b9ff 70%, #0984e3 100%); }
	17.5% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 40%, #0984e3 80%, #6c5ce7 100%); }
	18.33% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%); }
	19.17% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 50%, #a29bfe 100%); }

	20% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 50%, #ffeaa7 100%); }
	20.83% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 30%, #ffeaa7 60%, #fab1a0 100%); }
	21.67% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 30%, #fab1a0 70%, #ff7675 100%); }
	22.5% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 40%, #ff7675 80%, #fd79a8 100%); }
	23.33% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 50%, #fd79a8 100%); }
	24.17% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 50%, #e84393 100%); }

	25% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 50%, #ff6b6b 100%); }
	25.83% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 30%, #ff6b6b 60%, #ff8e53 100%); }
	26.67% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 30%, #ff8e53 70%, #ff6348 100%); }
	27.5% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 40%, #ff6348 80%, #ff9ff3 100%); }
	28.33% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 50%, #ff9ff3 100%); }
	29.17% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 50%, #f368e0 100%); }

	30% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 50%, #ff3838 100%); }
	30.83% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 30%, #ff3838 60%, #ff4757 100%); }
	31.67% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 30%, #ff4757 70%, #fdcb6e 100%); }
	32.5% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 40%, #fdcb6e 80%, #e17055 100%); }
	33.33% { background: linear-gradient(135deg, #ff4757 0%, #fdcb6e 50%, #e17055 100%); }
	34.17% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 50%, #00b894 100%); }

	35% { background: linear-gradient(135deg, #e17055 0%, #00b894 50%, #00cec9 100%); }
	35.83% { background: linear-gradient(135deg, #e17055 0%, #00b894 30%, #00cec9 60%, #55a3ff 100%); }
	36.67% { background: linear-gradient(135deg, #00b894 0%, #00cec9 30%, #55a3ff 70%, #74b9ff 100%); }
	37.5% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 40%, #74b9ff 80%, #0984e3 100%); }
	38.33% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 50%, #0984e3 100%); }
	39.17% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%); }

	40% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 50%, #a29bfe 100%); }
	40.83% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 30%, #a29bfe 60%, #ffeaa7 100%); }
	41.67% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 30%, #ffeaa7 70%, #fab1a0 100%); }
	42.5% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 40%, #fab1a0 80%, #ff7675 100%); }
	43.33% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #ff7675 100%); }
	44.17% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 50%, #fd79a8 100%); }

	45% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 50%, #e84393 100%); }
	45.83% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 30%, #e84393 60%, #ff6b6b 100%); }
	46.67% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 30%, #ff6b6b 70%, #ff8e53 100%); }
	47.5% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 40%, #ff8e53 80%, #ff6348 100%); }
	48.33% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6348 100%); }
	49.17% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 50%, #ff9ff3 100%); }

	50% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 50%, #f368e0 100%); }
	50.83% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 30%, #f368e0 60%, #ff3838 100%); }
	51.67% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 30%, #ff3838 70%, #ff4757 100%); }
	52.5% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 40%, #ff4757 80%, #fdcb6e 100%); }
	53.33% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 50%, #fdcb6e 100%); }
	54.17% { background: linear-gradient(135deg, #ff4757 0%, #fdcb6e 50%, #e17055 100%); }

	55% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 50%, #00b894 100%); }
	55.83% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 30%, #00b894 60%, #00cec9 100%); }
	56.67% { background: linear-gradient(135deg, #e17055 0%, #00b894 30%, #00cec9 70%, #55a3ff 100%); }
	57.5% { background: linear-gradient(135deg, #00b894 0%, #00cec9 40%, #55a3ff 80%, #74b9ff 100%); }
	58.33% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 50%, #74b9ff 100%); }
	59.17% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 50%, #0984e3 100%); }

	60% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%); }
	60.83% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 30%, #6c5ce7 60%, #a29bfe 100%); }
	61.67% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 30%, #a29bfe 70%, #ffeaa7 100%); }
	62.5% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 40%, #ffeaa7 80%, #fab1a0 100%); }
	63.33% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 50%, #fab1a0 100%); }
	64.17% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #ff7675 100%); }

	65% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 50%, #fd79a8 100%); }
	65.83% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 30%, #fd79a8 60%, #e84393 100%); }
	66.67% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 30%, #e84393 70%, #ff6b6b 100%); }
	67.5% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 40%, #ff6b6b 80%, #ff8e53 100%); }
	68.33% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 50%, #ff8e53 100%); }
	69.17% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6348 100%); }

	70% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 50%, #ff9ff3 100%); }
	70.83% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 30%, #ff9ff3 60%, #f368e0 100%); }
	71.67% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 30%, #f368e0 70%, #ff3838 100%); }
	72.5% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 40%, #ff3838 80%, #ff4757 100%); }
	73.33% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 50%, #ff4757 100%); }
	74.17% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 50%, #fdcb6e 100%); }

	75% { background: linear-gradient(135deg, #ff4757 0%, #fdcb6e 50%, #e17055 100%); }
	75.83% { background: linear-gradient(135deg, #ff4757 0%, #fdcb6e 30%, #e17055 60%, #00b894 100%); }
	76.67% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 30%, #00b894 70%, #00cec9 100%); }
	77.5% { background: linear-gradient(135deg, #e17055 0%, #00b894 40%, #00cec9 80%, #55a3ff 100%); }
	78.33% { background: linear-gradient(135deg, #00b894 0%, #00cec9 50%, #55a3ff 100%); }
	79.17% { background: linear-gradient(135deg, #00cec9 0%, #55a3ff 50%, #74b9ff 100%); }

	80% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 50%, #0984e3 100%); }
	80.83% { background: linear-gradient(135deg, #55a3ff 0%, #74b9ff 30%, #0984e3 60%, #6c5ce7 100%); }
	81.67% { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 30%, #6c5ce7 70%, #a29bfe 100%); }
	82.5% { background: linear-gradient(135deg, #0984e3 0%, #6c5ce7 40%, #a29bfe 80%, #ffeaa7 100%); }
	83.33% { background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 50%, #ffeaa7 100%); }
	84.17% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 50%, #fab1a0 100%); }

	85% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #ff7675 100%); }
	85.83% { background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 30%, #ff7675 60%, #fd79a8 100%); }
	86.67% { background: linear-gradient(135deg, #fab1a0 0%, #ff7675 30%, #fd79a8 70%, #e84393 100%); }
	87.5% { background: linear-gradient(135deg, #ff7675 0%, #fd79a8 40%, #e84393 80%, #ff6b6b 100%); }
	88.33% { background: linear-gradient(135deg, #fd79a8 0%, #e84393 50%, #ff6b6b 100%); }
	89.17% { background: linear-gradient(135deg, #e84393 0%, #ff6b6b 50%, #ff8e53 100%); }

	90% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 50%, #ff6348 100%); }
	90.83% { background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 30%, #ff6348 60%, #ff9ff3 100%); }
	91.67% { background: linear-gradient(135deg, #ff8e53 0%, #ff6348 30%, #ff9ff3 70%, #f368e0 100%); }
	92.5% { background: linear-gradient(135deg, #ff6348 0%, #ff9ff3 40%, #f368e0 80%, #ff3838 100%); }
	93.33% { background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 50%, #ff3838 100%); }
	94.17% { background: linear-gradient(135deg, #f368e0 0%, #ff3838 50%, #ff4757 100%); }

	95% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 50%, #fdcb6e 100%); }
	95.83% { background: linear-gradient(135deg, #ff3838 0%, #ff4757 30%, #fdcb6e 60%, #e17055 100%); }
	96.67% { background: linear-gradient(135deg, #ff4757 0%, #fdcb6e 30%, #e17055 70%, #00b894 100%); }
	97.5% { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 40%, #00b894 80%, #a29bfe 100%); }
	98.33% { background: linear-gradient(135deg, #e17055 0%, #00b894 50%, #a29bfe 100%); }
	99.17% { background: linear-gradient(135deg, #00b894 0%, #a29bfe 50%, #ffeaa7 100%); }
	100% { background: linear-gradient(135deg, #a29bfe 0%, #ffeaa7 50%, #fab1a0 100%); }
}

.nav-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 18rpx 14rpx;
	border-radius: 50%;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
	min-width: 74rpx;
	min-height: 74rpx;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(20rpx);
}

/* 点击波纹效果 */
.nav-item::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 0;
	height: 0;
	background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
	border-radius: 50%;
	transform: translate(-50%, -50%);
	transition: all 0.4s ease;
	pointer-events: none;
	z-index: 1;
}

.nav-item.clicked::before {
	width: 120rpx;
	height: 120rpx;
	opacity: 0;
}

.nav-icon {
	font-size: 32rpx;
	color: rgba(255, 255, 255, 0.9);
	transition: all 0.3s ease;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
	position: relative;
	z-index: 2;
}

.nav-text {
	font-size: 14rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 500;
	transition: all 0.3s ease;
	text-align: center;
	line-height: 1;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
	position: relative;
	z-index: 2;
	margin-top: 2rpx;
}

/* 激活状态 */
.nav-item.active {
	background: linear-gradient(135deg,
		rgba(102, 126, 234, 0.5) 0%,
		rgba(118, 75, 162, 0.5) 50%,
		rgba(240, 147, 251, 0.5) 100%);
	transform: scale(1.1);
	box-shadow:
		0 0 40rpx rgba(102, 126, 234, 0.6),
		0 10rpx 30rpx rgba(0, 0, 0, 0.3);
	border-radius: 50% !important;
}

.nav-item.active .nav-icon {
	color: white;
	transform: scale(1.1);
}

.nav-item.active .nav-text {
	color: white;
	font-weight: 600;
}

/* 点击旋转动画 */
.nav-item.clicked {
	animation: clickSpin 0.6s ease-out;
}

@keyframes clickSpin {
	0% { transform: rotate(0deg) scale(1); }
	50% { transform: rotate(180deg) scale(1.1); }
	100% { transform: rotate(360deg) scale(1); }
}

/* 悬停效果 */
.nav-item:not(.active):hover {
	background: rgba(255, 255, 255, 0.2);
	transform: scale(1.05);
	box-shadow:
		0 0 30rpx rgba(102, 126, 234, 0.3),
		0 10rpx 30rpx rgba(0, 0, 0, 0.2);
	border-radius: 50% !important;
}

.nav-item:not(.active):hover .nav-icon {
	color: white;
	transform: scale(1.15);
	filter:
		drop-shadow(0 0 20rpx rgba(255, 255, 255, 0.8))
		drop-shadow(0 6rpx 12rpx rgba(0, 0, 0, 0.3));
}

.nav-item:not(.active):hover .nav-text {
	color: white;
	text-shadow:
		0 0 16rpx rgba(255, 255, 255, 0.8),
		0 4rpx 8rpx rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.floating-navigation {
		bottom: 20rpx;
	}

	.nav-bubble {
		padding: 12rpx 20rpx;
		gap: 12rpx;
		max-width: 400rpx;
	}

	.nav-item {
		padding: 12rpx 10rpx;
		min-width: 60rpx;
		min-height: 60rpx;
	}

	.nav-icon {
		font-size: 28rpx;
	}

	.nav-text {
		font-size: 12rpx;
	}
}
</style>
