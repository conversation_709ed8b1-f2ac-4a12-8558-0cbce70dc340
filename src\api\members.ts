import { request } from './request';

/**
 * 获取所有会员列表
 */
export function getAllMembers() {
  return request({
    url: '/api/members',
    method: 'get'
  }).then(response => {
    console.log('原始会员响应:', response);
    
    // 处理不同格式的响应
    let data = [];
    
    if (response) {
      if (Array.isArray(response)) {
        data = response;
      } else if (response.data && Array.isArray(response.data)) {
        data = response.data;
      } else if (response.list && Array.isArray(response.list)) {
        data = response.list;
      } else if (response.data && response.data.list && Array.isArray(response.data.list)) {
        data = response.data.list;
      } else if (response.data && typeof response.data === 'object' && response.data !== null) {
        // 单个对象情况
        data = [response.data];
      } else if (typeof response === 'object' && response !== null) {
        // 其他对象情况
        data = [response];
      }
    }
    
    // 后处理数据，确保余额字段正确
    const processedData = data.map((member: any) => {
      if (!member) return {};
      
      // 确保余额是数值类型
      if (member.balance !== undefined) {
        const numBalance = Number(member.balance);
        if (!isNaN(numBalance)) {
          member.balance = numBalance;
        } else {
          member.balance = 0;
        }
      }
      
      return member;
    });
    
    console.log('处理后的会员数据:', processedData);
    return { data: processedData };
  }).catch(error => {
    console.error('获取会员列表失败:', error);
    return { data: [] };
  });
}

/**
 * 获取会员详情
 * @param id 会员ID
 */
export function getMemberDetail(id: number) {
  return request({
    url: `/api/members/${id}`,
    method: 'get'
  }).then(response => {
    return {
      data: response.data
    };
  });
}

/**
 * 创建会员
 * @param data 会员数据
 */
export function createMember(data: any) {
  console.log('创建会员API调用 - 数据:', data);
  
  // 移除可能存在的id字段
  const { id, ...dataWithoutId } = data;
  
  return request({
    url: '/api/members',
    method: 'post',
    data: dataWithoutId
  }).then(response => {
    console.log('创建会员响应:', response);
    
    // 处理不同格式的响应
    let responseData = null;
    
    if (response) {
      if (response.data) {
        responseData = response.data;
      } else if (typeof response === 'object') {
        responseData = response;
      }
    }
    
    return { data: responseData };
  }).catch(error => {
    console.error('创建会员API错误:', error);
    throw error;
  });
}

/**
 * 更新会员信息
 * @param id 会员ID
 * @param data 会员数据
 */
export function updateMember(id: number, data: any) {
  console.log(`更新会员API调用 - ID: ${id}, 数据:`, JSON.stringify(data));
  
  // 如果是余额数据，确保是数值类型
  if (data.balance !== undefined) {
    const numBalance = Number(data.balance);
    data.balance = isNaN(numBalance) ? 0 : numBalance;
    console.log('处理后的余额数据:', data.balance);
  }
  
  // 移除不需要的字段
  const { id: _, ...dataWithoutId } = data;
  
  // 使用会员资料更新接口
  return request({
    url: `/api/members/${id}/profile`,
    method: 'put',
    data: dataWithoutId
  }).then(response => {
    console.log('更新会员响应:', response);
    return { data: response.data || response };
  }).catch(error => {
    console.error('更新会员API错误:', error);
    throw error;
  });
}

/**
 * 删除会员
 * @param id 会员ID
 */
export function deleteMember(id: number) {
  return request({
    url: `/api/members/${id}`,
    method: 'delete'
  }).then(response => {
    return {
      data: response.data
    };
  });
}

/**
 * 获取今日注册会员数量
 */
export function getTodayRegistrations() {
  return request({
    url: '/api/members/stats/today',
    method: 'get'
  }).then(response => {
    return {
      data: response.data
    };
  });
}

/**
 * 获取活跃会员数量
 */
export function getActiveMembers() {
  return request({
    url: '/api/members/stats/active',
    method: 'get'
  }).then(response => {
    return {
      data: response.data
    };
  });
}

/**
 * 获取合伙人数量
 */
export function getPartnerCount() {
  return request({
    url: '/api/members/stats/partners',
    method: 'get'
  }).then(response => {
    return {
      data: response.data
    };
  });
}

/**
 * 搜索会员
 * @param keyword 搜索关键词
 */
export function searchMembers(keyword: string) {
  return request({
    url: '/api/members/search',
    method: 'get',
    params: { keyword }
  }).then(response => {
    return {
      data: response.data
    };
  });
}

/**
 * 更新会员资产（点数和余额）
 * @param id 会员ID
 * @param data 资产数据 { points, balance }
 */
export function updateMemberAssets(id: number, data: { points: number, balance: number }) {
  console.log(`更新会员资产API调用 - ID: ${id}, 数据:`, data);
  
  // 确保数据是数值类型
  const points = Number(data.points || 0);
  const balance = Number(data.balance || 0);
  
  // 使用专门的资产更新接口
  return request({
    url: `/api/members/${id}/assets`,
    method: 'put',
    data: {
      points,
      balance
    }
  }).then(response => {
    console.log('更新会员资产响应:', response);
    return { 
      data: response.data || response,
      success: true
    };
  }).catch(error => {
    console.error('更新会员资产API错误:', error);
    // 显示更详细的错误信息
    const errorMessage = error.response?.data?.message || error.message || '未知错误';
    console.error('错误详情:', errorMessage);
    throw new Error(`更新会员资产失败: ${errorMessage}`);
  });
}

/**
 * 调整会员点数
 * @param id 会员ID
 * @param data 调整数据 { type: 'increase' | 'decrease', amount: number, reason: string }
 */
export function adjustMemberPoints(id: number, data: { type: 'increase' | 'decrease', amount: number, reason: string }) {
  console.log(`调整会员点数API调用 - ID: ${id}, 数据:`, data);

  return request({
    url: `/api/admin/members/${id}/points/adjust`,
    method: 'post',
    data: data
  }).then(response => {
    console.log('调整会员点数响应:', response);
    return {
      data: response.data || response,
      success: true
    };
  }).catch(error => {
    console.error('调整会员点数API错误:', error);
    const errorMessage = error.response?.data?.message || error.message || '未知错误';
    console.error('错误详情:', errorMessage);
    throw new Error(`调整会员点数失败: ${errorMessage}`);
  });
}

/**
 * 调整会员余额
 * @param id 会员ID
 * @param data 调整数据 { type: 'increase' | 'decrease', amount: number, reason: string }
 */
export function adjustMemberBalance(id: number, data: { type: 'increase' | 'decrease', amount: number, reason: string }) {
  console.log(`调整会员余额API调用 - ID: ${id}, 数据:`, data);

  return request({
    url: `/api/admin/members/${id}/balance/adjust`,
    method: 'post',
    data: data
  }).then(response => {
    console.log('调整会员余额响应:', response);
    return {
      data: response.data || response,
      success: true
    };
  }).catch(error => {
    console.error('调整会员余额API错误:', error);
    const errorMessage = error.response?.data?.message || error.message || '未知错误';
    console.error('错误详情:', errorMessage);
    throw new Error(`调整会员余额失败: ${errorMessage}`);
  });
}

/**
 * 修复会员余额问题
 */
export function fixMemberBalances() {
  console.log('尝试修复会员余额问题');

  return request({
    url: '/api/fix-member-balances',
    method: 'post'
  }).then(response => {
    console.log('修复会员余额响应:', response);
    return { data: response };
  }).catch(error => {
    // 静默处理错误，不影响主流程
    console.log('修复会员余额API不存在，忽略此错误');
    return { data: { success: true, message: '会员余额已处理' } };
  });
}