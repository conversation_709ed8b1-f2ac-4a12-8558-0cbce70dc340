<template>
	<view v-if="visible && isBrowser" class="modal-overlay" @click="closeModal">
		<view class="modal-container" @click.stop>
			<!-- 顶部Logo区域 -->
			<view class="modal-header">
				<view class="logo-section">
					<view class="logo-icon">
						<image class="logo-img" src="/static/images/znt_avatar.png"></image>
					</view>
					<text class="app-title">启元AI</text>
					<text class="app-subtitle">{{ getSubtitle() }}</text>
				</view>
				<view class="close-btn" @click="closeModal">✕</view>
			</view>

			<!-- 登录表单 -->
			<view v-if="currentMode === 'login'" class="form-section">
				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">👤</text>
						<input
							class="form-input"
							type="text"
							placeholder="请输入手机号/邮箱"
							v-model="loginForm.username"
							:class="{ 'input-error': errors.username }"
						/>
					</view>
					<text v-if="errors.username" class="error-text">{{ errors.username }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input
							class="form-input"
							:type="showPassword ? 'text' : 'password'"
							placeholder="请输入密码"
							v-model="loginForm.password"
							:class="{ 'input-error': errors.password }"
						/>
						<text class="toggle-password" @click="togglePassword">
							{{ showPassword ? '👁️' : '🙈' }}
						</text>
					</view>
					<text v-if="errors.password" class="error-text">{{ errors.password }}</text>
				</view>

				<!-- 记住密码和忘记密码 -->
				<view class="form-options">
					<view class="remember-me" @click="toggleRemember">
						<text class="checkbox">{{ rememberMe ? '☑️' : '☐' }}</text>
						<text class="option-text">记住密码</text>
					</view>
					<text class="forgot-password" @click="switchMode('forgot-password')">忘记密码？</text>
				</view>

				<!-- 登录按钮 -->
				<button
					class="login-btn"
					:class="{ 'btn-loading': isLoading }"
					@click="handleLogin"
					:disabled="isLoading"
				>
					<text v-if="isLoading">登录中...</text>
					<text v-else>登录</text>
				</button>

				<!-- 注册链接 -->
				<view class="register-link">
					<text class="link-text">还没有账号？</text>
					<text class="link-action" @click="switchMode('register')">立即注册</text>
				</view>
			</view>

			<!-- 注册表单 -->
			<view v-if="currentMode === 'register'" class="form-section">
				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">{{ getContactIcon() }}</text>
						<input
							class="form-input"
							type="text"
							placeholder="请输入手机号或邮箱"
							v-model="registerForm.contact"
							:class="{ 'input-error': errors.contact }"
							@input="onContactInput"
						/>
						<text v-if="contactType" class="contact-type-indicator">{{ contactTypeText }}</text>
					</view>
					<text v-if="errors.contact" class="error-text">{{ errors.contact }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input
							class="form-input"
							:type="showPassword ? 'text' : 'password'"
							placeholder="请输入密码"
							v-model="registerForm.password"
							:class="{ 'input-error': errors.password }"
						/>
						<text class="toggle-password" @click="togglePassword">
							{{ showPassword ? '👁️' : '🙈' }}
						</text>
					</view>
					<text v-if="errors.password" class="error-text">{{ errors.password }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input
							class="form-input"
							:type="showConfirmPassword ? 'text' : 'password'"
							placeholder="请确认密码"
							v-model="registerForm.confirmPassword"
							:class="{ 'input-error': errors.confirmPassword }"
						/>
						<text class="toggle-password" @click="toggleConfirmPassword">
							{{ showConfirmPassword ? '👁️' : '🙈' }}
						</text>
					</view>
					<text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper code-wrapper">
						<text class="input-icon">🔢</text>
						<input
							class="form-input code-input"
							type="text"
							placeholder="请输入验证码"
							v-model="registerForm.verificationCode"
							:class="{ 'input-error': errors.verificationCode }"
						/>
						<button
							class="code-btn"
							:class="{ 'btn-disabled': codeSending || codeCountdown > 0 }"
							@click="sendVerificationCode"
							:disabled="codeSending || codeCountdown > 0"
						>
							<text v-if="codeCountdown > 0">{{ codeCountdown }}s</text>
							<text v-else-if="codeSending">发送中</text>
							<text v-else>获取验证码</text>
						</button>
					</view>
					<text v-if="errors.verificationCode" class="error-text">{{ errors.verificationCode }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🎫</text>
						<input
							class="form-input"
							type="text"
							placeholder="邀请码（非必填）"
							v-model="registerForm.inviteCode"
							:class="{ 'input-error': errors.inviteCode }"
						/>
					</view>
					<text v-if="errors.inviteCode" class="error-text">{{ errors.inviteCode }}</text>
				</view>

				<!-- 注册按钮 -->
				<button
					class="login-btn"
					:class="{ 'btn-loading': isLoading }"
					@click="handleRegister"
					:disabled="isLoading"
				>
					<text v-if="isLoading">注册中...</text>
					<text v-else>注册</text>
				</button>

				<!-- 登录链接 -->
				<view class="register-link">
					<text class="link-text">已有账号？</text>
					<text class="link-action" @click="switchMode('login')">立即登录</text>
				</view>
			</view>

			<!-- 忘记密码表单 -->
			<view v-if="currentMode === 'forgot-password'" class="form-section">
				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">👤</text>
						<input
							class="form-input"
							type="text"
							placeholder="请输入手机号或邮箱"
							v-model="forgotForm.account"
							:class="{ 'input-error': errors.account }"
						/>
					</view>
					<text v-if="errors.account" class="error-text">{{ errors.account }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper code-wrapper">
						<text class="input-icon">🔢</text>
						<input
							class="form-input code-input"
							type="text"
							placeholder="请输入验证码"
							v-model="forgotForm.verificationCode"
							:class="{ 'input-error': errors.verificationCode }"
						/>
						<button
							class="code-btn"
							:class="{ 'btn-disabled': codeSending || codeCountdown > 0 }"
							@click="sendForgotCode"
							:disabled="codeSending || codeCountdown > 0"
						>
							<text v-if="codeCountdown > 0">{{ codeCountdown }}s</text>
							<text v-else-if="codeSending">发送中</text>
							<text v-else>获取验证码</text>
						</button>
					</view>
					<text v-if="errors.verificationCode" class="error-text">{{ errors.verificationCode }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input
							class="form-input"
							:type="showNewPassword ? 'text' : 'password'"
							placeholder="请输入新密码"
							v-model="forgotForm.newPassword"
							:class="{ 'input-error': errors.newPassword }"
						/>
						<text class="toggle-password" @click="toggleNewPassword">
							{{ showNewPassword ? '👁️' : '🙈' }}
						</text>
					</view>
					<text v-if="errors.newPassword" class="error-text">{{ errors.newPassword }}</text>
				</view>

				<view class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input
							class="form-input"
							:type="showConfirmPassword ? 'text' : 'password'"
							placeholder="请确认新密码"
							v-model="forgotForm.confirmPassword"
							:class="{ 'input-error': errors.confirmPassword }"
						/>
						<text class="toggle-password" @click="toggleConfirmPassword">
							{{ showConfirmPassword ? '👁️' : '🙈' }}
						</text>
					</view>
					<text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
				</view>

				<!-- 重置密码按钮 -->
				<button
					class="login-btn"
					:class="{ 'btn-loading': isLoading }"
					@click="handleResetPassword"
					:disabled="isLoading"
				>
					<text v-if="isLoading">重置中...</text>
					<text v-else>重置密码</text>
				</button>

				<!-- 返回登录链接 -->
				<view class="register-link">
					<text class="link-text">想起密码了？</text>
					<text class="link-action" @click="switchMode('login')">返回登录</text>
				</view>
			</view>

			<!-- 用户协议 -->
			<view class="agreement">
				<text class="agreement-text">登录即表示同意</text>
				<text class="agreement-link" @click="showAgreement">《用户协议》</text>
				<text class="agreement-text">与</text>
				<text class="agreement-link" @click="showPrivacy">《隐私政策》</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'SmartLoginModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			environment: 'browser', // browser, wechat, miniprogram
			currentMode: 'login', // login, register, forgot-password
			loginForm: {
				username: '',
				password: ''
			},
			registerForm: {
				contact: '', // 手机号或邮箱，作为用户名
				password: '',
				confirmPassword: '',
				verificationCode: '',
				inviteCode: '' // 邀请码（非必填）
			},
			contactType: '', // 'phone' 或 'email'
			forgotForm: {
				account: '',
				verificationCode: '',
				newPassword: '',
				confirmPassword: ''
			},
			errors: {},
			showPassword: false,
			showConfirmPassword: false,
			showNewPassword: false,
			rememberMe: false,
			isLoading: false,
			codeSending: false,
			codeCountdown: 0
		}
	},
	computed: {
		isBrowser() {
			return this.environment === 'browser'
		},
		isWechat() {
			return this.environment === 'wechat'
		},
		contactTypeText() {
			switch (this.contactType) {
				case 'phone':
					return '手机号'
				case 'email':
					return '邮箱'
				case 'phone-typing':
					return '手机号...'
				case 'email-typing':
					return '邮箱...'
				default:
					return ''
			}
		},
		isMiniProgram() {
			return this.environment === 'miniprogram'
		}
	},
	mounted() {
		this.detectEnvironment()
		this.handleAutoLogin()
		this.checkInviteCode()
	},

	watch: {
		visible(newVal) {
			if (newVal) {
				this.handleAutoLogin()
			}
		}
	},
	methods: {
		// 检测运行环境
		detectEnvironment() {
			// #ifdef MP-WEIXIN
			this.environment = 'miniprogram'
			// #endif
			
			// #ifdef H5
			const ua = navigator.userAgent.toLowerCase()
			if (ua.includes('micromessenger')) {
				this.environment = 'wechat'
			} else {
				this.environment = 'browser'
			}
			// #endif
			
			// #ifdef APP-PLUS
			this.environment = 'browser'
			// #endif
		},

		// 处理自动登录
		handleAutoLogin() {
			if (this.isWechat) {
				// 微信环境自动进行微信登录
				this.wechatOfficialLogin()
			} else if (this.isMiniProgram) {
				// 小程序环境自动进行小程序登录
				this.handleMiniProgramLogin()
			}
			// 浏览器环境显示登录表单弹窗，不需要自动登录
		},

		// 关闭弹窗
		closeModal() {
			this.resetForm()
			this.$emit('close')
		},

		// 获取副标题
		getSubtitle() {
			switch (this.currentMode) {
				case 'login': return '欢迎回来'
				case 'register': return '创建新账号'
				case 'forgot-password': return '重置密码'
				default: return '欢迎回来'
			}
		},

		// 切换模式
		switchMode(mode) {
			this.currentMode = mode
			this.resetForm()
		},

		// 重置表单
		resetForm() {
			this.loginForm = {
				username: '',
				password: ''
			}
			this.registerForm = {
				contact: '',
				password: '',
				confirmPassword: '',
				verificationCode: ''
			}
			this.contactType = ''
			this.forgotForm = {
				account: '',
				verificationCode: '',
				newPassword: '',
				confirmPassword: ''
			}
			this.errors = {}
			this.showPassword = false
			this.showConfirmPassword = false
			this.showNewPassword = false
			this.isLoading = false
			this.codeSending = false
			this.codeCountdown = 0
		},

		// 微信登录
		handleWechatLogin() {
			if (this.isWechat) {
				// 微信公众号登录
				this.wechatOfficialLogin()
			} else {
				// 其他环境的微信登录
				uni.showToast({
					title: '微信登录功能开发中',
					icon: 'none'
				})
			}
		},

		// 小程序登录
		handleMiniProgramLogin() {
			// #ifdef MP-WEIXIN
			uni.login({
				provider: 'weixin',
				success: (res) => {
					console.log('小程序登录成功:', res)
					// 发送code到后端换取用户信息
					this.miniProgramAuth(res.code)
				},
				fail: (err) => {
					console.error('小程序登录失败:', err)
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'none'
					})
				}
			})
			// #endif
		},

		// 切换密码显示
		togglePassword() {
			this.showPassword = !this.showPassword
		},

		// 切换记住密码
		toggleRemember() {
			this.rememberMe = !this.rememberMe
		},

		// 切换确认密码显示
		toggleConfirmPassword() {
			this.showConfirmPassword = !this.showConfirmPassword
		},

		// 切换新密码显示
		toggleNewPassword() {
			this.showNewPassword = !this.showNewPassword
		},

		// 智能识别联系方式类型
		onContactInput() {
			const contact = this.registerForm.contact.trim()

			if (!contact) {
				this.contactType = ''
				return
			}

			// 手机号正则：1开头的11位数字
			const phoneRegex = /^1[3-9]\d{9}$/
			// 邮箱正则
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

			if (phoneRegex.test(contact)) {
				this.contactType = 'phone'
			} else if (emailRegex.test(contact)) {
				this.contactType = 'email'
			} else {
				// 判断是否可能是手机号（数字开头）
				if (/^1\d*$/.test(contact) && contact.length <= 11) {
					this.contactType = 'phone-typing'
				} else if (contact.includes('@')) {
					this.contactType = 'email-typing'
				} else {
					this.contactType = ''
				}
			}
		},

		// 获取联系方式图标
		getContactIcon() {
			switch (this.contactType) {
				case 'phone':
				case 'phone-typing':
					return '📱'
				case 'email':
				case 'email-typing':
					return '📧'
				default:
					return '📞'
			}
		},

		// 发送注册验证码
		async sendVerificationCode() {
			const contact = this.registerForm.contact.trim()

			if (!contact) {
				this.errors.contact = '请先输入手机号或邮箱'
				return
			}

			// 验证联系方式格式
			const phoneRegex = /^1[3-9]\d{9}$/
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

			if (!phoneRegex.test(contact) && !emailRegex.test(contact)) {
				this.errors.contact = '请输入正确的手机号或邮箱'
				return
			}

			this.codeSending = true
			this.errors.contact = ''

			try {
				// 调用发送验证码的API
				const { memberAPI } = await import('@/api/members.js')
				await memberAPI.sendVerificationCode(contact)

				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				})

				// 开始倒计时
				this.startCountdown()
			} catch (error) {
				console.error('发送验证码失败:', error)
				uni.showToast({
					title: error.message || '发送失败，请重试',
					icon: 'none'
				})
			} finally {
				this.codeSending = false
			}
		},

		// 发送忘记密码验证码
		async sendForgotCode() {
			if (!this.forgotForm.account) {
				this.errors.account = '请先输入手机号或邮箱'
				return
			}

			this.codeSending = true
			try {
				// 这里调用发送验证码的API
				// await memberAPI.sendForgotPasswordCode({ account: this.forgotForm.account })

				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				})

				// 开始倒计时
				this.startCountdown()
			} catch (error) {
				console.error('发送验证码失败:', error)
				uni.showToast({
					title: '发送失败，请重试',
					icon: 'none'
				})
			} finally {
				this.codeSending = false
			}
		},

		// 开始倒计时
		startCountdown() {
			this.codeCountdown = 60
			const timer = setInterval(() => {
				this.codeCountdown--
				if (this.codeCountdown <= 0) {
					clearInterval(timer)
				}
			}, 1000)
		},

		// 切换确认密码显示
		toggleConfirmPassword() {
			this.showConfirmPassword = !this.showConfirmPassword
		},

		// 切换新密码显示
		toggleNewPassword() {
			this.showNewPassword = !this.showNewPassword
		},

		// 验证表单
		validateForm() {
			this.errors = {}

			if (!this.loginForm.username.trim()) {
				this.errors.username = '请输入用户名、手机号或邮箱'
				return false
			}

			if (!this.loginForm.password.trim()) {
				this.errors.password = '请输入密码'
				return false
			}

			if (this.loginForm.password.length < 6) {
				this.errors.password = '密码长度不能少于6位'
				return false
			}

			return true
		},

		// 处理登录
		async handleLogin() {
			if (!this.validateForm()) {
				return
			}

			this.isLoading = true

			try {
				// 使用会员管理API进行登录验证
				const { memberAPI, userStore } = await import('@/api/members.js')
				const response = await memberAPI.login({
					username: this.loginForm.username.trim(),
					password: this.loginForm.password
				})

				if (response && response.success) {
					const memberData = response.data

					// 保存登录信息
					userStore.setToken(memberData.token || 'temp_token_' + Date.now())
					userStore.setCurrentUser({
						id: memberData.id,
						username: memberData.username,
						nickname: memberData.nickname || memberData.username,
						avatar: memberData.avatar || '',
						phone: memberData.phone || '',
						email: memberData.email || '',
						balance: memberData.balance || 0,
						points: memberData.points || 0,
						status: memberData.status || 'active',
						registrationTime: memberData.registrationTime,
						lastLoginTime: memberData.lastLoginTime
					})

					// 记住密码
					if (this.rememberMe) {
						this.saveCredentials()
					} else {
						this.clearCredentials()
					}

					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})

					// 关闭弹窗并通知父组件
					this.closeModal()
					this.$emit('login-success', memberData)

				} else {
					uni.showToast({
						title: response.message || '用户名或密码错误',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('登录错误:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			} finally {
				this.isLoading = false
			}
		},

		// 保存登录凭据
		saveCredentials() {
			uni.setStorageSync('rememberedCredentials', {
				username: this.loginForm.username,
				password: this.loginForm.password
			})
		},

		// 清除登录凭据
		clearCredentials() {
			uni.removeStorageSync('rememberedCredentials')
		},

		// 处理注册
		async handleRegister() {
			if (!this.validateRegisterForm()) {
				return
			}

			this.isLoading = true

			try {
				// 使用会员管理API进行注册
				const { memberAPI, userStore } = await import('@/api/members.js')

				// 判断联系方式类型
				const contact = this.registerForm.contact.trim()
				const phoneRegex = /^1[3-9]\d{9}$/
				const isPhone = phoneRegex.test(contact)

				// 准备注册数据
				const registerData = {
					name: contact, // 使用联系方式作为用户名
					password: this.registerForm.password,
					phone: isPhone ? contact : '',
					email: isPhone ? '' : contact,
					verificationCode: this.registerForm.verificationCode
				}

				// 如果有邀请码，添加到注册数据中
				if (this.registerForm.inviteCode.trim()) {
					registerData.inviteCode = this.registerForm.inviteCode.trim()
					console.log('弹窗注册时包含邀请码:', registerData.inviteCode)
				}

				const response = await memberAPI.register(registerData)

				if (response && response.success) {
					const memberData = response.data

					// 保存登录信息
					userStore.setToken(memberData.token || 'temp_token_' + Date.now())
					userStore.setCurrentUser({
						id: memberData.id,
						username: memberData.username,
						nickname: memberData.nickname || memberData.username,
						avatar: memberData.avatar || '',
						phone: memberData.phone || '',
						email: memberData.email || '',
						balance: memberData.balance || 0,
						points: memberData.points || 0,
						status: memberData.status || 'active',
						registrationTime: memberData.registrationTime,
						lastLoginTime: memberData.lastLoginTime
					})

					uni.showToast({
						title: '注册成功',
						icon: 'success'
					})

					// 关闭弹窗并通知父组件
					this.closeModal()
					this.$emit('login-success', memberData)

				} else {
					uni.showToast({
						title: response.message || '注册失败，请重试',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('注册错误:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			} finally {
				this.isLoading = false
			}
		},

		// 验证注册表单
		validateRegisterForm() {
			this.errors = {}

			const contact = this.registerForm.contact.trim()
			if (!contact) {
				this.errors.contact = '请输入手机号或邮箱'
				return false
			}

			// 验证联系方式格式
			const phoneRegex = /^1[3-9]\d{9}$/
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

			if (!phoneRegex.test(contact) && !emailRegex.test(contact)) {
				this.errors.contact = '请输入正确的手机号或邮箱'
				return false
			}

			if (!this.registerForm.password.trim()) {
				this.errors.password = '请输入密码'
				return false
			}

			if (this.registerForm.password.length < 6) {
				this.errors.password = '密码长度不能少于6位'
				return false
			}

			if (this.registerForm.password !== this.registerForm.confirmPassword) {
				this.errors.confirmPassword = '两次输入的密码不一致'
				return false
			}

			if (!this.registerForm.verificationCode.trim()) {
				this.errors.verificationCode = '请输入验证码'
				return false
			}

			// 验证邀请码（如果填写了）
			if (this.registerForm.inviteCode.trim()) {
				const inviteCodeRegex = /^[A-Z0-9]{6,20}$/
				if (!inviteCodeRegex.test(this.registerForm.inviteCode.trim())) {
					this.errors.inviteCode = '邀请码格式不正确'
					return false
				}
			}

			return true
		},

		// 处理重置密码
		async handleResetPassword() {
			if (!this.validateForgotForm()) {
				return
			}

			this.isLoading = true

			try {
				// 使用会员管理API进行密码重置
				const { memberAPI } = await import('@/api/members.js')
				const response = await memberAPI.resetPassword({
					account: this.forgotForm.account.trim(),
					verificationCode: this.forgotForm.verificationCode,
					newPassword: this.forgotForm.newPassword
				})

				if (response && response.success) {
					uni.showToast({
						title: '密码重置成功',
						icon: 'success'
					})

					// 切换到登录模式
					setTimeout(() => {
						this.switchMode('login')
					}, 1500)

				} else {
					uni.showToast({
						title: response.message || '重置失败，请重试',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('重置密码错误:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			} finally {
				this.isLoading = false
			}
		},

		// 验证忘记密码表单
		validateForgotForm() {
			this.errors = {}

			if (!this.forgotForm.account.trim()) {
				this.errors.account = '请输入手机号或邮箱'
				return false
			}

			if (!this.forgotForm.verificationCode.trim()) {
				this.errors.verificationCode = '请输入验证码'
				return false
			}

			if (!this.forgotForm.newPassword.trim()) {
				this.errors.newPassword = '请输入新密码'
				return false
			}

			if (this.forgotForm.newPassword.length < 6) {
				this.errors.newPassword = '密码长度不能少于6位'
				return false
			}

			if (this.forgotForm.newPassword !== this.forgotForm.confirmPassword) {
				this.errors.confirmPassword = '两次输入的密码不一致'
				return false
			}

			return true
		},

		// 微信公众号登录
		wechatOfficialLogin() {
			// 这里实现微信公众号OAuth登录
			uni.showToast({
				title: '微信公众号登录功能开发中',
				icon: 'none'
			})
		},

		// 小程序授权
		async miniProgramAuth(code) {
			try {
				// 发送code到后端进行用户身份验证
				// const response = await memberAPI.miniProgramLogin({ code })
				// 处理登录结果
				uni.showToast({
					title: '小程序登录功能开发中',
					icon: 'none'
				})
			} catch (error) {
				console.error('小程序授权失败:', error)
				uni.showToast({
					title: '授权失败，请重试',
					icon: 'none'
				})
			}
		},

		// 显示用户协议
		showAgreement() {
			uni.showToast({
				title: '用户协议页面开发中',
				icon: 'none'
			})
		},

		// 显示隐私政策
		showPrivacy() {
			uni.showToast({
				title: '隐私政策页面开发中',
				icon: 'none'
			})
		},

		// 检查URL参数中的邀请码
		checkInviteCode() {
			try {
				// #ifdef H5
				const urlParams = new URLSearchParams(window.location.search)
				const inviteCode = urlParams.get('inviteCode')
				if (inviteCode) {
					this.registerForm.inviteCode = inviteCode
					console.log('弹窗组件检测到邀请码:', inviteCode)
					// 如果有邀请码，自动切换到注册模式
					this.currentMode = 'register'
				}
				// #endif
			} catch (error) {
				console.error('检查邀请码失败:', error)
			}
		}
	}
}
</script>

<style scoped>
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
	padding: 40rpx;
}

.modal-container {
	background: #ffffff;
	border-radius: 24rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow-y: auto;
	position: relative;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 40rpx 20rpx;
}

.logo-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.logo-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	overflow: hidden;
	margin-bottom: 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.logo-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.app-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.app-subtitle {
	font-size: 24rpx;
	color: #666;
}

.close-btn {
	position: absolute;
	top: 30rpx;
	right: 30rpx;
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	color: #666;
	cursor: pointer;
	transition: all 0.3s ease;
}

.close-btn:hover {
	background: #e0e0e0;
}

/* 登录表单样式 */
.form-section {
	padding: 20rpx 40rpx;
}

.form-item {
	margin-bottom: 30rpx;
}

.input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.input-wrapper:focus-within {
	border-color: #667eea;
	background: #fff;
}

.input-icon {
	padding: 0 20rpx;
	font-size: 32rpx;
	color: #999;
}

.form-input {
	flex: 1;
	padding: 24rpx 20rpx;
	font-size: 32rpx;
	border: none;
	background: transparent;
	color: #333;
}

.form-input.input-error {
	border-color: #ff4757;
}

.toggle-password {
	padding: 0 20rpx;
	font-size: 32rpx;
	color: #999;
	cursor: pointer;
}

.contact-type-indicator {
	padding: 0 20rpx;
	font-size: 24rpx;
	color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 8rpx;
	margin-right: 10rpx;
}

.error-text {
	color: #ff4757;
	font-size: 24rpx;
	margin-top: 10rpx;
	margin-left: 20rpx;
}

.form-options {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.remember-me {
	display: flex;
	align-items: center;
	cursor: pointer;
}

.checkbox {
	font-size: 32rpx;
	margin-right: 10rpx;
}

.option-text {
	font-size: 28rpx;
	color: #666;
}

.forgot-password {
	font-size: 28rpx;
	color: #667eea;
	cursor: pointer;
}

.login-btn {
	width: 100%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 28rpx;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
	transition: all 0.3s ease;
}

.login-btn:active {
	transform: translateY(2rpx);
}

.login-btn.btn-loading {
	opacity: 0.7;
}

.register-link {
	text-align: center;
	margin-bottom: 20rpx;
}

.link-text {
	font-size: 28rpx;
	color: #666;
}

.link-action {
	font-size: 28rpx;
	color: #667eea;
	margin-left: 10rpx;
	cursor: pointer;
}

/* 验证码相关样式 */
.code-wrapper {
	position: relative;
}

.code-input {
	padding-right: 140rpx !important;
}

.code-btn {
	position: absolute;
	right: 10rpx;
	top: 50%;
	transform: translateY(-50%);
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 12rpx 20rpx;
	font-size: 24rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	min-width: 120rpx;
	text-align: center;
}

.code-btn:active {
	transform: translateY(-50%) scale(0.95);
}

.code-btn.btn-disabled {
	background: #ccc;
	cursor: not-allowed;
}

.code-btn.btn-disabled:active {
	transform: translateY(-50%);
}

.login-methods {
	padding: 20rpx 40rpx 40rpx;
}

.login-method {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20rpx;
	padding: 32rpx;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.login-method.primary {
	background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
	color: white;
	font-weight: 600;
}

.login-method.secondary {
	background: #f8f9fa;
	color: #333;
	border: 2rpx solid #e9ecef;
	flex: 1;
}

.login-method:active {
	transform: scale(0.98);
}

.method-icon {
	font-size: 40rpx;
}

.method-text {
	font-size: 32rpx;
}

.divider {
	text-align: center;
	margin: 40rpx 0 30rpx;
	position: relative;
}

.divider::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 0;
	right: 0;
	height: 1rpx;
	background: #e9ecef;
}

.divider-text {
	background: white;
	padding: 0 20rpx;
	font-size: 24rpx;
	color: #999;
	position: relative;
	z-index: 1;
}

.other-methods {
	display: flex;
	gap: 20rpx;
}

.agreement {
	text-align: center;
	padding: 20rpx 40rpx 40rpx;
	font-size: 24rpx;
	color: #999;
}

.agreement-link {
	color: #667eea;
	cursor: pointer;
}
</style>
