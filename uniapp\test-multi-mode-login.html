<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多模式登录弹窗测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 20px;
        }

        .test-container {
            text-align: center;
            max-width: 800px;
            width: 100%;
        }

        .test-title {
            font-size: 32px;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.95);
        }

        .test-desc {
            font-size: 16px;
            margin-bottom: 40px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
        }

        .mode-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .mode-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 30px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }

        .mode-btn:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .mode-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .mode-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .mode-desc {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }

        .features {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 30px;
            margin-top: 40px;
        }

        .features-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #4CAF50;
        }

        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            text-align: left;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .feature-icon {
            font-size: 20px;
            margin-top: 2px;
        }

        .feature-text {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }

        /* 模拟弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            padding: 20px;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal-container {
            background: white;
            border-radius: 24px;
            width: 100%;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.3s ease;
            color: #333;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 40px 40px 20px;
        }

        .logo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 20px;
        }

        .app-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .app-subtitle {
            font-size: 14px;
            color: #666;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #666;
            cursor: pointer;
            border: none;
        }

        .form-section {
            padding: 20px 40px 40px;
        }

        .form-item {
            margin-bottom: 20px;
        }

        .input-wrapper {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .input-wrapper:focus-within {
            border-color: #667eea;
            background: #fff;
        }

        .input-icon {
            padding: 0 15px;
            font-size: 18px;
            color: #999;
        }

        .form-input {
            flex: 1;
            padding: 15px 10px;
            font-size: 16px;
            border: none;
            background: transparent;
            color: #333;
            outline: none;
        }

        .toggle-password {
            padding: 0 15px;
            font-size: 18px;
            color: #999;
            cursor: pointer;
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            font-size: 14px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .remember-me input[type="checkbox"] {
            margin-right: 8px;
        }

        .forgot-password {
            color: #667eea;
            cursor: pointer;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .register-link {
            text-align: center;
            font-size: 14px;
        }

        .link-text {
            color: #666;
        }

        .link-action {
            color: #667eea;
            cursor: pointer;
            margin-left: 5px;
        }

        .code-wrapper {
            position: relative;
        }

        .code-input {
            padding-right: 120px !important;
        }

        .code-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 12px;
            cursor: pointer;
            min-width: 100px;
        }

        .code-btn.disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔐 多模式登录弹窗</h1>
        <p class="test-desc">
            在浏览器环境中，用户可以在一个弹窗内完成登录、注册、忘记密码等所有操作<br>
            点击下方按钮体验不同的登录模式
        </p>

        <div class="mode-buttons">
            <div class="mode-btn" onclick="showModal('login')">
                <div class="mode-icon">🔑</div>
                <div class="mode-title">登录模式</div>
                <div class="mode-desc">用户名密码登录</div>
            </div>

            <div class="mode-btn" onclick="showModal('register')">
                <div class="mode-icon">📝</div>
                <div class="mode-title">注册模式</div>
                <div class="mode-desc">创建新账号</div>
            </div>

            <div class="mode-btn" onclick="showModal('forgot-password')">
                <div class="mode-icon">🔄</div>
                <div class="mode-title">重置密码</div>
                <div class="mode-desc">忘记密码重置</div>
            </div>
        </div>

        <div class="features">
            <h3 class="features-title">✨ 功能特点</h3>
            <div class="features-list">
                <div class="feature-item">
                    <span class="feature-icon">🔄</span>
                    <span class="feature-text">模式切换：在弹窗内无缝切换登录、注册、重置密码</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📱</span>
                    <span class="feature-text">验证码：支持手机验证码验证，60秒倒计时</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔒</span>
                    <span class="feature-text">密码安全：密码显示切换，确认密码验证</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✅</span>
                    <span class="feature-text">表单验证：实时验证用户输入，友好错误提示</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">💾</span>
                    <span class="feature-text">记住密码：支持记住登录凭据功能</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎨</span>
                    <span class="feature-text">美观界面：现代化设计，流畅动画效果</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录弹窗 -->
    <div class="modal-overlay" id="loginModal">
        <div class="modal-container">
            <div class="modal-header">
                <div class="logo-section">
                    <div class="logo-icon">🤖</div>
                    <div class="app-title">启元AI</div>
                    <div class="app-subtitle" id="modalSubtitle">欢迎回来</div>
                </div>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>

            <div class="form-section" id="formContent">
                <!-- 动态内容将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        let currentMode = 'login';
        let codeCountdown = 0;
        let countdownTimer = null;

        const modes = {
            login: {
                subtitle: '欢迎回来',
                content: `
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">👤</span>
                            <input type="text" class="form-input" placeholder="请输入手机号/邮箱" id="username">
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">🔒</span>
                            <input type="password" class="form-input" placeholder="请输入密码" id="password">
                            <span class="toggle-password" onclick="togglePassword('password')">🙈</span>
                        </div>
                    </div>
                    <div class="form-options">
                        <label class="remember-me">
                            <input type="checkbox" id="rememberMe"> 记住密码
                        </label>
                        <span class="forgot-password" onclick="switchMode('forgot-password')">忘记密码？</span>
                    </div>
                    <button class="login-btn" onclick="handleSubmit()">登录</button>
                    <div class="register-link">
                        <span class="link-text">还没有账号？</span>
                        <span class="link-action" onclick="switchMode('register')">立即注册</span>
                    </div>
                `
            },
            register: {
                subtitle: '创建新账号',
                content: `
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">👤</span>
                            <input type="text" class="form-input" placeholder="请输入用户名" id="username">
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">📱</span>
                            <input type="tel" class="form-input" placeholder="请输入手机号" id="phone">
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">📧</span>
                            <input type="email" class="form-input" placeholder="请输入邮箱（可选）" id="email">
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">🔒</span>
                            <input type="password" class="form-input" placeholder="请输入密码" id="password">
                            <span class="toggle-password" onclick="togglePassword('password')">🙈</span>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">🔒</span>
                            <input type="password" class="form-input" placeholder="请确认密码" id="confirmPassword">
                            <span class="toggle-password" onclick="togglePassword('confirmPassword')">🙈</span>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="input-wrapper code-wrapper">
                            <span class="input-icon">🔢</span>
                            <input type="text" class="form-input code-input" placeholder="请输入验证码" id="verificationCode">
                            <button class="code-btn" onclick="sendCode()" id="codeBtn">获取验证码</button>
                        </div>
                    </div>
                    <button class="login-btn" onclick="handleSubmit()">注册</button>
                    <div class="register-link">
                        <span class="link-text">已有账号？</span>
                        <span class="link-action" onclick="switchMode('login')">立即登录</span>
                    </div>
                `
            },
            'forgot-password': {
                subtitle: '重置密码',
                content: `
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">👤</span>
                            <input type="text" class="form-input" placeholder="请输入手机号或邮箱" id="account">
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="input-wrapper code-wrapper">
                            <span class="input-icon">🔢</span>
                            <input type="text" class="form-input code-input" placeholder="请输入验证码" id="verificationCode">
                            <button class="code-btn" onclick="sendCode()" id="codeBtn">获取验证码</button>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">🔒</span>
                            <input type="password" class="form-input" placeholder="请输入新密码" id="newPassword">
                            <span class="toggle-password" onclick="togglePassword('newPassword')">🙈</span>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="input-wrapper">
                            <span class="input-icon">🔒</span>
                            <input type="password" class="form-input" placeholder="请确认新密码" id="confirmPassword">
                            <span class="toggle-password" onclick="togglePassword('confirmPassword')">🙈</span>
                        </div>
                    </div>
                    <button class="login-btn" onclick="handleSubmit()">重置密码</button>
                    <div class="register-link">
                        <span class="link-text">想起密码了？</span>
                        <span class="link-action" onclick="switchMode('login')">返回登录</span>
                    </div>
                `
            }
        };

        function showModal(mode) {
            currentMode = mode;
            switchMode(mode);
            document.getElementById('loginModal').classList.add('show');
        }

        function closeModal() {
            document.getElementById('loginModal').classList.remove('show');
            if (countdownTimer) {
                clearInterval(countdownTimer);
                countdownTimer = null;
                codeCountdown = 0;
            }
        }

        function switchMode(mode) {
            currentMode = mode;
            const modeConfig = modes[mode];
            document.getElementById('modalSubtitle').textContent = modeConfig.subtitle;
            document.getElementById('formContent').innerHTML = modeConfig.content;
        }

        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const toggle = input.nextElementSibling;
            
            if (input.type === 'password') {
                input.type = 'text';
                toggle.textContent = '👁️';
            } else {
                input.type = 'password';
                toggle.textContent = '🙈';
            }
        }

        function sendCode() {
            const codeBtn = document.getElementById('codeBtn');
            
            if (codeCountdown > 0) return;
            
            // 模拟发送验证码
            alert('验证码已发送！');
            
            // 开始倒计时
            codeCountdown = 60;
            codeBtn.textContent = `${codeCountdown}s`;
            codeBtn.classList.add('disabled');
            
            countdownTimer = setInterval(() => {
                codeCountdown--;
                if (codeCountdown > 0) {
                    codeBtn.textContent = `${codeCountdown}s`;
                } else {
                    codeBtn.textContent = '获取验证码';
                    codeBtn.classList.remove('disabled');
                    clearInterval(countdownTimer);
                    countdownTimer = null;
                }
            }, 1000);
        }

        function handleSubmit() {
            const modeNames = {
                'login': '登录',
                'register': '注册',
                'forgot-password': '重置密码'
            };
            
            alert(`${modeNames[currentMode]}成功！`);
            closeModal();
        }

        // 点击遮罩关闭弹窗
        document.getElementById('loginModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
