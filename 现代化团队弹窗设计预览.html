<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化团队弹窗设计预览 - 年轻化风格</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .demo-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* 现代化团队弹窗样式 */
        .modern-team-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(20px);
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .modern-team-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modern-team-modal-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            width: 92%;
            max-width: 400px;
            max-height: 85vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
            transform: translateY(60px) scale(0.95);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .modern-team-modal-overlay.show .modern-team-modal-card {
            transform: translateY(0) scale(1);
        }

        /* 头部样式 */
        .modern-team-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 24px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-icon-wrapper {
            width: 42px;
            height: 42px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .header-text {
            display: flex;
            flex-direction: column;
        }

        .header-title {
            font-size: 22px;
            font-weight: 600;
            color: #ffffff;
            line-height: 1.2;
        }

        .header-subtitle {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 2px;
            font-weight: 400;
        }

        .header-close {
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 9px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            color: white;
            font-size: 22px;
        }

        .header-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(0.95);
        }

        /* 内容区域 */
        .modern-team-content {
            padding: 24px 20px;
            flex: 1;
            overflow-y: auto;
        }

        /* 统计区域 */
        .stats-overview {
            margin-bottom: 30px;
        }

        .overview-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 18px;
        }

        .overview-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
        }

        .total-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 6px 12px;
            border-radius: 15px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .stats-cards-row {
            display: flex;
            gap: 15px;
        }

        .modern-stat-card {
            flex: 1;
            background: #ffffff;
            border-radius: 12px;
            padding: 18px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .modern-stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
        }

        .modern-stat-card.primary-card {
            border-left: 3px solid #667eea;
        }

        .modern-stat-card.secondary-card {
            border-left: 3px solid #764ba2;
        }

        .card-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .card-icon-bg {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 9px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .trend-indicator {
            width: 24px;
            height: 24px;
            background: #10b981;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .card-bottom {
            display: flex;
            flex-direction: column;
        }

        .card-number {
            font-size: 26px;
            font-weight: 700;
            color: #1a202c;
            line-height: 1;
            margin-bottom: 6px;
        }

        .card-label {
            font-size: 13px;
            color: #64748b;
            font-weight: 500;
        }

        /* 成员区域 */
        .members-overview {
            margin-top: 6px;
        }

        .members-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 18px;
        }

        .members-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a202c;
        }

        .view-all {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: #f8fafc;
            border-radius: 9px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            font-size: 13px;
            color: #667eea;
            font-weight: 500;
        }

        .view-all:hover {
            background: #e2e8f0;
            transform: scale(0.98);
        }

        /* 成员列表 */
        .modern-members-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .modern-member-item {
            background: #ffffff;
            border-radius: 12px;
            padding: 15px;
            border: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
        }

        .modern-member-item:hover {
            background: #f8fafc;
            transform: scale(0.98);
        }

        .member-avatar-section {
            position: relative;
            margin-right: 15px;
        }

        .member-avatar-circle {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
        }

        .level-badge {
            position: absolute;
            top: -6px;
            right: -6px;
            width: 24px;
            height: 15px;
            border-radius: 7px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #ffffff;
            font-size: 10px;
            font-weight: 600;
            color: #ffffff;
        }

        .level-badge.level-one {
            background: #10b981;
        }

        .level-badge.level-two {
            background: #f59e0b;
        }

        .member-info-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .member-name-text {
            font-size: 15px;
            font-weight: 600;
            color: #1a202c;
            line-height: 1.2;
        }

        .member-phone-text {
            font-size: 13px;
            color: #64748b;
        }

        .member-time-section {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .join-time-text {
            font-size: 11px;
            color: #94a3b8;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <button class="demo-button" onclick="showModal()">查看现代化团队弹窗</button>

    <!-- 现代化团队弹窗 -->
    <div id="teamModal" class="modern-team-modal-overlay">
        <div class="modern-team-modal-card">
            <!-- 头部区域 -->
            <div class="modern-team-header">
                <div class="header-left">
                    <div class="header-icon-wrapper">🚀</div>
                    <div class="header-text">
                        <div class="header-title">我的团队</div>
                        <div class="header-subtitle">Team Dashboard</div>
                    </div>
                </div>
                <button class="header-close" onclick="hideModal()">×</button>
            </div>

            <!-- 内容区域 -->
            <div class="modern-team-content">
                <!-- 统计概览 -->
                <div class="stats-overview">
                    <div class="overview-header">
                        <div class="overview-title">团队概览</div>
                        <div class="total-badge">156</div>
                    </div>
                    
                    <div class="stats-cards-row">
                        <div class="modern-stat-card primary-card">
                            <div class="card-top">
                                <div class="card-icon-bg">👥</div>
                                <div class="trend-indicator">↗</div>
                            </div>
                            <div class="card-bottom">
                                <div class="card-number">89</div>
                                <div class="card-label">直推成员</div>
                            </div>
                        </div>

                        <div class="modern-stat-card secondary-card">
                            <div class="card-top">
                                <div class="card-icon-bg">🌐</div>
                                <div class="trend-indicator">↗</div>
                            </div>
                            <div class="card-bottom">
                                <div class="card-number">67</div>
                                <div class="card-label">间推成员</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 成员列表 -->
                <div class="members-overview">
                    <div class="members-header">
                        <div class="members-title">最新成员</div>
                        <button class="view-all">
                            查看全部 →
                        </button>
                    </div>

                    <div class="modern-members-list">
                        <div class="modern-member-item">
                            <div class="member-avatar-section">
                                <div class="member-avatar-circle">张</div>
                                <div class="level-badge level-one">L1</div>
                            </div>
                            <div class="member-info-section">
                                <div class="member-name-text">张小明</div>
                                <div class="member-phone-text">138****1234</div>
                            </div>
                            <div class="member-time-section">
                                <div class="join-time-text">2小时前</div>
                            </div>
                        </div>

                        <div class="modern-member-item">
                            <div class="member-avatar-section">
                                <div class="member-avatar-circle">李</div>
                                <div class="level-badge level-two">L2</div>
                            </div>
                            <div class="member-info-section">
                                <div class="member-name-text">李小红</div>
                                <div class="member-phone-text">139****5678</div>
                            </div>
                            <div class="member-time-section">
                                <div class="join-time-text">1天前</div>
                            </div>
                        </div>

                        <div class="modern-member-item">
                            <div class="member-avatar-section">
                                <div class="member-avatar-circle">王</div>
                                <div class="level-badge level-one">L1</div>
                            </div>
                            <div class="member-info-section">
                                <div class="member-name-text">王小强</div>
                                <div class="member-phone-text">136****9012</div>
                            </div>
                            <div class="member-time-section">
                                <div class="join-time-text">3天前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showModal() {
            document.getElementById('teamModal').classList.add('show');
        }

        function hideModal() {
            document.getElementById('teamModal').classList.remove('show');
        }

        // 点击遮罩关闭
        document.getElementById('teamModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });
    </script>
</body>
</html>
